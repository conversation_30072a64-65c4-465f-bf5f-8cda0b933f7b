# Windows Battery Data Collection Script
# Run this in PowerShell (Admin not required for WMI queries)
# Works on HP, Dell, Lenovo, and all Windows laptops

Write-Host "=== WINDOWS BATTERY DATA COLLECTION ===" -ForegroundColor Cyan
Write-Host "Collecting battery information..." -ForegroundColor Yellow
Write-Host ""

# Initialize variables
$designCapacity = $null
$fullChargeCapacity = $null

# Method 1: Basic Battery Info (often incomplete)
Write-Host "1. BASIC BATTERY STATUS (Win32_Battery)" -ForegroundColor Green
try {
    $battery = Get-WmiObject Win32_Battery -ErrorAction Stop
    if ($battery) {
        Write-Host "   Status: $($battery.Status)"
        Write-Host "   Battery Status Code: $($battery.BatteryStatus) (1=Other, 2=Unknown, 3=FullyCharged, 4=Low, 5=Critical, 6=Charging)"
        Write-Host "   Charge Remaining: $($battery.EstimatedChargeRemaining)%"
        Write-Host "   Time Remaining: $($battery.EstimatedRunTime) minutes"
        Write-Host "   Design Capacity: $($battery.DesignCapacity) mWh (often null)"
        Write-Host "   Full Charge Capacity: $($battery.FullChargeCapacity) mWh (often null)"
        Write-Host "   Chemistry: $($battery.Chemistry)"
        Write-Host "   Device ID: $($battery.DeviceID)"
    } else {
        Write-Host "   No battery detected" -ForegroundColor Red
    }
} catch {
    Write-Host "   Error accessing Win32_Battery: $_" -ForegroundColor Red
}
Write-Host ""

# Method 2: Reliable Battery Data from root\WMI
Write-Host "2. DETAILED BATTERY DATA (root\WMI)" -ForegroundColor Green

# Design Capacity
try {
    $designCap = Get-WmiObject -Namespace root\WMI -Class BatteryStaticData -ErrorAction Stop
    if ($designCap -and $designCap.DesignedCapacity) {
        $designCapacity = $designCap.DesignedCapacity
        Write-Host "   Design Capacity: $designCapacity mWh"
    } else {
        Write-Host "   Design Capacity: Not available" -ForegroundColor Yellow
    }
} catch {
    Write-Host "   Design Capacity: Error - $_" -ForegroundColor Yellow
}

# Full Charge Capacity
try {
    $fullChargeCap = Get-WmiObject -Namespace root\WMI -Class BatteryFullChargedCapacity -ErrorAction Stop
    if ($fullChargeCap -and $fullChargeCap.FullChargedCapacity) {
        $fullChargeCapacity = $fullChargeCap.FullChargedCapacity
        Write-Host "   Full Charge Capacity: $fullChargeCapacity mWh"
    } else {
        Write-Host "   Full Charge Capacity: Not available" -ForegroundColor Yellow
    }
} catch {
    Write-Host "   Full Charge Capacity: Error - $_" -ForegroundColor Yellow
}

# Cycle Count
try {
    $cycleCount = Get-WmiObject -Namespace root\WMI -Class BatteryCycleCount -ErrorAction Stop
    if ($cycleCount -and $cycleCount.CycleCount) {
        Write-Host "   Cycle Count: $($cycleCount.CycleCount)"
    } else {
        Write-Host "   Cycle Count: Not available" -ForegroundColor Yellow
    }
} catch {
    Write-Host "   Cycle Count: Error - $_" -ForegroundColor Yellow
}

# Additional Battery Info
try {
    $batteryRuntime = Get-WmiObject -Namespace root\WMI -Class BatteryRuntime -ErrorAction Stop
    if ($batteryRuntime) {
        Write-Host "   Runtime to Empty: $($batteryRuntime.RuntimeToEmpty) minutes"
    }
} catch {
    # Silent fail - not critical
}

# Calculate Battery Health
if ($designCapacity -and $fullChargeCapacity -and $designCapacity -gt 0) {
    $healthPercent = [math]::Round(($fullChargeCapacity / $designCapacity) * 100, 2)
    $degradedPercent = [math]::Round(100 - $healthPercent, 2)
    
    Write-Host ""
    Write-Host "3. BATTERY HEALTH ANALYSIS" -ForegroundColor Green
    Write-Host "   Battery Health: $healthPercent%"
    Write-Host "   Battery Degraded: $degradedPercent%"
    
    # Health Status
    if ($healthPercent -ge 80) {
        Write-Host "   Status: GOOD (No replacement needed)" -ForegroundColor Green
    } elseif ($healthPercent -ge 50) {
        Write-Host "   Status: FAIR (Monitor closely)" -ForegroundColor Yellow
    } else {
        Write-Host "   Status: POOR (Replacement recommended)" -ForegroundColor Red
    }
}
Write-Host ""

# Method 3: System Information
Write-Host "4. SYSTEM INFORMATION" -ForegroundColor Green
$computer = Get-WmiObject Win32_ComputerSystem
$bios = Get-WmiObject Win32_BIOS
Write-Host "   Manufacturer: $($computer.Manufacturer)"
Write-Host "   Model: $($computer.Model)"
Write-Host "   Serial Number: $($bios.SerialNumber)"
Write-Host ""

# Method 4: PowerCfg Report
Write-Host "5. GENERATING FULL BATTERY REPORT" -ForegroundColor Green
$reportPath = "$env:USERPROFILE\battery-report.html"
try {
    $result = powercfg /batteryreport /output $reportPath 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "   Report saved to: $reportPath" -ForegroundColor Cyan
        Write-Host "   Opening report in browser..."
        Start-Process $reportPath -ErrorAction SilentlyContinue
    } else {
        Write-Host "   Error generating report: $result" -ForegroundColor Red
    }
} catch {
    Write-Host "   Error running powercfg: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== DATA COLLECTION COMPLETE ===" -ForegroundColor Cyan
Write-Host ""
Write-Host "SUMMARY:" -ForegroundColor Yellow
Write-Host "- Battery health calculation is universal across all brands"
Write-Host "- HP, Dell, and Lenovo all use the same Windows battery APIs"
Write-Host "- The HTML report provides the most comprehensive data"
Write-Host ""
Write-Host "Press any key to exit..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")