﻿<!DOCTYPE html>
<!-- saved from url=(0016)http://localhost -->
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:ms="urn:schemas-microsoft-com:xslt" xmlns:bat="http://schemas.microsoft.com/battery/2012" xmlns:js="http://microsoft.com/kernel"><head><meta http-equiv="X-UA-Compatible" content="IE=edge"/><meta name="ReportUtcOffset" content="+5:30"/><title>Battery report</title><style type="text/css">

      body {

          font-family: Segoe UI Light;

          letter-spacing: 0.02em;

          background-color: #181818;

          color: #F0F0F0;

          margin-left: 5.5em;

      }



      h1 {

          color: #11D8E8;

          font-size: 42pt;

      }



      h2 {

          font-size: 15pt;

          color: #11EEF4;

          margin-top: 4em;

          margin-bottom: 0em;

          letter-spacing: 0.08em;

      }



      td {

          padding-left: 0.3em;

          padding-right: 0.3em;

      }



      .nobatts {

          font-family: Segoe UI Semibold;

          background: #272727;

          color: #ACAC60;

          font-size: 13pt;

          padding-left:0.4em;

          padding-right:0.4em;

          padding-top:0.3em;

          padding-bottom:0.3em;

      }



      .explanation {

          color: #777777;

          font-size: 12pt;

          margin-bottom: 1em;

      }



      .explanation2 {

          color: #777777;

          font-size: 12pt;

          margin-bottom: 0.1em;

      }



      table {

          border-width: 0;

          table-layout: fixed;

          font-family: Segoe UI Light;

          letter-spacing: 0.02em;

          background-color: #181818;

          color: #f0f0f0;

      }



      .even { background: #272727; }

      .odd { background: #1E1E1E; }

      .even.suspend { background: #1A1A28; }

      .odd.suspend { background: #1A1A2C; }



      thead {

          font-family: Segoe UI Semibold;

          font-size: 85%;

          color: #BCBCBC;

      }



      text {

          font-size: 12pt;

          font-family: Segoe UI Light;

          fill: #11EEF4;

      }



      .centered { text-align: center; }



      .label {

          font-family: Segoe UI Semibold;

          font-size: 85%;

          color: #BCBCBC;

      }



      .dc.even { background: #40182C; }

      .dc.odd { background: #30141F; }



      td.colBreak {

          padding: 0;

          width: 0.15em;

      }



      td.state { text-align: center; }



      td.hms {

          font-family: Segoe UI Symbol;

          text-align: right;

          padding-right: 3.4em;

      }



      td.dateTime { font-family: Segoe UI Symbol; }

      td.nullValue { text-align: center; }



      td.percent {

          font-family: Segoe UI Symbol;

          text-align: right;

          padding-right: 2.5em;

      }



      col:first-child { width: 13em; }

      col.col2 { width: 10.4em; }

      col.percent { width: 7.5em; }



      td.mw {

          text-align: right;

          padding-right: 2.5em;

      }



      td.acdc { text-align: center; }



      span.date {

          display: inline-block;

          width: 5.5em;

      }



      span.time {

          text-align: right;

          width: 4.2em;

          display: inline-block;

      }



      text { font-family: Segoe UI Symbol; }



      .noncontigbreak {

          height: 0.3em;

          background-color: #1A1A28;

      }

    </style><script type="text/javascript">

    // Formats a number using the current locale (to handle the 1000's separator).

    // The result is rounded so no decimal point is shown.

    function numberToLocaleString(value) {

        var localeString = Math.round(parseFloat(value + '')).toLocaleString();

        return localeString.substring(0, localeString.indexOf('.'));

    }



    function padLeft(number, length) {

        var str = '' + number;

        while (str.length < length) {

            str = '0' + str;

        }



        return str;

    }



    // Returns the number of milliseconds between 2 date-times represented as strings.

    function msBetween(startTime, endTime) {

        return startTime > endTime

               ? msBetween(endTime, startTime)

               : parseDateTime(endTime) - parseDateTime(startTime);

    }



    var dateFormat = /(\d{4})-(\d{2})-(\d{2})[T](\d{2}):(\d{2}):(\d{2})/



    // Parses a date-time string and returns a Date (i.e. number of milliseconds)

    function parseDateTime(value) {

        if (!value) {

            return 0;

        }



        var match = dateFormat.exec(value)

        if (!match) {

            return 0;

        }



        return Date.parse(match[1] + '/' + match[2] + '/' +

                          match[3] + ' ' + match[4] + ':' +

                          match[5] + ':' + match[6])

    }



    // Parses just the date portion of a date-time string and returns a Date

    // (i.e. number of milliseconds)

    function parseDate(value) {

        if (!value) {

            return 0;

        }



        var match = dateFormat.exec(value)

        if (!match) {

            return 0;

        }



        return Date.parse(match[1] + '/' + match[2] + '/' + match[3])

    }



    var durationFormat = /P((\d+)D)?T((\d+)H)?((\d+)M)?(\d+)S/



    // Convert a string of the form P10DT1H15M40S to a count of milliseconds

    function parseDurationToMs(value) {

        var match = durationFormat.exec(value)

        if (!match) {

            return 0

        }



        var days = parseInt(match[2] || '0');

        var hrs = parseInt(match[4] || '0');

        var mins = parseInt(match[6] || '0');

        var secs = parseInt(match[7] || '0');

        return ((((((days * 24) + hrs) * 60) + mins) * 60) +  secs) * 1000;

    }



    // Converts milliseconds to days

    function msToDays(ms) {

        return (ms / 1000 / 60 / 60 / 24);

    }



    function daysToMs(days) {

        return (days * 24 * 60 * 60 * 1000);

    }



    // Formats a number of milliseconds as h:mm:ss

    function formatDurationMs(value) {

        var ms = parseInt(value);

        var secs = ms / 1000;

        var mins = secs / 60;

        var hrs = Math.floor(mins / 60);

        mins = Math.floor(mins % 60);

        secs = Math.floor(secs % 60);

        return hrs + ':' + padLeft(mins,2) + ':' + padLeft(secs,2);

    }



    // Converts a millisecond timestamp to a day and month string

    // Note: dayOffset is forward from date.

    function dateToDayAndMonth(ms, dayOffset) {

        var adjustedDate = new Date(ms + (dayOffset * 24 * 60 * 60 * 1000));

        return padLeft(adjustedDate.getMonth() + 1, 2) + "-" +

               padLeft(adjustedDate.getDate(), 2);

    }



    // Takes a millisecond timestamp and returns a new millisecond timestamp

    // rounded down to the current day.

    function dateFloor(ms) {

        var dt = new Date(ms);

        return Date.parse(dt.getFullYear() + '/' + (dt.getMonth() + 1) + '/' + dt.getDate());

    }

    

    Timegraph = {

        axisTop: 9.5,

        axisRight: 24.5,

        axisBottom: 25.5,

        axisLeft: 25.5,

        ticks: 10,



        // Maximum number of 24 hour ticks for showing 12 and 6 hour ticks



        ticks12Hour: 8,

        ticks6Hour: 4,



        // Shading



        lineColor: "#B82830",

        shadingColor: "#4d1d35",



        precompute: function (graph) {

            var canvas = graph.canvas;

            var data = graph.data;

            var min = 0;

            var max = 0;



            graph.height = canvas.height - Timegraph.axisTop - Timegraph.axisBottom;

            graph.width = canvas.width - Timegraph.axisLeft - Timegraph.axisRight;

            for (var i = 0; i < data.length; i++) {

                data[i].t0 = parseDateTime(data[i].x0);

                data[i].t1 = parseDateTime(data[i].x1);



                if (i == 0) {

                    min = data[i].t0;

                    max = data[i].t1;

                }



                if (data[i].t0 < min) {

                    min = data[i].t0;

                }



                if (data[i].t1 > max) {

                    max = data[i].t1;

                }



                data[i].yy0 =

                    Timegraph.axisTop + graph.height - data[i].y0 * graph.height;



                data[i].yy1 =

                    Timegraph.axisTop + graph.height - data[i].y1 * graph.height;

            }



            if (graph.startTime != null) {

                graph.startMs = parseDateTime(graph.startTime);



            } else {

                graph.startMs = min;

            }



            graph.endMs = max;

            graph.durationMs = max - min;

        },



        drawFrame: function (graph) {

            var canvas = graph.canvas;

            var context = graph.context;



            graph.width =

                canvas.width - Timegraph.axisRight - Timegraph.axisLeft;



            graph.height =

                canvas.height - Timegraph.axisTop - Timegraph.axisBottom;



            context.beginPath();

            context.moveTo(Timegraph.axisLeft, Timegraph.axisTop);

            context.lineTo(Timegraph.axisLeft + graph.width,

                           Timegraph.axisTop);



            context.lineTo(Timegraph.axisLeft + graph.width,

                           Timegraph.axisTop + graph.height);



            context.lineTo(Timegraph.axisLeft,

                           Timegraph.axisTop + graph.height);



            context.lineTo(Timegraph.axisLeft, Timegraph.axisTop);

            context.strokeStyle = "#c0c0c0";

            context.stroke();

        },



        drawRange: function (graph) {

            var canvas = graph.canvas;

            var context = graph.context;



            context.font = "12pt Segoe UI";

            context.fillStyle = "#00b0f0";

            context.fillText("%", 0, Timegraph.axisTop + 5, Timegraph.axisLeft);



            var tickSpacing = graph.height / 10;

            var offset = Timegraph.axisTop + tickSpacing;

            var tickValue = 90;

            for (var i = 0; i < 9; i++) {

                context.beginPath();

                context.moveTo(Timegraph.axisLeft, offset);

                context.lineTo(Timegraph.axisLeft + graph.width,

                               offset);



                context.stroke();

                context.fillText(tickValue.toString(),

                                 0,

                                 offset + 5,

                                 Timegraph.axisLeft);



                offset += tickSpacing;

                tickValue -= 10;

            }

        },



        drawDomain: function (graph, start, end) {

            var canvas = graph.canvas;

            var context = graph.context;

            var data = graph.data;

            var duration = end - start;

            if ((end < start)) {

                return;

            }



            var startDay = dateFloor(start);

            var t0 = startDay;

            var t1 = dateFloor(end);

            var dayOffset = 0;

            if (start > t0) {

                t0 = t0 + daysToMs(1);

                dayOffset++;

            }



            if (t0 >= t1) {

                return;

            }



            var increment =

                Math.max(Math.floor((t1 - t0) / daysToMs(Timegraph.ticks)), 1);



            var incrementMs = daysToMs(increment);

            var spacing = (incrementMs / duration) * graph.width;

            var offset = (t0 - start) / duration;

            var ticksCount = Math.floor((t1 - t0) / incrementMs);

            for (offset = offset * graph.width + Timegraph.axisLeft;

                 offset < (graph.width + Timegraph.axisLeft);

                 offset += spacing) {



                context.beginPath();

                context.moveTo(offset, Timegraph.axisTop);

                context.lineTo(offset, Timegraph.axisTop + graph.height);

                context.stroke();

                context.fillText(dateToDayAndMonth(startDay, dayOffset),

                                 offset,

                                 Timegraph.axisTop + graph.height + 15,

                                 spacing);



                dayOffset += increment;

            }

        },



        plot: function (graph, start, end) {

            var canvas = graph.canvas;

            var context = graph.context

            var data = graph.data;



            if ((end < start)) {

                return;

            }



            var duration = end - start;

            Timegraph.drawDomain(graph, start, end);

            context.fillStyle = Timegraph.shadingColor;

            for (var i = 0; i < data.length - 1; i++) {

                if ((data[i].t0 < start) || (data[i].t0 > end) ||

                    (data[i].t1 > end)) {



                    continue;

                }



                var x1 = (data[i].t0 - start) / duration;

                x1 = x1 * graph.width + Timegraph.axisLeft;



                var x2 = (data[i].t1 - start) / duration;

                x2 = x2 * graph.width + Timegraph.axisLeft;



                context.globalAlpha = 0.3;

                context.fillRect(x1, Timegraph.axisTop, (x2 - x1), graph.height);

                context.globalAlpha = 1;

                context.beginPath();

                context.strokeStyle = Timegraph.lineColor;

                context.lineWidth = 1.5;

                context.moveTo(x1, data[i].yy0);

                context.lineTo(x2, data[i].yy1);

                context.stroke();

            }

        },



        draw: function (graph) {

            var canvas = document.getElementById(graph.element);

            if (canvas == null) {

                return;

            }



            var context = canvas.getContext('2d');

            if (context == null) {

                return;

            }



            graph.width = 0;

            graph.height = 0;

            graph.context = context;

            graph.canvas = canvas;



            Timegraph.precompute(graph);

            Timegraph.drawFrame(graph);

            Timegraph.drawRange(graph);

            Timegraph.plot(graph, graph.startMs, graph.endMs);

        }

    };

    

    drainGraphData = [

    { x0: "2025-06-14T11:44:13", x1: "2025-06-14T11:44:13", y0: 0.8894472361809045, y1: 0.8894472361809045 }, 

{ x0: "2025-06-14T11:44:13", x1: "2025-06-14T12:43:05", y0: 0.8894472361809045, y1: 0.27790739411342424 }, 

{ x0: "2025-06-14T12:43:05", x1: "2025-06-14T12:44:00", y0: 0.27790739411342424, y1: 0.2728822684852836 }, 

{ x0: "2025-06-14T12:44:00", x1: "2025-06-14T12:45:26", y0: 0.2728822684852836, y1: 0.26399856424982054 }, 

{ x0: "2025-06-14T12:45:26", x1: "2025-06-14T12:53:18", y0: 0.26399856424982054, y1: 0.2191313711414214 }, 

{ x0: "2025-06-14T13:02:48", x1: "2025-06-14T13:03:23", y0: 0.2071966977745872, y1: 0.20118449389806173 }, 

{ x0: "2025-06-14T13:03:23", x1: "2025-06-14T13:07:22", y0: 0.20118449389806173, y1: 0.1673546302943288 }, 

{ x0: "2025-06-14T13:07:22", x1: "2025-06-14T13:12:16", y0: 0.1673546302943288, y1: 0.1414213926776741 }, 

{ x0: "2025-06-14T13:44:35", x1: "2025-06-14T14:06:36", y0: 0.4950646087580761, y1: 0.7978284278535535 }, 

{ x0: "2025-06-14T14:06:36", x1: "2025-06-14T14:11:51", y0: 0.7978284278535535, y1: 0.7450646087580761 }, 

{ x0: "2025-06-14T14:11:51", x1: "2025-06-14T14:22:10", y0: 0.7450646087580761, y1: 0.6823402727925341 }, 

{ x0: "2025-06-15T13:20:55", x1: "2025-06-15T13:21:30", y0: 0.9492103374012921, y1: 0.9353015075376885 }, 

{ x0: "2025-06-15T13:21:30", x1: "2025-06-15T14:11:13", y0: 0.9353015075376885, y1: 0.12455132806891601 }, 

{ x0: "2025-06-15T14:12:20", x1: "2025-06-15T14:13:03", y0: 0.10463029432878679, y1: 0.09063173007896626 }, 

{ x0: "2025-06-16T14:05:12", x1: "2025-06-16T14:06:36", y0: 0.7543683758235462, y1: 0.7710541392151246 }, 

{ x0: "2025-06-16T14:06:36", x1: "2025-06-16T14:15:53", y0: 0.7710541392151246, y1: 0.6939272414780865 }, 

{ x0: "2025-06-16T14:15:53", x1: "2025-06-16T14:17:13", y0: 0.6939272414780865, y1: 0.6820395302205672 }, 

{ x0: "2025-06-16T14:17:13", x1: "2025-06-16T14:22:20", y0: 0.6820395302205672, y1: 0.6454454311085649 }, 

{ x0: "2025-06-16T14:22:20", x1: "2025-06-16T14:23:35", y0: 0.6454454311085649, y1: 0.6407189916929247 }, 

{ x0: "2025-06-16T14:23:35", x1: "2025-06-16T14:28:36", y0: 0.6407189916929247, y1: 0.6064881122887424 }, 

{ x0: "2025-06-16T14:28:36", x1: "2025-06-16T14:39:00", y0: 0.6064881122887424, y1: 0.5675307934689201 }, 

{ x0: "2025-06-16T14:56:57", x1: "2025-06-16T14:57:33", y0: 0.5556430822114008, y1: 0.5492695502721283 }, 

{ x0: "2025-06-16T14:57:33", x1: "2025-06-16T15:16:04", y0: 0.5492695502721283, y1: 0.4236608421655686 }, 

{ x0: "2025-06-16T15:16:04", x1: "2025-06-16T15:17:27", y0: 0.4236608421655686, y1: 0.41492409051847606 }, 

{ x0: "2025-06-16T15:17:27", x1: "2025-06-16T15:17:27", y0: 0.41492409051847606, y1: 0.41492409051847606 }, 

{ x0: "2025-06-16T15:17:27", x1: "2025-06-16T15:28:17", y0: 0.41492409051847606, y1: 0.3784016041248926 }, 

{ x0: "2025-06-16T18:05:20", x1: "2025-06-16T18:05:55", y0: 0.3203236894872529, y1: 0.3140217702663993 }, 

{ x0: "2025-06-17T00:03:54", x1: "2025-06-17T00:05:21", y0: 2.3847035233457463, y1: 2.3847035233457463 }, 

{ x0: "2025-06-17T00:05:21", x1: "2025-06-17T00:07:09", y0: 2.3847035233457463, y1: 0.9872529361214551 }, 

{ x0: "2025-06-17T00:07:09", x1: "2025-06-17T00:07:30", y0: 0.9872529361214551, y1: 0.9848897164136351 }, 

{ x0: "2025-06-17T00:07:30", x1: "2025-06-17T00:08:53", y0: 0.9848897164136351, y1: 0.9737897450587224 }, 

{ x0: "2025-06-17T00:08:53", x1: "2025-06-17T00:09:00", y0: 0.9737897450587224, y1: 0.972930392437697 }, 

{ x0: "2025-06-17T00:09:00", x1: "2025-06-17T00:09:12", y0: 0.972930392437697, y1: 0.9721426525350902 }, 

{ x0: "2025-06-17T00:09:12", x1: "2025-06-17T00:11:30", y0: 0.9721426525350902, y1: 0.9641936407906044 }, 

{ x0: "2025-06-17T00:11:30", x1: "2025-06-17T00:14:24", y0: 0.9641936407906044, y1: 0.9546691492409052 }, 

{ x0: "2025-06-17T00:14:24", x1: "2025-06-17T00:17:30", y0: 0.9546691492409052, y1: 0.9443569177885992 }, 

{ x0: "2025-06-17T00:17:30", x1: "2025-06-17T00:19:58", y0: 0.9443569177885992, y1: 0.9356201661415068 }, 

{ x0: "2025-06-17T00:30:21", x1: "2025-06-17T00:30:56", y0: 0.925236321970782, y1: 0.9189344027499284 }, 

{ x0: "2025-06-17T00:30:56", x1: "2025-06-17T00:31:39", y0: 0.9189344027499284, y1: 0.9141363506158694 }, 

{ x0: "2025-06-17T00:31:39", x1: "2025-06-17T00:32:58", y0: 0.9141363506158694, y1: 0.9046118590661701 }, 

{ x0: "2025-06-17T00:32:58", x1: "2025-06-17T00:34:39", y0: 0.9046118590661701, y1: 0.8934402749928387 }, 

{ x0: "2025-06-17T00:34:39", x1: "2025-06-17T00:35:55", y0: 0.8934402749928387, y1: 0.8847035233457462 }, 

{ x0: "2025-06-17T00:35:55", x1: "2025-06-17T00:37:00", y0: 0.8847035233457462, y1: 0.8791893440274993 }, 

{ x0: "2025-06-17T00:37:00", x1: "2025-06-17T00:37:17", y0: 0.8791893440274993, y1: 0.8736035519908336 }, 

{ x0: "2025-06-17T00:37:17", x1: "2025-06-17T00:38:19", y0: 0.8736035519908336, y1: 0.8632913205385276 }, 

{ x0: "2025-06-17T00:38:19", x1: "2025-06-17T00:39:39", y0: 0.8632913205385276, y1: 0.8513319965625895 }, 

{ x0: "2025-06-17T00:39:39", x1: "2025-06-17T00:40:19", y0: 0.8513319965625895, y1: 0.8418075050128903 }, 

{ x0: "2025-06-17T00:40:19", x1: "2025-06-17T00:41:19", y0: 0.8418075050128903, y1: 0.8330707533657977 }, 

{ x0: "2025-06-17T00:41:19", x1: "2025-06-17T00:42:40", y0: 0.8330707533657977, y1: 0.8227585219134919 }, 

{ x0: "2025-06-17T00:42:40", x1: "2025-06-17T00:43:39", y0: 0.8227585219134919, y1: 0.814809510169006 }, 

{ x0: "2025-06-17T00:43:39", x1: "2025-06-17T00:44:58", y0: 0.814809510169006, y1: 0.8036379260956746 }, 

{ x0: "2025-06-17T00:44:58", x1: "2025-06-17T00:46:19", y0: 0.8036379260956746, y1: 0.7941134345459754 }, 

{ x0: "2025-06-17T00:46:19", x1: "2025-06-17T00:47:39", y0: 0.7941134345459754, y1: 0.7821541105700372 }, 



    ];

    

    function main() {

        Timegraph.draw({

            element: "drain-graph",

            data: drainGraphData,

            startTime: "2025-06-14T00:48:24",

            endTime: "2025-06-17T00:48:24",

        });

    }



    if (window.addEventListener != null) {

        window.addEventListener("load", main, false);



    } else if (window.attachEvent != null) {

        window.attachEvent("onload", main);

    }

    </script></head><body><h1>

      Battery report

    </h1><table style="margin-bottom: 6em;"><col/><tr><td class="label">

          COMPUTER NAME

        </td><td>DESKTOP-3PAR3IN</td></tr><tr><td class="label">

          SYSTEM PRODUCT NAME

        </td><td>Dell Inc. Latitude E5270</td></tr><tr><td class="label">

          BIOS

        </td><td>1.17.3 08/17/2017</td></tr><tr><td class="label">

          OS BUILD

        </td><td>19041.1.amd64fre.vb_release.191206-1406</td></tr><tr><td class="label">

          PLATFORM ROLE

        </td><td>Mobile</td></tr><tr><td class="label">

          CONNECTED STANDBY

        </td><td>Not supported</td></tr><tr><td class="label">

          REPORT TIME

        </td><td class="dateTime"><span class="date">2025-06-17 </span><span class="time">00:48:24</span></td></tr></table><h2>

      Installed batteries

    </h2><div class="explanation">

      Information about each currently installed battery

    </div><table><colgroup><col style="width: 15em;"/><col style="width: 14em;"/></colgroup><thead><tr><td> </td><td>

                  BATTERY

                  1</td></tr></thead><tr><td><span class="label">NAME</span></td><td>DELL RDRH971</td></tr><tr><td><span class="label">MANUFACTURER</span></td><td>LGC-LGC4.20</td></tr><tr><td><span class="label">SERIAL NUMBER</span></td><td>390</td></tr><tr><td><span class="label">CHEMISTRY</span></td><td>LION</td></tr><tr><td><span class="label">DESIGN CAPACITY</span></td><td>33,300 mWh

      </td></tr><tr style="height:0.4em;"></tr><tr><td><span class="label">FULL CHARGE CAPACITY</span></td><td>13,964 mWh

      </td></tr><tr><td><span class="label">CYCLE COUNT</span></td><td>

        -

      </td></tr></table><h2>Recent usage</h2><div class="explanation">

      Power states over the last 3 days

    </div><table><colgroup><col/><col class="col2"/><col style="width: 4.2em;"/><col class="percent"/><col style="width: 11em;"/></colgroup><thead><tr><td>

            START TIME

          </td><td class="centered">

            STATE

          </td><td class="centered">

            SOURCE

          </td><td colspan="2" class="centered">

            CAPACITY REMAINING

          </td></tr></thead><tr class="even  1"><td class="dateTime"><span class="date">2025-06-14 </span><span class="time">03:11:46</span></td><td class="state">

        Active

      </td><td class="acdc">

        AC

      </td><td class="percent">92 %

        </td><td class="mw">10,223 mWh

        </td></tr><tr class="odd suspend 2"><td class="dateTime"><span class="date"> </span><span class="time">11:43:38</span></td><td class="state">

        Suspended

      </td><td class="acdc"></td><td class="percent">90 %

        </td><td class="mw">9,990 mWh

        </td></tr><tr class="even dc 3"><td class="dateTime"><span class="date"> </span><span class="time">11:44:13</span></td><td class="state">

        Active

      </td><td class="acdc">

        Battery

      </td><td class="percent">89 %

        </td><td class="mw">9,912 mWh

        </td></tr><tr class="odd suspend 4"><td class="dateTime"><span class="date"> </span><span class="time">12:53:18</span></td><td class="state">

        Suspended

      </td><td class="acdc"></td><td class="percent">22 %

        </td><td class="mw">2,442 mWh

        </td></tr><tr class="even dc 5"><td class="dateTime"><span class="date"> </span><span class="time">13:02:48</span></td><td class="state">

        Active

      </td><td class="acdc">

        Battery

      </td><td class="percent">21 %

        </td><td class="mw">2,309 mWh

        </td></tr><tr class="odd  6"><td class="dateTime"><span class="date"> </span><span class="time">13:17:50</span></td><td class="state">

        Active

      </td><td class="acdc">

        AC

      </td><td class="percent">9 %

        </td><td class="mw">1,032 mWh

        </td></tr><tr class="even dc 7"><td class="dateTime"><span class="date"> </span><span class="time">14:06:36</span></td><td class="state">

        Active

      </td><td class="acdc">

        Battery

      </td><td class="percent">80 %

        </td><td class="mw">8,891 mWh

        </td></tr><tr class="odd suspend 8"><td class="dateTime"><span class="date"> </span><span class="time">14:22:10</span></td><td class="state">

        Suspended

      </td><td class="acdc"></td><td class="percent">68 %

        </td><td class="mw">7,604 mWh

        </td></tr><tr class="even dc 9"><td class="dateTime"><span class="date">2025-06-15 </span><span class="time">13:20:55</span></td><td class="state">

        Active

      </td><td class="acdc">

        Battery

      </td><td class="percent">95 %

        </td><td class="mw">10,578 mWh

        </td></tr><tr class="odd suspend 10"><td class="dateTime"><span class="date"> </span><span class="time">14:11:13</span></td><td class="state">

        Suspended

      </td><td class="acdc"></td><td class="percent">12 %

        </td><td class="mw">1,388 mWh

        </td></tr><tr class="even dc 11"><td class="dateTime"><span class="date"> </span><span class="time">14:12:20</span></td><td class="state">

        Active

      </td><td class="acdc">

        Battery

      </td><td class="percent">10 %

        </td><td class="mw">1,166 mWh

        </td></tr><tr class="odd suspend 12"><td class="dateTime"><span class="date"> </span><span class="time">14:13:03</span></td><td class="state">

        Suspended

      </td><td class="acdc"></td><td class="percent">9 %

        </td><td class="mw">1,010 mWh

        </td></tr><tr class="even  13"><td class="dateTime"><span class="date">2025-06-16 </span><span class="time">13:15:41</span></td><td class="state">

        Active

      </td><td class="acdc">

        AC

      </td><td class="percent">1 %

        </td><td class="mw">78 mWh

        </td></tr><tr class="odd dc 14"><td class="dateTime"><span class="date"> </span><span class="time">14:06:36</span></td><td class="state">

        Active

      </td><td class="acdc">

        Battery

      </td><td class="percent">77 %

        </td><td class="mw">10,767 mWh

        </td></tr><tr class="even suspend 15"><td class="dateTime"><span class="date"> </span><span class="time">14:39:00</span></td><td class="state">

        Suspended

      </td><td class="acdc"></td><td class="percent">57 %

        </td><td class="mw">7,925 mWh

        </td></tr><tr class="odd dc 16"><td class="dateTime"><span class="date"> </span><span class="time">14:56:57</span></td><td class="state">

        Active

      </td><td class="acdc">

        Battery

      </td><td class="percent">56 %

        </td><td class="mw">7,759 mWh

        </td></tr><tr class="even suspend 17"><td class="dateTime"><span class="date"> </span><span class="time">15:28:17</span></td><td class="state">

        Suspended

      </td><td class="acdc"></td><td class="percent">38 %

        </td><td class="mw">5,284 mWh

        </td></tr><tr class="odd dc 18"><td class="dateTime"><span class="date"> </span><span class="time">18:05:20</span></td><td class="state">

        Active

      </td><td class="acdc">

        Battery

      </td><td class="percent">32 %

        </td><td class="mw">4,473 mWh

        </td></tr><tr class="even  19"><td class="dateTime"><span class="date"> </span><span class="time">18:14:48</span></td><td class="state">

        Active

      </td><td class="acdc">

        AC

      </td><td class="percent">24 %

        </td><td class="mw">3,286 mWh

        </td></tr><tr class="odd suspend 20"><td class="dateTime"><span class="date"> </span><span class="time">18:53:37</span></td><td class="state">

        Suspended

      </td><td class="acdc"></td><td class="percent">81 %

        </td><td class="mw">11,311 mWh

        </td></tr><tr class="even  21"><td class="dateTime"><span class="date"> </span><span class="time">20:43:03</span></td><td class="state">

        Active

      </td><td class="acdc">

        AC

      </td><td class="percent">238 %

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="odd suspend 22"><td class="dateTime"><span class="date"> </span><span class="time">21:35:55</span></td><td class="state">

        Suspended

      </td><td class="acdc"></td><td class="percent">238 %

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="even  23"><td class="dateTime"><span class="date">2025-06-17 </span><span class="time">00:03:19</span></td><td class="state">

        Active

      </td><td class="acdc">

        AC

      </td><td class="percent">238 %

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="odd dc 24"><td class="dateTime"><span class="date"> </span><span class="time">00:05:21</span></td><td class="state">

        Active

      </td><td class="acdc">

        Battery

      </td><td class="percent">238 %

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="even suspend 25"><td class="dateTime"><span class="date"> </span><span class="time">00:19:58</span></td><td class="state">

        Suspended

      </td><td class="acdc"></td><td class="percent">94 %

        </td><td class="mw">13,065 mWh

        </td></tr><tr class="odd dc 26"><td class="dateTime"><span class="date"> </span><span class="time">00:30:21</span></td><td class="state">

        Active

      </td><td class="acdc">

        Battery

      </td><td class="percent">93 %

        </td><td class="mw">12,920 mWh

        </td></tr><tr class="even dc 27"><td class="dateTime"><span class="date"> </span><span class="time">00:48:24</span></td><td class="state">

        Report generated

      </td><td class="acdc">

        Battery

      </td><td class="percent">78 %

        </td><td class="mw">10,922 mWh

        </td></tr></table><h2>Battery usage</h2><div class="explanation">

      Battery drains over the last 3 days

    </div><canvas id="drain-graph" width="864" height="400"></canvas><table><colgroup><col/><col class="col2"/><col style="width: 10em;"/><col class="percent"/><col style="width: 11em;"/></colgroup><thead><tr><td>

            START TIME

          </td><td class="centered">

            STATE

          </td><td class="centered">

            DURATION

          </td><td class="centered" colspan="2">

            ENERGY DRAINED

          </td></tr></thead><tr class="even dc 1"><td class="dateTime"><span class="date">2025-06-14 </span><span class="time">11:44:13</span></td><td class="state">

        Active

      </td><td class="hms">1:09:04</td><td class="percent">67 %

        </td><td class="mw">7,470 mWh

        </td></tr><tr class="noncontigbreak"><td colspan="5"> </td></tr><tr class="odd dc 2"><td class="dateTime"><span class="date"> </span><span class="time">13:02:48</span></td><td class="state">

        Active

      </td><td class="hms">0:15:01</td><td class="percent">11 %

        </td><td class="mw">1,277 mWh

        </td></tr><tr class="noncontigbreak"><td colspan="5"> </td></tr><tr class="even dc 3"><td class="dateTime"><span class="date"> </span><span class="time">14:06:36</span></td><td class="state">

        Active

      </td><td class="hms">0:15:34</td><td class="percent">12 %

        </td><td class="mw">1,287 mWh

        </td></tr><tr class="noncontigbreak"><td colspan="5"> </td></tr><tr class="odd dc 4"><td class="dateTime"><span class="date">2025-06-15 </span><span class="time">13:20:55</span></td><td class="state">

        Active

      </td><td class="hms">0:50:17</td><td class="percent">82 %

        </td><td class="mw">9,190 mWh

        </td></tr><tr class="noncontigbreak"><td colspan="5"> </td></tr><tr class="even dc 5"><td class="dateTime"><span class="date"> </span><span class="time">14:12:20</span></td><td class="state">

        Active

      </td><td class="hms">0:00:43</td><td class="percent">1 %

        </td><td class="mw">156 mWh

        </td></tr><tr class="noncontigbreak"><td colspan="5"> </td></tr><tr class="odd dc 6"><td class="dateTime"><span class="date"> </span><span class="time">14:06:36</span></td><td class="state">

        Active

      </td><td class="hms">0:32:23</td><td class="percent">20 %

        </td><td class="mw">2,842 mWh

        </td></tr><tr class="noncontigbreak"><td colspan="5"> </td></tr><tr class="even dc 7"><td class="dateTime"><span class="date"> </span><span class="time">14:56:57</span></td><td class="state">

        Active

      </td><td class="hms">0:31:19</td><td class="percent">18 %

        </td><td class="mw">2,475 mWh

        </td></tr><tr class="noncontigbreak"><td colspan="5"> </td></tr><tr class="odd dc 8"><td class="dateTime"><span class="date"> </span><span class="time">18:05:20</span></td><td class="state">

        Active

      </td><td class="hms">0:09:28</td><td class="percent">9 %

        </td><td class="mw">1,187 mWh

        </td></tr><tr class="noncontigbreak"><td colspan="5"> </td></tr><tr class="even dc 9"><td class="dateTime"><span class="date"> </span><span class="time">00:05:21</span></td><td class="state">

        Active

      </td><td class="hms">0:14:36</td><td class="percent">145 %

        </td><td class="mw">20,235 mWh

        </td></tr><tr class="noncontigbreak"><td colspan="5"> </td></tr><tr class="odd dc 10"><td class="dateTime"><span class="date"> </span><span class="time">00:30:21</span></td><td class="state">

        Active

      </td><td class="hms">0:18:02</td><td class="percent">14 %

        </td><td class="mw">1,998 mWh

        </td></tr></table><h2>

      Usage history

    </h2><div class="explanation2">

      History of system usage on AC and battery

    </div><table><colgroup><col/><col class="col2"/><col style="width: 10em;"/><col style=""/><col style="width: 10em;"/><col style="width: 10em;"/><col style=""/></colgroup><thead><tr><td> </td><td colspan="2" class="centered">

            BATTERY DURATION

          </td><td class="colBreak"> </td><td colspan="3" class="centered">

            AC DURATION

          </td></tr><tr><td>

            PERIOD

          </td><td class="centered">

            ACTIVE

          </td><td class="centered">

            CONNECTED STANDBY

          </td><td class="colBreak"> </td><td class="centered">

            ACTIVE

          </td><td class="centered">

            CONNECTED STANDBY

          </td></tr></thead><tr class="even  1"><td class="dateTime">2023-12-20

      - 2023-12-25</td><td class="hms">8:03:48</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">2:36:52</td><td class="nullValue">-</td></tr><tr class="odd  2"><td class="dateTime">2023-12-25

      - 2024-01-02</td><td class="hms">5:58:19</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">1:29:13</td><td class="nullValue">-</td></tr><tr class="even  3"><td class="dateTime">2024-01-02

      - 2024-01-09</td><td class="hms">20:07:30</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">6:06:37</td><td class="nullValue">-</td></tr><tr class="odd  4"><td class="dateTime">2024-01-09

      - 2024-01-15</td><td class="hms">18:01:28</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">17:06:36</td><td class="nullValue">-</td></tr><tr class="even  5"><td class="dateTime">2024-01-15

      - 2024-01-23</td><td class="hms">6:45:20</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">2:48:40</td><td class="nullValue">-</td></tr><tr class="odd  6"><td class="dateTime">2024-01-23

      - 2024-01-30</td><td class="hms">25:15:39</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">23:45:19</td><td class="nullValue">-</td></tr><tr class="even  7"><td class="dateTime">2024-01-30

      - 2024-02-16</td><td class="hms">26:53:16</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">25:01:42</td><td class="nullValue">-</td></tr><tr class="odd  8"><td class="dateTime">2024-02-16

      - 2024-02-21</td><td class="hms">2:12:33</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">2:08:02</td><td class="nullValue">-</td></tr><tr class="even  9"><td class="dateTime">2024-02-21

      - 2024-02-26</td><td class="hms">3:38:03</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">0:44:10</td><td class="nullValue">-</td></tr><tr class="odd  10"><td class="dateTime">2024-02-26

      - 2024-03-07</td><td class="hms">0:10:46</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">1:51:56</td><td class="nullValue">-</td></tr><tr class="even  11"><td class="dateTime">2024-03-07

      - 2024-03-11</td><td class="hms">4:17:27</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">1:05:32</td><td class="nullValue">-</td></tr><tr class="odd  12"><td class="dateTime">2024-03-11

      - 2024-04-02</td><td class="hms">3:13:54</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">1:51:15</td><td class="nullValue">-</td></tr><tr class="even  13"><td class="dateTime">2024-04-02

      - 2024-04-19</td><td class="hms">3:00:53</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">1:50:51</td><td class="nullValue">-</td></tr><tr class="odd  14"><td class="dateTime">2024-04-19

      - 2024-04-22</td><td class="hms">1:49:05</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">0:34:36</td><td class="nullValue">-</td></tr><tr class="even  15"><td class="dateTime">2024-04-22

      - 2024-05-05</td><td class="hms">15:37:42</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">8:09:17</td><td class="nullValue">-</td></tr><tr class="odd  16"><td class="dateTime">2024-05-05

      - 2024-05-06</td><td class="hms">0:22:23</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr class="even  17"><td class="dateTime">2024-05-06

      - 2024-05-14</td><td class="hms">2:09:53</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">0:46:59</td><td class="nullValue">-</td></tr><tr class="odd  18"><td class="dateTime">2024-05-14

      - 2024-05-20</td><td class="hms">5:44:08</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">2:02:40</td><td class="nullValue">-</td></tr><tr class="even  19"><td class="dateTime">2024-05-20

      - 2024-05-27</td><td class="hms">6:41:26</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">3:57:15</td><td class="nullValue">-</td></tr><tr class="odd  20"><td class="dateTime">2024-05-27

      - 2024-06-03</td><td class="hms">3:00:16</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">2:16:13</td><td class="nullValue">-</td></tr><tr class="even  21"><td class="dateTime">2024-06-03

      - 2024-06-10</td><td class="hms">4:57:34</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">2:50:51</td><td class="nullValue">-</td></tr><tr class="odd  22"><td class="dateTime">2024-06-10

      - 2024-06-20</td><td class="hms">2:52:43</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">0:35:09</td><td class="nullValue">-</td></tr><tr class="even  23"><td class="dateTime">2024-06-20

      - 2024-06-29</td><td class="hms">6:37:30</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">0:42:57</td><td class="nullValue">-</td></tr><tr class="odd  24"><td class="dateTime">2024-06-29

      - 2024-07-03</td><td class="hms">0:05:05</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">0:16:40</td><td class="nullValue">-</td></tr><tr class="even  25"><td class="dateTime">2024-07-03

      - 2024-07-09</td><td class="hms">0:27:17</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">0:27:24</td><td class="nullValue">-</td></tr><tr class="odd  26"><td class="dateTime">2024-07-09

      - 2024-07-19</td><td class="hms">2:50:05</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">1:07:49</td><td class="nullValue">-</td></tr><tr class="even  27"><td class="dateTime">2024-07-19

      - 2024-07-22</td><td class="hms">4:03:28</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">0:56:02</td><td class="nullValue">-</td></tr><tr class="odd  28"><td class="dateTime">2024-07-22

      - 2024-07-29</td><td class="hms">5:44:05</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">2:20:05</td><td class="nullValue">-</td></tr><tr class="even  29"><td class="dateTime">2024-07-29

      - 2024-08-12</td><td class="hms">37:12:16</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">11:38:40</td><td class="nullValue">-</td></tr><tr class="odd  30"><td class="dateTime">2024-08-12

      - 2024-08-21</td><td class="hms">41:20:25</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">13:24:38</td><td class="nullValue">-</td></tr><tr class="even  31"><td class="dateTime">2024-08-21

      - 2024-08-26</td><td class="hms">3:17:38</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">4:19:29</td><td class="nullValue">-</td></tr><tr class="odd  32"><td class="dateTime">2024-08-26

      - 2024-09-03</td><td class="hms">8:21:44</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">2:51:10</td><td class="nullValue">-</td></tr><tr class="even  33"><td class="dateTime">2024-09-03

      - 2024-09-09</td><td class="hms">55:04:36</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">8:40:45</td><td class="nullValue">-</td></tr><tr class="odd  34"><td class="dateTime">2024-09-09

      - 2024-09-16</td><td class="hms">1:19:29</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">5:14:23</td><td class="nullValue">-</td></tr><tr class="even  35"><td class="dateTime">2024-09-16

      - 2024-09-25</td><td class="hms">59:17:06</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">15:04:54</td><td class="nullValue">-</td></tr><tr class="odd  36"><td class="dateTime">2024-09-25

      - 2024-10-01</td><td class="hms">0:01:23</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">0:40:37</td><td class="nullValue">-</td></tr><tr class="even  37"><td class="dateTime">2024-10-01

      - 2024-10-07</td><td class="hms">6:06:22</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">2:14:23</td><td class="nullValue">-</td></tr><tr class="odd  38"><td class="dateTime">2024-10-07

      - 2024-10-23</td><td class="hms">1:38:07</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">0:28:14</td><td class="nullValue">-</td></tr><tr class="even  39"><td class="dateTime">2024-10-23

      - 2024-11-05</td><td class="hms">1:04:27</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr class="odd  40"><td class="dateTime">2024-11-05

      - 2024-11-20</td><td class="hms">4:11:47</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">3:13:08</td><td class="nullValue">-</td></tr><tr class="even  41"><td class="dateTime">2024-11-20

      - 2024-11-25</td><td class="hms">1:15:42</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">9:47:28</td><td class="nullValue">-</td></tr><tr class="odd  42"><td class="dateTime">2024-11-25

      - 2024-12-02</td><td class="hms">3:28:14</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">1:35:22</td><td class="nullValue">-</td></tr><tr class="even  43"><td class="dateTime">2024-12-02

      - 2024-12-14</td><td class="hms">0:24:55</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr class="odd  44"><td class="dateTime">2024-12-14

      - 2024-12-18</td><td class="hms">1:52:30</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">1:08:42</td><td class="nullValue">-</td></tr><tr class="even  45"><td class="dateTime">2024-12-18

      - 2024-12-23</td><td class="hms">0:23:59</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr class="odd  46"><td class="dateTime">2024-12-23

      - 2025-01-06</td><td class="hms">2:35:11</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">0:45:34</td><td class="nullValue">-</td></tr><tr class="even  47"><td class="dateTime">2025-01-06

      - 2025-01-13</td><td class="hms">25:19:01</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">8:00:17</td><td class="nullValue">-</td></tr><tr class="odd  48"><td class="dateTime">2025-01-13

      - 2025-01-22</td><td class="hms">13:17:50</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">8:14:13</td><td class="nullValue">-</td></tr><tr class="even  49"><td class="dateTime">2025-01-22

      - 2025-01-27</td><td class="hms">6:39:45</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">4:54:28</td><td class="nullValue">-</td></tr><tr class="odd  50"><td class="dateTime">2025-01-27

      - 2025-02-04</td><td class="hms">22:22:18</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">13:27:35</td><td class="nullValue">-</td></tr><tr class="even  51"><td class="dateTime">2025-02-04

      - 2025-02-10</td><td class="hms">1:55:26</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">1:20:33</td><td class="nullValue">-</td></tr><tr class="odd  52"><td class="dateTime">2025-02-10

      - 2025-02-17</td><td class="hms">2:05:13</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">1:08:58</td><td class="nullValue">-</td></tr><tr class="even  53"><td class="dateTime">2025-02-17

      - 2025-02-24</td><td class="hms">30:12:14</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">18:06:35</td><td class="nullValue">-</td></tr><tr class="odd  54"><td class="dateTime">2025-02-24

      - 2025-03-03</td><td class="hms">5:31:39</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">1:27:25</td><td class="nullValue">-</td></tr><tr class="even  55"><td class="dateTime">2025-03-03

      - 2025-03-12</td><td class="hms">5:35:34</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">3:45:02</td><td class="nullValue">-</td></tr><tr class="odd  56"><td class="dateTime">2025-03-12

      - 2025-04-02</td><td class="hms">1:18:48</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr class="even  57"><td class="dateTime">2025-04-02

      - 2025-04-07</td><td class="hms">0:00:01</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">1:34:28</td><td class="nullValue">-</td></tr><tr class="odd  58"><td class="dateTime">2025-04-07

      - 2025-04-14</td><td class="hms">0:22:23</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">0:49:57</td><td class="nullValue">-</td></tr><tr class="even  59"><td class="dateTime">2025-04-21

      - 2025-04-28</td><td class="hms">1:01:15</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">1:16:07</td><td class="nullValue">-</td></tr><tr class="odd  60"><td class="dateTime">2025-04-28

      - 2025-05-05</td><td class="hms">0:28:34</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">1:52:49</td><td class="nullValue">-</td></tr><tr class="even  61"><td class="dateTime">2025-05-05

      - 2025-05-12</td><td class="hms">6:27:23</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">4:03:15</td><td class="nullValue">-</td></tr><tr class="odd  62"><td class="dateTime">2025-05-12

      - 2025-05-19</td><td class="hms">2:37:53</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">2:24:45</td><td class="nullValue">-</td></tr><tr class="even  63"><td class="dateTime">2025-05-19

      - 2025-05-26</td><td class="hms">4:51:12</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">3:36:55</td><td class="nullValue">-</td></tr><tr class="odd  64"><td class="dateTime">2025-05-26

      - 2025-06-02</td><td class="hms">1:29:46</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">2:59:28</td><td class="nullValue">-</td></tr><tr class="even  65"><td class="dateTime">2025-06-02

      - 2025-06-09</td><td class="hms">0:52:18</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr class="odd  66"><td class="dateTime">2025-06-09</td><td class="hms">0:52:18</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr class="even  67"><td class="dateTime">2025-06-10</td><td class="hms">0:14:59</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">2:51:02</td><td class="nullValue">-</td></tr><tr class="odd  68"><td class="dateTime">2025-06-11</td><td class="nullValue">-</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr class="even  69"><td class="dateTime">2025-06-12</td><td class="hms">0:45:32</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr class="odd  70"><td class="dateTime">2025-06-13</td><td class="hms">0:01:54</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">1:16:11</td><td class="nullValue">-</td></tr><tr class="even  71"><td class="dateTime">2025-06-14</td><td class="hms">1:13:37</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">0:48:45</td><td class="nullValue">-</td></tr><tr class="odd  72"><td class="dateTime">2025-06-15</td><td class="hms">0:50:25</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr class="even  73"><td class="dateTime">2025-06-16</td><td class="hms">0:49:29</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">2:19:41</td><td class="nullValue">-</td></tr></table><h2>

      Battery capacity history

    </h2><div class="explanation">

      Charge capacity history of the system's batteries

    </div><table><colgroup><col/><col class="col2"/><col style="width: 10em;"/></colgroup><thead><tr><td><span>PERIOD</span></td><td class="centered">

            FULL CHARGE CAPACITY

          </td><td class="centered">

            DESIGN CAPACITY

          </td></tr></thead><tr class="even  1"><td class="dateTime">2023-12-20

      - 2023-12-25</td><td class="mw">29,903 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="odd  2"><td class="dateTime">2023-12-25

      - 2024-01-02</td><td class="mw">29,903 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="even  3"><td class="dateTime">2024-01-02

      - 2024-01-09</td><td class="mw">29,890 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="odd  4"><td class="dateTime">2024-01-09

      - 2024-01-15</td><td class="mw">29,813 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="even  5"><td class="dateTime">2024-01-15

      - 2024-01-23</td><td class="mw">28,194 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="odd  6"><td class="dateTime">2024-01-23

      - 2024-01-30</td><td class="mw">28,617 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="even  7"><td class="dateTime">2024-01-30

      - 2024-02-16</td><td class="mw">28,269 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="odd  8"><td class="dateTime">2024-02-16

      - 2024-02-21</td><td class="mw">27,206 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="even  9"><td class="dateTime">2024-02-21

      - 2024-02-26</td><td class="mw">27,206 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="odd  10"><td class="dateTime">2024-02-26

      - 2024-03-07</td><td class="mw">27,206 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="even  11"><td class="dateTime">2024-03-07

      - 2024-03-11</td><td class="mw">27,206 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="odd  12"><td class="dateTime">2024-03-11

      - 2024-04-02</td><td class="mw">27,206 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="even  13"><td class="dateTime">2024-04-02

      - 2024-04-19</td><td class="mw">26,731 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="odd  14"><td class="dateTime">2024-04-19

      - 2024-04-22</td><td class="mw">26,729 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="even  15"><td class="dateTime">2024-04-22

      - 2024-05-05</td><td class="mw">26,220 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="odd  16"><td class="dateTime">2024-05-05

      - 2024-05-06</td><td class="mw">24,309 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="even  17"><td class="dateTime">2024-05-06

      - 2024-05-14</td><td class="mw">24,309 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="odd  18"><td class="dateTime">2024-05-14

      - 2024-05-20</td><td class="mw">24,224 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="even  19"><td class="dateTime">2024-05-20

      - 2024-05-27</td><td class="mw">24,154 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="odd  20"><td class="dateTime">2024-05-27

      - 2024-06-03</td><td class="mw">24,154 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="even  21"><td class="dateTime">2024-06-03

      - 2024-06-10</td><td class="mw">24,154 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="odd  22"><td class="dateTime">2024-06-10

      - 2024-06-20</td><td class="mw">24,154 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="even  23"><td class="dateTime">2024-06-20

      - 2024-06-29</td><td class="mw">23,524 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="odd  24"><td class="dateTime">2024-06-29

      - 2024-07-03</td><td class="mw">23,310 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="even  25"><td class="dateTime">2024-07-03

      - 2024-07-09</td><td class="mw">23,310 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="odd  26"><td class="dateTime">2024-07-09

      - 2024-07-19</td><td class="mw">23,310 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="even  27"><td class="dateTime">2024-07-19

      - 2024-07-22</td><td class="mw">23,310 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="odd  28"><td class="dateTime">2024-07-22

      - 2024-07-29</td><td class="mw">23,310 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="even  29"><td class="dateTime">2024-07-29

      - 2024-08-12</td><td class="mw">24,712 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="odd  30"><td class="dateTime">2024-08-12

      - 2024-08-21</td><td class="mw">25,821 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="even  31"><td class="dateTime">2024-08-21

      - 2024-08-26</td><td class="mw">26,197 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="odd  32"><td class="dateTime">2024-08-26

      - 2024-09-03</td><td class="mw">26,052 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="even  33"><td class="dateTime">2024-09-03

      - 2024-09-09</td><td class="mw">26,209 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="odd  34"><td class="dateTime">2024-09-09

      - 2024-09-16</td><td class="mw">27,029 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="even  35"><td class="dateTime">2024-09-16

      - 2024-09-25</td><td class="mw">26,220 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="odd  36"><td class="dateTime">2024-09-25

      - 2024-10-01</td><td class="mw">24,187 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="even  37"><td class="dateTime">2024-10-01

      - 2024-10-07</td><td class="mw">24,187 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="odd  38"><td class="dateTime">2024-10-07

      - 2024-10-23</td><td class="mw">24,187 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="even  39"><td class="dateTime">2024-10-23

      - 2024-11-05</td><td class="mw">24,187 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="odd  40"><td class="dateTime">2024-11-05

      - 2024-11-20</td><td class="mw">21,905 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="even  41"><td class="dateTime">2024-11-20

      - 2024-11-25</td><td class="mw">21,345 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="odd  42"><td class="dateTime">2024-11-25

      - 2024-12-02</td><td class="mw">21,345 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="even  43"><td class="dateTime">2024-12-02

      - 2024-12-14</td><td class="mw">21,345 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="odd  44"><td class="dateTime">2024-12-14

      - 2024-12-18</td><td class="mw">21,345 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="even  45"><td class="dateTime">2024-12-18

      - 2024-12-23</td><td class="mw">21,345 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="odd  46"><td class="dateTime">2024-12-23

      - 2025-01-06</td><td class="mw">21,345 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="even  47"><td class="dateTime">2025-01-06

      - 2025-01-13</td><td class="mw">18,504 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="odd  48"><td class="dateTime">2025-01-13

      - 2025-01-22</td><td class="mw">19,033 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="even  49"><td class="dateTime">2025-01-22

      - 2025-01-27</td><td class="mw">21,423 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="odd  50"><td class="dateTime">2025-01-27

      - 2025-02-04</td><td class="mw">20,678 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="even  51"><td class="dateTime">2025-02-04

      - 2025-02-10</td><td class="mw">20,713 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="odd  52"><td class="dateTime">2025-02-10

      - 2025-02-17</td><td class="mw">20,713 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="even  53"><td class="dateTime">2025-02-17

      - 2025-02-24</td><td class="mw">20,692 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="odd  54"><td class="dateTime">2025-02-24

      - 2025-03-03</td><td class="mw">17,616 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="even  55"><td class="dateTime">2025-03-03

      - 2025-03-12</td><td class="mw">17,616 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="odd  56"><td class="dateTime">2025-03-12

      - 2025-04-02</td><td class="mw">17,616 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="even  57"><td class="dateTime">2025-04-02

      - 2025-04-07</td><td class="mw">17,616 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="odd  58"><td class="dateTime">2025-04-07

      - 2025-04-14</td><td class="mw">17,616 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="even  59"><td class="dateTime">2025-04-21

      - 2025-04-28</td><td class="mw">14,264 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="odd  60"><td class="dateTime">2025-04-28

      - 2025-05-05</td><td class="mw">14,264 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="even  61"><td class="dateTime">2025-05-05

      - 2025-05-12</td><td class="mw">14,264 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="odd  62"><td class="dateTime">2025-05-12

      - 2025-05-19</td><td class="mw">16,616 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="even  63"><td class="dateTime">2025-05-19

      - 2025-05-26</td><td class="mw">15,052 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="odd  64"><td class="dateTime">2025-05-26

      - 2025-06-02</td><td class="mw">11,333 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="even  65"><td class="dateTime">2025-06-02

      - 2025-06-09</td><td class="mw">11,333 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="odd  66"><td class="dateTime">2025-06-09</td><td class="mw">11,333 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="even  67"><td class="dateTime">2025-06-10</td><td class="mw">11,245 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="odd  68"><td class="dateTime">2025-06-11</td><td class="mw">11,144 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="even  69"><td class="dateTime">2025-06-12</td><td class="mw">11,144 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="odd  70"><td class="dateTime">2025-06-13</td><td class="mw">11,144 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="even  71"><td class="dateTime">2025-06-14</td><td class="mw">11,144 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="odd  72"><td class="dateTime">2025-06-15</td><td class="mw">11,144 mWh

        </td><td class="mw">33,300 mWh

        </td></tr><tr class="even  73"><td class="dateTime">2025-06-16</td><td class="mw">12,405 mWh

        </td><td class="mw">33,300 mWh

        </td></tr></table><h2>

      Battery life estimates

    </h2><div class="explanation2">

      Battery life estimates based on observed drains

    </div><table><colgroup><col/><col class="col2"/><col style="width: 10em;"/><col style=""/><col style="width: 10em;"/><col style="width: 10em;"/><col style="width: 10em;"/></colgroup><thead><tr class="rowHeader"><td> </td><td colspan="2" class="centered">

            AT FULL CHARGE

          </td><td class="colBreak"> </td><td colspan="2" class="centered">

            AT DESIGN CAPACITY

          </td></tr><tr class="rowHeader"><td>

            PERIOD

          </td><td class="centered"><span>ACTIVE</span></td><td class="centered"><span>CONNECTED STANDBY</span></td><td class="colBreak"> </td><td class="centered"><span>ACTIVE</span></td><td class="centered"><span>CONNECTED STANDBY</span></td></tr></thead><tr style="vertical-align:top" class="even  1"><td class="dateTime">2023-12-20

      - 2023-12-25</td><td class="hms">3:53:20</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">4:19:50</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  2"><td class="dateTime">2023-12-25

      - 2024-01-02</td><td class="hms">4:00:51</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">4:28:12</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  3"><td class="dateTime">2024-01-02

      - 2024-01-09</td><td class="hms">3:46:51</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">4:12:44</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  4"><td class="dateTime">2024-01-09

      - 2024-01-15</td><td class="hms">2:40:46</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">2:59:35</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  5"><td class="dateTime">2024-01-15

      - 2024-01-23</td><td class="hms">2:30:21</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">2:57:35</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  6"><td class="dateTime">2024-01-23

      - 2024-01-30</td><td class="hms">2:34:32</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">2:59:50</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  7"><td class="dateTime">2024-01-30

      - 2024-02-16</td><td class="hms">2:32:30</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">2:59:39</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  8"><td class="dateTime">2024-02-16

      - 2024-02-21</td><td class="hms">1:49:40</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">2:14:14</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  9"><td class="dateTime">2024-02-21

      - 2024-02-26</td><td class="hms">3:21:18</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">4:06:23</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  10"><td class="dateTime">2024-02-26

      - 2024-03-07</td><td class="nullValue">-</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  11"><td class="dateTime">2024-03-07

      - 2024-03-11</td><td class="hms">2:11:14</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">2:40:38</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  12"><td class="dateTime">2024-03-11

      - 2024-04-02</td><td class="hms">2:29:55</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">3:03:30</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  13"><td class="dateTime">2024-04-02

      - 2024-04-19</td><td class="hms">2:09:54</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">2:41:50</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  14"><td class="dateTime">2024-04-19

      - 2024-04-22</td><td class="hms">2:27:00</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">3:03:09</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  15"><td class="dateTime">2024-04-22

      - 2024-05-05</td><td class="hms">2:06:32</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">2:40:42</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  16"><td class="dateTime">2024-05-05

      - 2024-05-06</td><td class="hms">2:05:03</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">2:51:18</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  17"><td class="dateTime">2024-05-06

      - 2024-05-14</td><td class="hms">2:10:14</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">2:58:24</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  18"><td class="dateTime">2024-05-14

      - 2024-05-20</td><td class="hms">1:54:59</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">2:38:04</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  19"><td class="dateTime">2024-05-20

      - 2024-05-27</td><td class="hms">2:33:44</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">3:31:57</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  20"><td class="dateTime">2024-05-27

      - 2024-06-03</td><td class="hms">1:52:45</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">2:35:27</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  21"><td class="dateTime">2024-06-03

      - 2024-06-10</td><td class="hms">2:08:43</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">2:57:28</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  22"><td class="dateTime">2024-06-10

      - 2024-06-20</td><td class="hms">1:58:36</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">2:43:30</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  23"><td class="dateTime">2024-06-20

      - 2024-06-29</td><td class="hms">1:51:50</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">2:38:19</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  24"><td class="dateTime">2024-06-29

      - 2024-07-03</td><td class="hms">3:57:27</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">5:39:13</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  25"><td class="dateTime">2024-07-03

      - 2024-07-09</td><td class="hms">1:57:52</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">2:48:24</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  26"><td class="dateTime">2024-07-09

      - 2024-07-19</td><td class="hms">2:09:55</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">3:05:36</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  27"><td class="dateTime">2024-07-19

      - 2024-07-22</td><td class="hms">2:39:22</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">3:47:40</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  28"><td class="dateTime">2024-07-22

      - 2024-07-29</td><td class="hms">2:36:13</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">3:43:10</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  29"><td class="dateTime">2024-07-29

      - 2024-08-12</td><td class="hms">3:59:32</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">5:22:47</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  30"><td class="dateTime">2024-08-12

      - 2024-08-21</td><td class="hms">3:55:28</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">5:03:40</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  31"><td class="dateTime">2024-08-21

      - 2024-08-26</td><td class="hms">3:37:39</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">4:36:40</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  32"><td class="dateTime">2024-08-26

      - 2024-09-03</td><td class="hms">3:30:08</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">4:28:36</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  33"><td class="dateTime">2024-09-03

      - 2024-09-09</td><td class="hms">11:48:47</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">15:00:33</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  34"><td class="dateTime">2024-09-09

      - 2024-09-16</td><td class="hms">2:19:26</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">2:51:47</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  35"><td class="dateTime">2024-09-16

      - 2024-09-25</td><td class="hms">9:15:00</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">11:44:51</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  36"><td class="dateTime">2024-09-25

      - 2024-10-01</td><td class="hms">2:11:12</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">3:00:38</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  37"><td class="dateTime">2024-10-01

      - 2024-10-07</td><td class="hms">2:19:13</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">3:11:40</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  38"><td class="dateTime">2024-10-07

      - 2024-10-23</td><td class="hms">2:37:18</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">3:36:34</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  39"><td class="dateTime">2024-10-23

      - 2024-11-05</td><td class="hms">1:49:53</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">2:31:17</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  40"><td class="dateTime">2024-11-05

      - 2024-11-20</td><td class="hms">2:52:20</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">4:21:59</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  41"><td class="dateTime">2024-11-20

      - 2024-11-25</td><td class="hms">0:44:05</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">1:08:47</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  42"><td class="dateTime">2024-11-25

      - 2024-12-02</td><td class="hms">2:07:04</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">3:18:14</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  43"><td class="dateTime">2024-12-02

      - 2024-12-14</td><td class="hms">1:43:16</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">2:41:06</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  44"><td class="dateTime">2024-12-14

      - 2024-12-18</td><td class="hms">1:10:04</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">1:49:19</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  45"><td class="dateTime">2024-12-18

      - 2024-12-23</td><td class="hms">2:21:24</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">3:40:37</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  46"><td class="dateTime">2024-12-23

      - 2025-01-06</td><td class="hms">1:46:32</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">2:46:12</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  47"><td class="dateTime">2025-01-06

      - 2025-01-13</td><td class="hms">2:58:23</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">5:21:01</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  48"><td class="dateTime">2025-01-13

      - 2025-01-22</td><td class="hms">1:31:49</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">2:40:40</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  49"><td class="dateTime">2025-01-22

      - 2025-01-27</td><td class="hms">1:58:35</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">3:04:20</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  50"><td class="dateTime">2025-01-27

      - 2025-02-04</td><td class="hms">1:46:18</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">2:51:11</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  51"><td class="dateTime">2025-02-04

      - 2025-02-10</td><td class="hms">2:53:34</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">4:39:03</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  52"><td class="dateTime">2025-02-10

      - 2025-02-17</td><td class="hms">2:56:29</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">4:43:45</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  53"><td class="dateTime">2025-02-17

      - 2025-02-24</td><td class="hms">1:56:51</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">3:08:03</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  54"><td class="dateTime">2025-02-24

      - 2025-03-03</td><td class="hms">2:52:58</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">5:26:58</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  55"><td class="dateTime">2025-03-03

      - 2025-03-12</td><td class="hms">2:23:18</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">4:30:54</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  56"><td class="dateTime">2025-03-12

      - 2025-04-02</td><td class="hms">1:37:14</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">3:03:50</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  57"><td class="dateTime">2025-04-02

      - 2025-04-07</td><td class="nullValue">-</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  58"><td class="dateTime">2025-04-07

      - 2025-04-14</td><td class="hms">1:36:47</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">3:02:57</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  59"><td class="dateTime">2025-04-21

      - 2025-04-28</td><td class="hms">1:32:35</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">3:36:09</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  60"><td class="dateTime">2025-04-28

      - 2025-05-05</td><td class="hms">1:22:51</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">3:13:25</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  61"><td class="dateTime">2025-05-05

      - 2025-05-12</td><td class="hms">1:16:35</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">2:58:49</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  62"><td class="dateTime">2025-05-12

      - 2025-05-19</td><td class="hms">1:30:20</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">3:01:03</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  63"><td class="dateTime">2025-05-19

      - 2025-05-26</td><td class="hms">0:52:13</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">1:55:31</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  64"><td class="dateTime">2025-05-26

      - 2025-06-02</td><td class="hms">1:41:50</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">4:59:13</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  65"><td class="dateTime">2025-06-02

      - 2025-06-09</td><td class="hms">1:12:56</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">3:34:20</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  66"><td class="dateTime">2025-06-09</td><td class="hms">1:12:56</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">3:34:20</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  67"><td class="dateTime">2025-06-10</td><td class="hms">0:07:06</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">0:21:02</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  68"><td class="dateTime">2025-06-11</td><td class="nullValue">-</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  69"><td class="dateTime">2025-06-12</td><td class="hms">1:21:37</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">4:03:55</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  70"><td class="dateTime">2025-06-13</td><td class="hms">1:30:52</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">4:31:32</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  71"><td class="dateTime">2025-06-14</td><td class="hms">1:38:33</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">4:54:30</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  72"><td class="dateTime">2025-06-15</td><td class="hms">1:01:07</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">3:02:39</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  73"><td class="dateTime">2025-06-16</td><td class="hms">1:57:53</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">5:16:27</td><td class="nullValue">-</td></tr></table><div class="explanation2" style="margin-top: 1em; margin-bottom: 0.4em;">

      Current estimate of battery life based on all observed drains since OS install

    </div><table><colgroup><col/><col class="col2"/><col style="width: 10em;"/><col style=""/><col style="width: 10em;"/><col style="width: 10em;"/><col style="width: 10em;"/></colgroup><tr class="even" style="vertical-align:top"><td>

          Since OS install

        </td><td class="hms">1:42:18</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="hms">4:03:59</td><td class="nullValue">-</td></tr></table><br/><br/><br/></body></html>