# Battery Data Collection Summary

## 🔋 Universal Battery Metrics (HP, Dell, Lenovo)

### Key Battery Health Formula
**Battery Health %** = (Full Charge Capacity ÷ Design Capacity) × 100

### Battery Status Codes (Universal)
- **1** = Other
- **2** = Unknown  
- **3** = Fully Charged
- **4** = Low
- **5** = Critical
- **6** = Charging

### Health Status Thresholds (Industry Standard)
- **Good**: ≥ 80% (No replacement needed)
- **Fair**: 50-79% (Monitor closely)
- **Poor**: < 50% (Replacement recommended)

## 📊 Available Data Points

| Data | Description | Reliability | Universal |
|------|-------------|------------|-----------|
| **Charge %** | Current battery percentage | ✅ Always | Yes |
| **Charging Status** | If plugged in/charging | ✅ Always | Yes |
| **Design Capacity** | Original capacity (mWh) | ⚠️ Use root\WMI | Yes |
| **Full Charge Capacity** | Current max capacity (mWh) | ⚠️ Use root\WMI | Yes |
| **Cycle Count** | Charge/discharge cycles | ⚠️ Use root\WMI | Yes |
| **Battery Health %** | Calculated from above | ✅ When data available | Yes |
| **Chemistry** | Battery type (Li-ion, etc) | ✅ Usually | Yes |
| **Serial Number** | Battery serial | ❌ Rarely | Varies |
| **Manufacture Date** | Battery age | ❌ Rarely | Varies |

## 🛠️ Two Reliable Methods

### Method 1: WMI Classes (Programmatic)
```powershell
# ❌ DON'T USE: root\cimv2\Win32_Battery (often returns null)
# ✅ USE: root\WMI namespace classes

Get-WmiObject -Namespace root\WMI -Class BatteryStaticData          # Design Capacity
Get-WmiObject -Namespace root\WMI -Class BatteryFullChargedCapacity # Current Capacity  
Get-WmiObject -Namespace root\WMI -Class BatteryCycleCount          # Cycles
```

### Method 2: PowerCfg (Most Complete)
```cmd
powercfg /batteryreport
```
Generates HTML report with:
- Complete charge/discharge history
- Battery usage patterns
- Capacity degradation over time
- Estimated battery life

## 💻 Quick Test Commands

### PowerShell One-Liner (Copy & Run)
```powershell
$d=(Get-WmiObject -Namespace root\WMI BatteryStaticData).DesignedCapacity;$f=(Get-WmiObject -Namespace root\WMI BatteryFullChargedCapacity).FullChargedCapacity;if($d -and $f){"Health: "+[math]::Round(($f/$d)*100,2)+"%"}else{"Data not available"}
```

### Generate Full Report
```cmd
powercfg /batteryreport && start battery-report.html
```

## 📝 Scripts Provided

1. **test-battery-windows.ps1** - PowerShell script with full error handling
2. **test-battery-nodejs.js** - Node.js script for integration with your agent
3. **battery-data.json** - Output file with all collected data

## ⚠️ Common Issues & Solutions

| Issue | Solution |
|-------|----------|
| Win32_Battery returns null values | Use root\WMI namespace instead |
| Access denied errors | Usually works without admin rights |
| No battery detected | Check if laptop has removable battery |
| PowerCfg fails | Requires Windows 8 or newer |

## 🎯 For Your QC System

### Recommended Data to Collect
1. **Battery Health %** - Primary metric for QC pass/fail
2. **Cycle Count** - Indicates battery age
3. **Current Charge %** - For testing consistency
4. **Chemistry Type** - For recycling/disposal

### Pass/Fail Criteria
- **Pass**: Battery health ≥ 80%
- **Warning**: Battery health 70-79%
- **Fail**: Battery health < 70%

### Integration with Agent
```javascript
// Add to your existing agent
const batteryHealth = (fullCapacity / designCapacity) * 100;
const qcStatus = batteryHealth >= 80 ? 'PASS' : 'FAIL';
```

## 📌 Key Takeaways

1. **Battery APIs are universal** - Same across HP, Dell, Lenovo
2. **Use root\WMI namespace** - More reliable than Win32_Battery
3. **PowerCfg is most complete** - But requires file generation
4. **Health calculation is simple** - Just two values needed
5. **80% threshold is standard** - Industry-wide replacement point