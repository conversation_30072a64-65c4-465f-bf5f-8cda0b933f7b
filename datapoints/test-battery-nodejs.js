// Node.js Battery Data Collection Script
// Works with your existing agent setup
// Run: node test-battery-nodejs.js

const { exec } = require('child_process');
const util = require('util');
const fs = require('fs');
const execPromise = util.promisify(exec);

// Helper function to safely parse PowerShell output
function safeParse(value, defaultValue = null) {
  if (!value || value.trim() === '' || value.trim().toLowerCase() === 'null') {
    return defaultValue;
  }
  const parsed = parseInt(value.trim());
  return isNaN(parsed) ? defaultValue : parsed;
}

async function getBatteryData() {
  console.log('=== BATTERY DATA COLLECTION (Node.js) ===\n');
  
  const batteryData = {
    timestamp: new Date().toISOString(),
    basic: {},
    health: {},
    system: {},
    errors: []
  };

  try {
    // 1. Basic Battery Info (PowerShell via Node.js)
    console.log('1. Getting basic battery status...');
    try {
      const basicCmd = `powershell -Command "Get-WmiObject Win32_Battery | Select-Object EstimatedChargeRemaining, BatteryStatus, Status, Chemistry, DeviceID | ConvertTo-Json"`;
      const { stdout: basicOut } = await execPromise(basicCmd);
      
      if (basicOut && basicOut.trim()) {
        const basicInfo = JSON.parse(basicOut);
        batteryData.basic = {
          chargePercent: basicInfo.EstimatedChargeRemaining || 0,
          batteryStatus: basicInfo.BatteryStatus || 'Unknown',
          status: basicInfo.Status || 'Unknown',
          chemistry: basicInfo.Chemistry || 'Unknown',
          deviceId: basicInfo.DeviceID || 'Unknown'
        };
        console.log(`   Charge: ${basicInfo.EstimatedChargeRemaining || 0}%`);
        console.log(`   Status: ${basicInfo.Status || 'Unknown'}`);
        console.log(`   Battery Status Code: ${basicInfo.BatteryStatus} (1=Other, 2=Unknown, 3=FullyCharged, 4=Low, 5=Critical, 6=Charging)\n`);
      } else {
        console.log('   No battery detected\n');
        batteryData.errors.push('No battery detected');
      }
    } catch (err) {
      console.log(`   Error getting basic battery info: ${err.message}\n`);
      batteryData.errors.push(`Basic battery info: ${err.message}`);
    }

    // 2. Design Capacity
    console.log('2. Getting design capacity...');
    try {
      const designCmd = `powershell -Command "(Get-WmiObject -Namespace root\\WMI -Class BatteryStaticData -ErrorAction SilentlyContinue).DesignedCapacity"`;
      const { stdout: designOut } = await execPromise(designCmd);
      const designCapacity = safeParse(designOut);
      batteryData.health.designCapacity = designCapacity;
      console.log(`   Design Capacity: ${designCapacity || 'Not available'} mWh\n`);
    } catch (err) {
      console.log(`   Design Capacity: Error - ${err.message}\n`);
      batteryData.errors.push(`Design capacity: ${err.message}`);
    }

    // 3. Full Charge Capacity
    console.log('3. Getting full charge capacity...');
    try {
      const fullCmd = `powershell -Command "(Get-WmiObject -Namespace root\\WMI -Class BatteryFullChargedCapacity -ErrorAction SilentlyContinue).FullChargedCapacity"`;
      const { stdout: fullOut } = await execPromise(fullCmd);
      const fullCapacity = safeParse(fullOut);
      batteryData.health.fullChargeCapacity = fullCapacity;
      console.log(`   Full Charge Capacity: ${fullCapacity || 'Not available'} mWh\n`);
    } catch (err) {
      console.log(`   Full Charge Capacity: Error - ${err.message}\n`);
      batteryData.errors.push(`Full charge capacity: ${err.message}`);
    }

    // 4. Cycle Count
    console.log('4. Getting cycle count...');
    try {
      const cycleCmd = `powershell -Command "(Get-WmiObject -Namespace root\\WMI -Class BatteryCycleCount -ErrorAction SilentlyContinue).CycleCount"`;
      const { stdout: cycleOut } = await execPromise(cycleCmd);
      const cycleCount = safeParse(cycleOut);
      batteryData.health.cycleCount = cycleCount;
      console.log(`   Cycle Count: ${cycleCount || 'Not available'}\n`);
    } catch (err) {
      console.log(`   Cycle Count: Error - ${err.message}\n`);
      batteryData.errors.push(`Cycle count: ${err.message}`);
    }

    // 5. Calculate Health
    const designCapacity = batteryData.health.designCapacity;
    const fullCapacity = batteryData.health.fullChargeCapacity;
    
    if (designCapacity && fullCapacity && designCapacity > 0) {
      const healthPercent = Math.round((fullCapacity / designCapacity) * 100 * 100) / 100;
      const degradedPercent = Math.round((100 - healthPercent) * 100) / 100;
      
      batteryData.health.healthPercent = healthPercent;
      batteryData.health.degradedPercent = degradedPercent;
      
      let healthStatus;
      if (healthPercent >= 80) {
        healthStatus = 'GOOD';
      } else if (healthPercent >= 50) {
        healthStatus = 'FAIR';
      } else {
        healthStatus = 'POOR';
      }
      batteryData.health.healthStatus = healthStatus;
      
      console.log('5. BATTERY HEALTH ANALYSIS');
      console.log(`   Health: ${healthPercent}%`);
      console.log(`   Degraded: ${degradedPercent}%`);
      console.log(`   Status: ${healthStatus}\n`);
    }

    // 6. System Info
    console.log('6. Getting system information...');
    try {
      const sysCmd = `powershell -Command "Get-WmiObject Win32_ComputerSystem | Select-Object Manufacturer, Model | ConvertTo-Json"`;
      const { stdout: sysOut } = await execPromise(sysCmd);
      const sysInfo = JSON.parse(sysOut);
      
      const serialCmd = `powershell -Command "(Get-WmiObject Win32_BIOS).SerialNumber"`;
      const { stdout: serialOut } = await execPromise(serialCmd);
      
      batteryData.system = {
        manufacturer: sysInfo.Manufacturer || 'Unknown',
        model: sysInfo.Model || 'Unknown',
        serialNumber: serialOut.trim() || 'Unknown'
      };
      
      console.log(`   Manufacturer: ${sysInfo.Manufacturer || 'Unknown'}`);
      console.log(`   Model: ${sysInfo.Model || 'Unknown'}`);
      console.log(`   Serial: ${serialOut.trim() || 'Unknown'}\n`);
    } catch (err) {
      console.log(`   System Info: Error - ${err.message}\n`);
      batteryData.errors.push(`System info: ${err.message}`);
    }

    // 7. Generate PowerCfg Report
    console.log('7. Generating full battery report...');
    const reportPath = process.env.USERPROFILE + '\\battery-report-nodejs.html';
    await execPromise(`powercfg /batteryreport /output "${reportPath}"`);
    console.log(`   Report saved to: ${reportPath}\n`);

    // Save JSON data
    const jsonPath = './battery-data.json';
    fs.writeFileSync(jsonPath, JSON.stringify(batteryData, null, 2));
    console.log(`\n=== DATA SAVED TO ${jsonPath} ===`);
    
    // Show errors summary if any
    if (batteryData.errors.length > 0) {
      console.log('\n⚠️  ERRORS ENCOUNTERED:');
      batteryData.errors.forEach(err => console.log(`   - ${err}`));
    }
    
    return batteryData;

  } catch (error) {
    console.error('\nFATAL ERROR:', error.message);
    batteryData.errors.push(`Fatal: ${error.message}`);
    // Still try to save what we have
    try {
      const jsonPath = './battery-data.json';
      fs.writeFileSync(jsonPath, JSON.stringify(batteryData, null, 2));
    } catch (saveErr) {
      console.error('Could not save data:', saveErr.message);
    }
    return batteryData;
  }
}

// Run the collection
getBatteryData().then(data => {
  console.log('\n=== COLLECTION COMPLETE ===');
  console.log('\nKey Findings:');
  console.log('- Battery health metrics are universal across HP, Dell, Lenovo');
  console.log('- Use root\\WMI namespace for reliable data (not root\\cimv2)');
  console.log('- PowerCfg report provides most comprehensive information');
  console.log('\nPress Ctrl+C to exit...');
});