# Comprehensive QC System for Refurbished Laptops

A scalable quality control system that automates Windows deployment, battery testing, hardware verification, and reporting - processing 50-100 laptops daily with zero manual bay assignment.

## Components

### FOG Server (PXE Deployment)
- Automated Windows installation
- Agent pre-deployment
- Model-specific imaging

### Agent (Windows)
- Automatic bay detection via power patterns
- Battery and hardware testing
- Zero manual configuration

### Dashboard (Web)
- Real-time monitoring for 30+ laptops
- Automatic bay assignment logic
- Power control interface
- Scalable to 100+ units/day

### Bridge Service (Local)
- Controls 30-channel relay module
- Executes power patterns for bay detection
- Local fallback UI on port 8080

## 🚀 Key Innovation: Automatic Bay Assignment

Traditional QC systems require manual bay entry for each laptop. Our system uses power cycling patterns to automatically detect which bay a laptop is in - completely eliminating manual data entry and enabling continuous workflow.

## Quick Start

### Initial Setup
```bash
# Install all dependencies at once
npm run install:all
```

### Development

**Run everything at once:**
```bash
npm run dev:all
```

**Or run individually:**
```bash
npm run dev:agent      # Agent only
npm run dev:dashboard  # Dashboard only
npm run dev:bridge     # Bridge only
```

### Production Build
```bash
npm run build          # Builds dashboard for production
npm run build:agent    # Builds agent EXE
```

### Deployment to Vercel

1. Push code to GitHub
2. Import project on Vercel
3. Use these settings:
   - Root Directory: `./` (leave as default)
   - Build Command: (auto-detected from vercel.json)
   - Output Directory: (auto-detected)
4. Add environment variables:
   - `VITE_SUPABASE_URL`
   - `VITE_SUPABASE_ANON_KEY`

## Documentation

- [CLAUDE.md](CLAUDE.md) - Living project document (READ FIRST)
- [Project Summary](docs/PROJECT_SUMMARY.md) - System overview and scale targets
- [Implementation Plan](docs/IMPLEMENTATION_PLAN.md) - Technical architecture
- [Task List](docs/TASK_LIST.md) - Development phases and priorities
- [Architecture Guide](docs/ARCHITECTURE.md) - Code organization (non-technical)

## Environment Setup

Create `.env` files in both agent and dashboard directories with Supabase credentials.

## Security Note

This system is for internal use only. Agent applications should not be distributed outside the organization.