﻿<!DOCTYPE html>
<!-- saved from url=(0016)http://localhost -->
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:ms="urn:schemas-microsoft-com:xslt" xmlns:bat="http://schemas.microsoft.com/battery/2012" xmlns:js="http://microsoft.com/kernel"><head><meta http-equiv="X-UA-Compatible" content="IE=edge"/><meta name="ReportUtcOffset" content="+5:30"/><title>Battery report</title><style type="text/css">

      body {

          font-family: Segoe UI Light;

          letter-spacing: 0.02em;

          background-color: #181818;

          color: #F0F0F0;

          margin-left: 5.5em;

      }



      h1 {

          color: #11D8E8;

          font-size: 42pt;

      }



      h2 {

          font-size: 15pt;

          color: #11EEF4;

          margin-top: 4em;

          margin-bottom: 0em;

          letter-spacing: 0.08em;

      }



      td {

          padding-left: 0.3em;

          padding-right: 0.3em;

      }



      .nobatts {

          font-family: Segoe UI Semibold;

          background: #272727;

          color: #ACAC60;

          font-size: 13pt;

          padding-left:0.4em;

          padding-right:0.4em;

          padding-top:0.3em;

          padding-bottom:0.3em;

      }



      .explanation {

          color: #777777;

          font-size: 12pt;

          margin-bottom: 1em;

      }



      .explanation2 {

          color: #777777;

          font-size: 12pt;

          margin-bottom: 0.1em;

      }



      table {

          border-width: 0;

          table-layout: fixed;

          font-family: Segoe UI Light;

          letter-spacing: 0.02em;

          background-color: #181818;

          color: #f0f0f0;

      }



      .even { background: #272727; }

      .odd { background: #1E1E1E; }

      .even.suspend { background: #1A1A28; }

      .odd.suspend { background: #1A1A2C; }



      thead {

          font-family: Segoe UI Semibold;

          font-size: 85%;

          color: #BCBCBC;

      }



      text {

          font-size: 12pt;

          font-family: Segoe UI Light;

          fill: #11EEF4;

      }



      .centered { text-align: center; }



      .label {

          font-family: Segoe UI Semibold;

          font-size: 85%;

          color: #BCBCBC;

      }



      .dc.even { background: #40182C; }

      .dc.odd { background: #30141F; }



      td.colBreak {

          padding: 0;

          width: 0.15em;

      }



      td.state { text-align: center; }



      td.hms {

          font-family: Segoe UI Symbol;

          text-align: right;

          padding-right: 3.4em;

      }



      td.dateTime { font-family: Segoe UI Symbol; }

      td.nullValue { text-align: center; }



      td.percent {

          font-family: Segoe UI Symbol;

          text-align: right;

          padding-right: 2.5em;

      }



      col:first-child { width: 13em; }

      col.col2 { width: 10.4em; }

      col.percent { width: 7.5em; }



      td.mw {

          text-align: right;

          padding-right: 2.5em;

      }



      td.acdc { text-align: center; }



      span.date {

          display: inline-block;

          width: 5.5em;

      }



      span.time {

          text-align: right;

          width: 4.2em;

          display: inline-block;

      }



      text { font-family: Segoe UI Symbol; }



      .noncontigbreak {

          height: 0.3em;

          background-color: #1A1A28;

      }

    </style><script type="text/javascript">

    // Formats a number using the current locale (to handle the 1000's separator).

    // The result is rounded so no decimal point is shown.

    function numberToLocaleString(value) {

        var localeString = Math.round(parseFloat(value + '')).toLocaleString();

        return localeString.substring(0, localeString.indexOf('.'));

    }



    function padLeft(number, length) {

        var str = '' + number;

        while (str.length < length) {

            str = '0' + str;

        }



        return str;

    }



    // Returns the number of milliseconds between 2 date-times represented as strings.

    function msBetween(startTime, endTime) {

        return startTime > endTime

               ? msBetween(endTime, startTime)

               : parseDateTime(endTime) - parseDateTime(startTime);

    }



    var dateFormat = /(\d{4})-(\d{2})-(\d{2})[T](\d{2}):(\d{2}):(\d{2})/



    // Parses a date-time string and returns a Date (i.e. number of milliseconds)

    function parseDateTime(value) {

        if (!value) {

            return 0;

        }



        var match = dateFormat.exec(value)

        if (!match) {

            return 0;

        }



        return Date.parse(match[1] + '/' + match[2] + '/' +

                          match[3] + ' ' + match[4] + ':' +

                          match[5] + ':' + match[6])

    }



    // Parses just the date portion of a date-time string and returns a Date

    // (i.e. number of milliseconds)

    function parseDate(value) {

        if (!value) {

            return 0;

        }



        var match = dateFormat.exec(value)

        if (!match) {

            return 0;

        }



        return Date.parse(match[1] + '/' + match[2] + '/' + match[3])

    }



    var durationFormat = /P((\d+)D)?T((\d+)H)?((\d+)M)?(\d+)S/



    // Convert a string of the form P10DT1H15M40S to a count of milliseconds

    function parseDurationToMs(value) {

        var match = durationFormat.exec(value)

        if (!match) {

            return 0

        }



        var days = parseInt(match[2] || '0');

        var hrs = parseInt(match[4] || '0');

        var mins = parseInt(match[6] || '0');

        var secs = parseInt(match[7] || '0');

        return ((((((days * 24) + hrs) * 60) + mins) * 60) +  secs) * 1000;

    }



    // Converts milliseconds to days

    function msToDays(ms) {

        return (ms / 1000 / 60 / 60 / 24);

    }



    function daysToMs(days) {

        return (days * 24 * 60 * 60 * 1000);

    }



    // Formats a number of milliseconds as h:mm:ss

    function formatDurationMs(value) {

        var ms = parseInt(value);

        var secs = ms / 1000;

        var mins = secs / 60;

        var hrs = Math.floor(mins / 60);

        mins = Math.floor(mins % 60);

        secs = Math.floor(secs % 60);

        return hrs + ':' + padLeft(mins,2) + ':' + padLeft(secs,2);

    }



    // Converts a millisecond timestamp to a day and month string

    // Note: dayOffset is forward from date.

    function dateToDayAndMonth(ms, dayOffset) {

        var adjustedDate = new Date(ms + (dayOffset * 24 * 60 * 60 * 1000));

        return padLeft(adjustedDate.getMonth() + 1, 2) + "-" +

               padLeft(adjustedDate.getDate(), 2);

    }



    // Takes a millisecond timestamp and returns a new millisecond timestamp

    // rounded down to the current day.

    function dateFloor(ms) {

        var dt = new Date(ms);

        return Date.parse(dt.getFullYear() + '/' + (dt.getMonth() + 1) + '/' + dt.getDate());

    }

    

    Timegraph = {

        axisTop: 9.5,

        axisRight: 24.5,

        axisBottom: 25.5,

        axisLeft: 25.5,

        ticks: 10,



        // Maximum number of 24 hour ticks for showing 12 and 6 hour ticks



        ticks12Hour: 8,

        ticks6Hour: 4,



        // Shading



        lineColor: "#B82830",

        shadingColor: "#4d1d35",



        precompute: function (graph) {

            var canvas = graph.canvas;

            var data = graph.data;

            var min = 0;

            var max = 0;



            graph.height = canvas.height - Timegraph.axisTop - Timegraph.axisBottom;

            graph.width = canvas.width - Timegraph.axisLeft - Timegraph.axisRight;

            for (var i = 0; i < data.length; i++) {

                data[i].t0 = parseDateTime(data[i].x0);

                data[i].t1 = parseDateTime(data[i].x1);



                if (i == 0) {

                    min = data[i].t0;

                    max = data[i].t1;

                }



                if (data[i].t0 < min) {

                    min = data[i].t0;

                }



                if (data[i].t1 > max) {

                    max = data[i].t1;

                }



                data[i].yy0 =

                    Timegraph.axisTop + graph.height - data[i].y0 * graph.height;



                data[i].yy1 =

                    Timegraph.axisTop + graph.height - data[i].y1 * graph.height;

            }



            if (graph.startTime != null) {

                graph.startMs = parseDateTime(graph.startTime);



            } else {

                graph.startMs = min;

            }



            graph.endMs = max;

            graph.durationMs = max - min;

        },



        drawFrame: function (graph) {

            var canvas = graph.canvas;

            var context = graph.context;



            graph.width =

                canvas.width - Timegraph.axisRight - Timegraph.axisLeft;



            graph.height =

                canvas.height - Timegraph.axisTop - Timegraph.axisBottom;



            context.beginPath();

            context.moveTo(Timegraph.axisLeft, Timegraph.axisTop);

            context.lineTo(Timegraph.axisLeft + graph.width,

                           Timegraph.axisTop);



            context.lineTo(Timegraph.axisLeft + graph.width,

                           Timegraph.axisTop + graph.height);



            context.lineTo(Timegraph.axisLeft,

                           Timegraph.axisTop + graph.height);



            context.lineTo(Timegraph.axisLeft, Timegraph.axisTop);

            context.strokeStyle = "#c0c0c0";

            context.stroke();

        },



        drawRange: function (graph) {

            var canvas = graph.canvas;

            var context = graph.context;



            context.font = "12pt Segoe UI";

            context.fillStyle = "#00b0f0";

            context.fillText("%", 0, Timegraph.axisTop + 5, Timegraph.axisLeft);



            var tickSpacing = graph.height / 10;

            var offset = Timegraph.axisTop + tickSpacing;

            var tickValue = 90;

            for (var i = 0; i < 9; i++) {

                context.beginPath();

                context.moveTo(Timegraph.axisLeft, offset);

                context.lineTo(Timegraph.axisLeft + graph.width,

                               offset);



                context.stroke();

                context.fillText(tickValue.toString(),

                                 0,

                                 offset + 5,

                                 Timegraph.axisLeft);



                offset += tickSpacing;

                tickValue -= 10;

            }

        },



        drawDomain: function (graph, start, end) {

            var canvas = graph.canvas;

            var context = graph.context;

            var data = graph.data;

            var duration = end - start;

            if ((end < start)) {

                return;

            }



            var startDay = dateFloor(start);

            var t0 = startDay;

            var t1 = dateFloor(end);

            var dayOffset = 0;

            if (start > t0) {

                t0 = t0 + daysToMs(1);

                dayOffset++;

            }



            if (t0 >= t1) {

                return;

            }



            var increment =

                Math.max(Math.floor((t1 - t0) / daysToMs(Timegraph.ticks)), 1);



            var incrementMs = daysToMs(increment);

            var spacing = (incrementMs / duration) * graph.width;

            var offset = (t0 - start) / duration;

            var ticksCount = Math.floor((t1 - t0) / incrementMs);

            for (offset = offset * graph.width + Timegraph.axisLeft;

                 offset < (graph.width + Timegraph.axisLeft);

                 offset += spacing) {



                context.beginPath();

                context.moveTo(offset, Timegraph.axisTop);

                context.lineTo(offset, Timegraph.axisTop + graph.height);

                context.stroke();

                context.fillText(dateToDayAndMonth(startDay, dayOffset),

                                 offset,

                                 Timegraph.axisTop + graph.height + 15,

                                 spacing);



                dayOffset += increment;

            }

        },



        plot: function (graph, start, end) {

            var canvas = graph.canvas;

            var context = graph.context

            var data = graph.data;



            if ((end < start)) {

                return;

            }



            var duration = end - start;

            Timegraph.drawDomain(graph, start, end);

            context.fillStyle = Timegraph.shadingColor;

            for (var i = 0; i < data.length - 1; i++) {

                if ((data[i].t0 < start) || (data[i].t0 > end) ||

                    (data[i].t1 > end)) {



                    continue;

                }



                var x1 = (data[i].t0 - start) / duration;

                x1 = x1 * graph.width + Timegraph.axisLeft;



                var x2 = (data[i].t1 - start) / duration;

                x2 = x2 * graph.width + Timegraph.axisLeft;



                context.globalAlpha = 0.3;

                context.fillRect(x1, Timegraph.axisTop, (x2 - x1), graph.height);

                context.globalAlpha = 1;

                context.beginPath();

                context.strokeStyle = Timegraph.lineColor;

                context.lineWidth = 1.5;

                context.moveTo(x1, data[i].yy0);

                context.lineTo(x2, data[i].yy1);

                context.stroke();

            }

        },



        draw: function (graph) {

            var canvas = document.getElementById(graph.element);

            if (canvas == null) {

                return;

            }



            var context = canvas.getContext('2d');

            if (context == null) {

                return;

            }



            graph.width = 0;

            graph.height = 0;

            graph.context = context;

            graph.canvas = canvas;



            Timegraph.precompute(graph);

            Timegraph.drawFrame(graph);

            Timegraph.drawRange(graph);

            Timegraph.plot(graph, graph.startMs, graph.endMs);

        }

    };

    

    drainGraphData = [

    { x0: "2025-06-16T14:52:15", x1: "2025-06-16T15:14:53", y0: 0.4784, y1: 0.3020088888888889 }, 

{ x0: "2025-06-16T15:14:53", x1: "2025-06-16T15:15:06", y0: 0.3020088888888889, y1: 0.3008 }, 

{ x0: "2025-06-16T15:15:06", x1: "2025-06-16T15:16:32", y0: 0.3008, y1: 0.2972088888888889 }, 

{ x0: "2025-06-16T15:16:32", x1: "2025-06-16T15:29:57", y0: 0.2972088888888889, y1: 0.1980088888888889 }, 

{ x0: "2025-06-16T15:29:57", x1: "2025-06-16T15:30:13", y0: 0.1980088888888889, y1: 0.1968 }, 

{ x0: "2025-06-16T15:30:13", x1: "2025-06-16T15:35:35", y0: 0.1968, y1: 0.18439111111111112 }, 

{ x0: "2025-06-16T16:48:17", x1: "2025-06-16T16:49:32", y0: 0.5955911111111111, y1: 0.5984 }, 

{ x0: "2025-06-16T16:49:32", x1: "2025-06-16T17:19:48", y0: 0.5984, y1: 0.5443911111111112 }, 

{ x0: "2025-06-16T17:19:48", x1: "2025-06-16T17:19:53", y0: 0.5443911111111112, y1: 0.5443911111111112 }, 

{ x0: "2025-06-17T12:30:03", x1: "2025-06-17T12:30:38", y0: 0.5296, y1: 0.5256177777777777 }, 

{ x0: "2025-06-17T12:30:38", x1: "2025-06-17T12:41:29", y0: 0.5256177777777777, y1: 0.4620088888888889 }, 

{ x0: "2025-06-17T12:41:29", x1: "2025-06-17T12:41:29", y0: 0.4620088888888889, y1: 0.4608 }, 

{ x0: "2025-06-17T12:41:29", x1: "2025-06-17T13:05:05", y0: 0.4608, y1: 0.4144 }, 

{ x0: "2025-06-17T13:05:05", x1: "2025-06-17T13:05:11", y0: 0.4144, y1: 0.41400888888888887 }, 

{ x0: "2025-06-17T13:55:38", x1: "2025-06-17T13:56:13", y0: 0.40401777777777775, y1: 0.3987911111111111 }, 

{ x0: "2025-06-17T13:56:13", x1: "2025-06-17T13:57:00", y0: 0.3987911111111111, y1: 0.39441777777777775 }, 

{ x0: "2025-06-17T13:57:00", x1: "2025-06-17T13:58:03", y0: 0.39441777777777775, y1: 0.38481777777777776 }, 

{ x0: "2025-06-17T13:58:03", x1: "2025-06-17T13:59:19", y0: 0.38481777777777776, y1: 0.3744 }, 

{ x0: "2025-06-17T13:59:19", x1: "2025-06-17T14:00:06", y0: 0.3744, y1: 0.3644088888888889 }, 

{ x0: "2025-06-17T14:00:06", x1: "2025-06-17T14:00:45", y0: 0.3644088888888889, y1: 0.3548088888888889 }, 

{ x0: "2025-06-17T14:00:45", x1: "2025-06-17T14:01:26", y0: 0.3548088888888889, y1: 0.3448177777777778 }, 

{ x0: "2025-06-17T14:01:26", x1: "2025-06-17T14:02:18", y0: 0.3448177777777778, y1: 0.3347911111111111 }, 

{ x0: "2025-06-17T14:02:18", x1: "2025-06-17T14:03:07", y0: 0.3347911111111111, y1: 0.3248 }, 

{ x0: "2025-06-17T14:03:07", x1: "2025-06-17T14:03:56", y0: 0.3248, y1: 0.3148088888888889 }, 

{ x0: "2025-06-17T14:03:56", x1: "2025-06-17T14:04:41", y0: 0.3148088888888889, y1: 0.3048177777777778 }, 

{ x0: "2025-06-17T14:04:41", x1: "2025-06-17T14:05:23", y0: 0.3048177777777778, y1: 0.2947911111111111 }, 

{ x0: "2025-06-17T14:05:23", x1: "2025-06-17T14:06:07", y0: 0.2947911111111111, y1: 0.2835911111111111 }, 

{ x0: "2025-06-17T14:06:07", x1: "2025-06-17T14:06:40", y0: 0.2835911111111111, y1: 0.27441777777777776 }, 

{ x0: "2025-06-17T14:06:40", x1: "2025-06-17T14:07:19", y0: 0.27441777777777776, y1: 0.26439111111111113 }, 

{ x0: "2025-06-17T14:07:19", x1: "2025-06-17T14:07:59", y0: 0.26439111111111113, y1: 0.25479111111111113 }, 

{ x0: "2025-06-17T14:07:59", x1: "2025-06-17T14:08:43", y0: 0.25479111111111113, y1: 0.2432 }, 

{ x0: "2025-06-17T14:08:43", x1: "2025-06-17T14:09:18", y0: 0.2432, y1: 0.23399111111111112 }, 

{ x0: "2025-06-17T14:09:18", x1: "2025-06-17T14:09:55", y0: 0.23399111111111112, y1: 0.22481777777777778 }, 

{ x0: "2025-06-17T14:09:55", x1: "2025-06-17T14:11:03", y0: 0.22481777777777778, y1: 0.2144 }, 

{ x0: "2025-06-17T14:11:03", x1: "2025-06-17T14:12:38", y0: 0.2144, y1: 0.2064 }, 

{ x0: "2025-06-17T14:12:38", x1: "2025-06-17T14:12:38", y0: 0.2064, y1: 0.2064 }, 

{ x0: "2025-06-17T14:12:38", x1: "2025-06-17T14:13:04", y0: 0.2064, y1: 0.2048 }, 

{ x0: "2025-06-17T14:13:04", x1: "2025-06-17T14:18:16", y0: 0.2048, y1: 0.19441777777777777 }, 

{ x0: "2025-06-17T14:18:16", x1: "2025-06-17T14:18:22", y0: 0.19441777777777777, y1: 0.19399111111111111 }, 



    ];

    

    function main() {

        Timegraph.draw({

            element: "drain-graph",

            data: drainGraphData,

            startTime: "2025-06-14T14:53:52",

            endTime: "2025-06-17T14:53:52",

        });

    }



    if (window.addEventListener != null) {

        window.addEventListener("load", main, false);



    } else if (window.attachEvent != null) {

        window.attachEvent("onload", main);

    }

    </script></head><body><h1>

      Battery report

    </h1><table style="margin-bottom: 6em;"><col/><tr><td class="label">

          COMPUTER NAME

        </td><td>DESKTOP-NI63215</td></tr><tr><td class="label">

          SYSTEM PRODUCT NAME

        </td><td>Dell Inc. Latitude 3420</td></tr><tr><td class="label">

          BIOS

        </td><td>1.38.0 09/06/2024</td></tr><tr><td class="label">

          OS BUILD

        </td><td>19041.1.amd64fre.vb_release.191206-1406</td></tr><tr><td class="label">

          PLATFORM ROLE

        </td><td>Mobile</td></tr><tr><td class="label">

          CONNECTED STANDBY

        </td><td>Supported</td></tr><tr><td class="label">

          REPORT TIME

        </td><td class="dateTime"><span class="date">2025-06-17 </span><span class="time">14:53:52</span></td></tr></table><h2>

      Installed batteries

    </h2><div class="explanation">

      Information about each currently installed battery

    </div><table><colgroup><col style="width: 15em;"/><col style="width: 14em;"/></colgroup><thead><tr><td> </td><td>

                  BATTERY

                  1</td></tr></thead><tr><td><span class="label">NAME</span></td><td>DELL FH3K216</td></tr><tr><td><span class="label">MANUFACTURER</span></td><td>SWD-COS3.661</td></tr><tr><td><span class="label">SERIAL NUMBER</span></td><td>8158</td></tr><tr><td><span class="label">CHEMISTRY</span></td><td>LiP</td></tr><tr><td><span class="label">DESIGN CAPACITY</span></td><td>41,006 mWh

      </td></tr><tr style="height:0.4em;"></tr><tr><td><span class="label">FULL CHARGE CAPACITY</span></td><td>28,125 mWh

      </td></tr><tr><td><span class="label">CYCLE COUNT</span></td><td>

        -

      </td></tr></table><h2>Recent usage</h2><div class="explanation">

      Power states over the last 3 days

    </div><table><colgroup><col/><col class="col2"/><col style="width: 4.2em;"/><col class="percent"/><col style="width: 11em;"/></colgroup><thead><tr><td>

            START TIME

          </td><td class="centered">

            STATE

          </td><td class="centered">

            SOURCE

          </td><td colspan="2" class="centered">

            CAPACITY REMAINING

          </td></tr></thead><tr class="even dc 1"><td class="dateTime"><span class="date">2025-06-16 </span><span class="time">14:52:15</span></td><td class="state">

        Active

      </td><td class="acdc">

        Battery

      </td><td class="percent">48 %

        </td><td class="mw">13,455 mWh

        </td></tr><tr class="odd dc 2"><td class="dateTime"><span class="date"> </span><span class="time">15:14:53</span></td><td class="state">

            Connected standby

          </td><td class="acdc">

        Battery

      </td><td class="percent">30 %

        </td><td class="mw">8,494 mWh

        </td></tr><tr class="even dc 3"><td class="dateTime"><span class="date"> </span><span class="time">15:16:32</span></td><td class="state">

        Active

      </td><td class="acdc">

        Battery

      </td><td class="percent">30 %

        </td><td class="mw">8,359 mWh

        </td></tr><tr class="odd dc 4"><td class="dateTime"><span class="date"> </span><span class="time">15:29:57</span></td><td class="state">

            Connected standby

          </td><td class="acdc">

        Battery

      </td><td class="percent">20 %

        </td><td class="mw">5,569 mWh

        </td></tr><tr class="even  5"><td class="dateTime"><span class="date"> </span><span class="time">15:35:54</span></td><td class="state">

            Connected standby

          </td><td class="acdc">

        AC

      </td><td class="percent">18 %

        </td><td class="mw">5,175 mWh

        </td></tr><tr class="odd suspend 6"><td class="dateTime"><span class="date"> </span><span class="time">15:35:59</span></td><td class="state">

        Suspended

      </td><td class="acdc"></td><td class="percent">18 %

        </td><td class="mw">5,175 mWh

        </td></tr><tr class="even  7"><td class="dateTime"><span class="date"> </span><span class="time">15:37:20</span></td><td class="state">

            Connected standby

          </td><td class="acdc">

        AC

      </td><td class="percent">19 %

        </td><td class="mw">5,254 mWh

        </td></tr><tr class="odd  8"><td class="dateTime"><span class="date"> </span><span class="time">15:37:55</span></td><td class="state">

        Active

      </td><td class="acdc">

        AC

      </td><td class="percent">19 %

        </td><td class="mw">5,389 mWh

        </td></tr><tr class="even  9"><td class="dateTime"><span class="date"> </span><span class="time">15:37:55</span></td><td class="state">

            Connected standby

          </td><td class="acdc">

        AC

      </td><td class="percent">19 %

        </td><td class="mw">5,389 mWh

        </td></tr><tr class="odd  10"><td class="dateTime"><span class="date"> </span><span class="time">15:40:49</span></td><td class="state">

        Active

      </td><td class="acdc">

        AC

      </td><td class="percent">21 %

        </td><td class="mw">6,041 mWh

        </td></tr><tr class="even  11"><td class="dateTime"><span class="date"> </span><span class="time">15:51:36</span></td><td class="state">

            Connected standby

          </td><td class="acdc">

        AC

      </td><td class="percent">30 %

        </td><td class="mw">8,561 mWh

        </td></tr><tr class="odd  12"><td class="dateTime"><span class="date"> </span><span class="time">15:52:30</span></td><td class="state">

        Active

      </td><td class="acdc">

        AC

      </td><td class="percent">31 %

        </td><td class="mw">8,764 mWh

        </td></tr><tr class="even  13"><td class="dateTime"><span class="date"> </span><span class="time">16:02:37</span></td><td class="state">

            Connected standby

          </td><td class="acdc">

        AC

      </td><td class="percent">39 %

        </td><td class="mw">10,935 mWh

        </td></tr><tr class="odd dc 14"><td class="dateTime"><span class="date"> </span><span class="time">16:49:32</span></td><td class="state">

            Connected standby

          </td><td class="acdc">

        Battery

      </td><td class="percent">60 %

        </td><td class="mw">16,830 mWh

        </td></tr><tr class="even suspend 15"><td class="dateTime"><span class="date"> </span><span class="time">17:19:53</span></td><td class="state">

        Suspended

      </td><td class="acdc"></td><td class="percent">54 %

        </td><td class="mw">15,311 mWh

        </td></tr><tr class="odd dc 16"><td class="dateTime"><span class="date">2025-06-17 </span><span class="time">12:30:03</span></td><td class="state">

            Connected standby

          </td><td class="acdc">

        Battery

      </td><td class="percent">53 %

        </td><td class="mw">14,895 mWh

        </td></tr><tr class="even dc 17"><td class="dateTime"><span class="date"> </span><span class="time">12:30:38</span></td><td class="state">

        Active

      </td><td class="acdc">

        Battery

      </td><td class="percent">53 %

        </td><td class="mw">14,783 mWh

        </td></tr><tr class="odd dc 18"><td class="dateTime"><span class="date"> </span><span class="time">12:41:29</span></td><td class="state">

            Connected standby

          </td><td class="acdc">

        Battery

      </td><td class="percent">46 %

        </td><td class="mw">12,960 mWh

        </td></tr><tr class="even suspend 19"><td class="dateTime"><span class="date"> </span><span class="time">13:05:11</span></td><td class="state">

        Suspended

      </td><td class="acdc"></td><td class="percent">41 %

        </td><td class="mw">11,644 mWh

        </td></tr><tr class="odd dc 20"><td class="dateTime"><span class="date"> </span><span class="time">13:55:38</span></td><td class="state">

            Connected standby

          </td><td class="acdc">

        Battery

      </td><td class="percent">40 %

        </td><td class="mw">11,363 mWh

        </td></tr><tr class="even dc 21"><td class="dateTime"><span class="date"> </span><span class="time">13:56:13</span></td><td class="state">

        Active

      </td><td class="acdc">

        Battery

      </td><td class="percent">40 %

        </td><td class="mw">11,216 mWh

        </td></tr><tr class="odd dc 22"><td class="dateTime"><span class="date"> </span><span class="time">14:12:38</span></td><td class="state">

            Connected standby

          </td><td class="acdc">

        Battery

      </td><td class="percent">21 %

        </td><td class="mw">5,805 mWh

        </td></tr><tr class="even suspend 23"><td class="dateTime"><span class="date"> </span><span class="time">14:18:22</span></td><td class="state">

        Suspended

      </td><td class="acdc"></td><td class="percent">19 %

        </td><td class="mw">5,456 mWh

        </td></tr><tr class="odd dc 24"><td class="dateTime"><span class="date"> </span><span class="time">14:39:27</span></td><td class="state">

            Connected standby

          </td><td class="acdc">

        Battery

      </td><td class="percent">20 %

        </td><td class="mw">5,535 mWh

        </td></tr><tr class="even  25"><td class="dateTime"><span class="date"> </span><span class="time">14:39:27</span></td><td class="state">

            Connected standby

          </td><td class="acdc">

        AC

      </td><td class="percent">20 %

        </td><td class="mw">5,535 mWh

        </td></tr><tr class="odd  26"><td class="dateTime"><span class="date"> </span><span class="time">14:39:27</span></td><td class="state">

        Active

      </td><td class="acdc">

        AC

      </td><td class="percent">20 %

        </td><td class="mw">5,535 mWh

        </td></tr><tr class="even  27"><td class="dateTime"><span class="date"> </span><span class="time">14:53:52</span></td><td class="state">

        Report generated

      </td><td class="acdc">

        AC

      </td><td class="percent">24 %

        </td><td class="mw">6,761 mWh

        </td></tr></table><h2>Battery usage</h2><div class="explanation">

      Battery drains over the last 3 days

    </div><canvas id="drain-graph" width="864" height="400"></canvas><table><colgroup><col/><col class="col2"/><col style="width: 10em;"/><col class="percent"/><col style="width: 11em;"/></colgroup><thead><tr><td>

            START TIME

          </td><td class="centered">

            STATE

          </td><td class="centered">

            DURATION

          </td><td class="centered" colspan="2">

            ENERGY DRAINED

          </td></tr></thead><tr class="even dc 1"><td class="dateTime"><span class="date">2025-06-16 </span><span class="time">14:52:15</span></td><td class="state">

        Active

      </td><td class="hms">0:22:37</td><td class="percent">18 %

        </td><td class="mw">4,961 mWh

        </td></tr><tr class="odd dc 2"><td class="dateTime"><span class="date"> </span><span class="time">15:14:53</span></td><td class="state">

            Connected standby

          </td><td class="hms">0:01:38</td><td class="nullValue">-</td><td class="mw">135 mWh

        </td></tr><tr class="even dc 3"><td class="dateTime"><span class="date"> </span><span class="time">15:16:32</span></td><td class="state">

        Active

      </td><td class="hms">0:13:25</td><td class="percent">10 %

        </td><td class="mw">2,790 mWh

        </td></tr><tr class="odd dc 4"><td class="dateTime"><span class="date"> </span><span class="time">15:29:57</span></td><td class="state">

            Connected standby

          </td><td class="hms">0:05:57</td><td class="percent">1 %

        </td><td class="mw">394 mWh

        </td></tr><tr class="noncontigbreak"><td colspan="5"> </td></tr><tr class="even dc 5"><td class="dateTime"><span class="date"> </span><span class="time">16:49:32</span></td><td class="state">

            Connected standby

          </td><td class="hms">0:30:21</td><td class="percent">5 %

        </td><td class="mw">1,519 mWh

        </td></tr><tr class="noncontigbreak"><td colspan="5"> </td></tr><tr class="odd dc 6"><td class="dateTime"><span class="date">2025-06-17 </span><span class="time">12:30:03</span></td><td class="state">

            Connected standby

          </td><td class="hms">0:00:35</td><td class="nullValue">-</td><td class="mw">112 mWh

        </td></tr><tr class="even dc 7"><td class="dateTime"><span class="date"> </span><span class="time">12:30:38</span></td><td class="state">

        Active

      </td><td class="hms">0:10:51</td><td class="percent">6 %

        </td><td class="mw">1,823 mWh

        </td></tr><tr class="odd dc 8"><td class="dateTime"><span class="date"> </span><span class="time">12:41:29</span></td><td class="state">

            Connected standby

          </td><td class="hms">0:23:41</td><td class="percent">5 %

        </td><td class="mw">1,316 mWh

        </td></tr><tr class="noncontigbreak"><td colspan="5"> </td></tr><tr class="even dc 9"><td class="dateTime"><span class="date"> </span><span class="time">13:55:38</span></td><td class="state">

            Connected standby

          </td><td class="hms">0:00:35</td><td class="percent">1 %

        </td><td class="mw">147 mWh

        </td></tr><tr class="odd dc 10"><td class="dateTime"><span class="date"> </span><span class="time">13:56:13</span></td><td class="state">

        Active

      </td><td class="hms">0:16:25</td><td class="percent">19 %

        </td><td class="mw">5,411 mWh

        </td></tr><tr class="even dc 11"><td class="dateTime"><span class="date"> </span><span class="time">14:12:38</span></td><td class="state">

            Connected standby

          </td><td class="hms">0:05:43</td><td class="percent">1 %

        </td><td class="mw">349 mWh

        </td></tr><tr class="noncontigbreak"><td colspan="5"> </td></tr><tr class="odd dc 12"><td class="dateTime"><span class="date"> </span><span class="time">14:39:27</span></td><td class="state">

            Connected standby

          </td><td class="hms">0:00:00</td><td class="nullValue">-</td><td class="nullValue">-</td></tr></table><h2>

      Usage history

    </h2><div class="explanation2">

      History of system usage on AC and battery

    </div><table><colgroup><col/><col class="col2"/><col style="width: 10em;"/><col style=""/><col style="width: 10em;"/><col style="width: 10em;"/><col style=""/></colgroup><thead><tr><td> </td><td colspan="2" class="centered">

            BATTERY DURATION

          </td><td class="colBreak"> </td><td colspan="3" class="centered">

            AC DURATION

          </td></tr><tr><td>

            PERIOD

          </td><td class="centered">

            ACTIVE

          </td><td class="centered">

            CONNECTED STANDBY

          </td><td class="colBreak"> </td><td class="centered">

            ACTIVE

          </td><td class="centered">

            CONNECTED STANDBY

          </td></tr></thead><tr class="even  1"><td class="dateTime">2025-05-13

      - 2025-05-20</td><td class="nullValue">-</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr class="odd  2"><td class="dateTime">2025-05-20

      - 2025-05-27</td><td class="nullValue">-</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr class="even  3"><td class="dateTime">2025-05-27

      - 2025-06-03</td><td class="nullValue">-</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr class="odd  4"><td class="dateTime">2025-06-03</td><td class="nullValue">-</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr class="even  5"><td class="dateTime">2025-06-04</td><td class="nullValue">-</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr class="odd  6"><td class="dateTime">2025-06-05</td><td class="nullValue">-</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr class="even  7"><td class="dateTime">2025-06-06</td><td class="nullValue">-</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr class="odd  8"><td class="dateTime">2025-06-07</td><td class="nullValue">-</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr class="even  9"><td class="dateTime">2025-06-08</td><td class="nullValue">-</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr class="odd  10"><td class="dateTime">2025-06-09</td><td class="nullValue">-</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr class="even  11"><td class="dateTime">2025-06-10</td><td class="nullValue">-</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr class="odd  12"><td class="dateTime">2025-06-11</td><td class="nullValue">-</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr class="even  13"><td class="dateTime">2025-06-12</td><td class="nullValue">-</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr class="odd  14"><td class="dateTime">2025-06-13</td><td class="nullValue">-</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr class="even  15"><td class="dateTime">2025-06-14</td><td class="nullValue">-</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr class="odd  16"><td class="dateTime">2025-06-15</td><td class="nullValue">-</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr class="even  17"><td class="dateTime">2025-06-16</td><td class="hms">0:36:02</td><td class="hms">0:37:54</td><td class="colBreak"> </td><td class="hms">0:20:53</td><td class="hms">0:51:21</td></tr></table><h2>

      Battery capacity history

    </h2><div class="explanation">

      Charge capacity history of the system's batteries

    </div><table><colgroup><col/><col class="col2"/><col style="width: 10em;"/></colgroup><thead><tr><td><span>PERIOD</span></td><td class="centered">

            FULL CHARGE CAPACITY

          </td><td class="centered">

            DESIGN CAPACITY

          </td></tr></thead><tr class="even  1"><td class="dateTime">2025-05-13

      - 2025-05-20</td><td class="mw">19,586 mWh

        </td><td class="mw">41,006 mWh

        </td></tr><tr class="odd  2"><td class="dateTime">2025-05-20

      - 2025-05-27</td><td class="mw">19,586 mWh

        </td><td class="mw">41,006 mWh

        </td></tr><tr class="even  3"><td class="dateTime">2025-05-27

      - 2025-06-03</td><td class="mw">19,586 mWh

        </td><td class="mw">41,006 mWh

        </td></tr><tr class="odd  4"><td class="dateTime">2025-06-03</td><td class="mw">19,586 mWh

        </td><td class="mw">41,006 mWh

        </td></tr><tr class="even  5"><td class="dateTime">2025-06-04</td><td class="mw">19,586 mWh

        </td><td class="mw">41,006 mWh

        </td></tr><tr class="odd  6"><td class="dateTime">2025-06-05</td><td class="mw">19,586 mWh

        </td><td class="mw">41,006 mWh

        </td></tr><tr class="even  7"><td class="dateTime">2025-06-06</td><td class="mw">19,586 mWh

        </td><td class="mw">41,006 mWh

        </td></tr><tr class="odd  8"><td class="dateTime">2025-06-07</td><td class="mw">19,586 mWh

        </td><td class="mw">41,006 mWh

        </td></tr><tr class="even  9"><td class="dateTime">2025-06-08</td><td class="mw">19,586 mWh

        </td><td class="mw">41,006 mWh

        </td></tr><tr class="odd  10"><td class="dateTime">2025-06-09</td><td class="mw">19,586 mWh

        </td><td class="mw">41,006 mWh

        </td></tr><tr class="even  11"><td class="dateTime">2025-06-10</td><td class="mw">19,586 mWh

        </td><td class="mw">41,006 mWh

        </td></tr><tr class="odd  12"><td class="dateTime">2025-06-11</td><td class="mw">19,586 mWh

        </td><td class="mw">41,006 mWh

        </td></tr><tr class="even  13"><td class="dateTime">2025-06-12</td><td class="mw">19,586 mWh

        </td><td class="mw">41,006 mWh

        </td></tr><tr class="odd  14"><td class="dateTime">2025-06-13</td><td class="mw">19,586 mWh

        </td><td class="mw">41,006 mWh

        </td></tr><tr class="even  15"><td class="dateTime">2025-06-14</td><td class="mw">19,586 mWh

        </td><td class="mw">41,006 mWh

        </td></tr><tr class="odd  16"><td class="dateTime">2025-06-15</td><td class="mw">19,586 mWh

        </td><td class="mw">41,006 mWh

        </td></tr><tr class="even  17"><td class="dateTime">2025-06-16</td><td class="mw">28,125 mWh

        </td><td class="mw">41,006 mWh

        </td></tr></table><h2>

      Battery life estimates

    </h2><div class="explanation2">

      Battery life estimates based on observed drains

    </div><table><colgroup><col/><col class="col2"/><col style="width: 10em;"/><col style=""/><col style="width: 10em;"/><col style="width: 10em;"/><col style="width: 10em;"/></colgroup><thead><tr class="rowHeader"><td> </td><td colspan="2" class="centered">

            AT FULL CHARGE

          </td><td class="colBreak"> </td><td colspan="2" class="centered">

            AT DESIGN CAPACITY

          </td></tr><tr class="rowHeader"><td>

            PERIOD

          </td><td class="centered"><span>ACTIVE</span></td><td class="centered"><span>CONNECTED STANDBY</span></td><td class="colBreak"> </td><td class="centered"><span>ACTIVE</span></td><td class="centered"><span>CONNECTED STANDBY</span></td></tr></thead><tr style="vertical-align:top" class="even  1"><td class="dateTime">2025-05-13

      - 2025-05-20</td><td class="nullValue">-</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  2"><td class="dateTime">2025-05-20

      - 2025-05-27</td><td class="nullValue">-</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  3"><td class="dateTime">2025-05-27

      - 2025-06-03</td><td class="nullValue">-</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  4"><td class="dateTime">2025-06-03</td><td class="nullValue">-</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  5"><td class="dateTime">2025-06-04</td><td class="nullValue">-</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  6"><td class="dateTime">2025-06-05</td><td class="nullValue">-</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  7"><td class="dateTime">2025-06-06</td><td class="nullValue">-</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  8"><td class="dateTime">2025-06-07</td><td class="nullValue">-</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  9"><td class="dateTime">2025-06-08</td><td class="nullValue">-</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  10"><td class="dateTime">2025-06-09</td><td class="nullValue">-</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  11"><td class="dateTime">2025-06-10</td><td class="nullValue">-</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  12"><td class="dateTime">2025-06-11</td><td class="nullValue">-</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  13"><td class="dateTime">2025-06-12</td><td class="nullValue">-</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  14"><td class="dateTime">2025-06-13</td><td class="nullValue">-</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  15"><td class="dateTime">2025-06-14</td><td class="nullValue">-</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="odd  16"><td class="dateTime">2025-06-15</td><td class="nullValue">-</td><td class="nullValue">-</td><td class="colBreak"> </td><td class="nullValue">-</td><td class="nullValue">-</td></tr><tr style="vertical-align:top" class="even  17"><td class="dateTime">2025-06-16</td><td class="hms">2:10:44</td><td class="hms"><div style="height:1em;">8:40:28</div><span style="font-size:9pt; ">184 %

      

              / 16 h

            </span></td><td class="colBreak"> </td><td class="hms">3:10:37</td><td class="hms"><div style="height:1em;">12:38:51</div><span style="font-size:9pt; ">127 %

      

              / 16 h

            </span></td></tr></table><div class="explanation2" style="margin-top: 1em; margin-bottom: 0.4em;">

      Current estimate of battery life based on all observed drains since OS install

    </div><table><colgroup><col/><col class="col2"/><col style="width: 10em;"/><col style=""/><col style="width: 10em;"/><col style="width: 10em;"/><col style="width: 10em;"/></colgroup><tr class="even" style="vertical-align:top"><td>

          Since OS install

        </td><td class="hms">2:10:44</td><td class="hms"><div style="height:1em;">8:40:28</div><span style="font-size:9pt; ">184 %

      

                / 16 h

              </span></td><td class="colBreak"> </td><td class="hms">3:10:37</td><td class="hms"><div style="height:1em;">12:38:51</div><span style="font-size:9pt; ">127 %

      

                / 16 h

              </span></td></tr></table><br/><br/><br/></body></html>