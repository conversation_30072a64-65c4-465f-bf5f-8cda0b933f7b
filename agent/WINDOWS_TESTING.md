# Windows Testing Commands for Laptop QC Agent

## Quick System Info Commands

Run these in PowerShell or Command Prompt to test what values you'll get:

### 1. Get Model Information
```powershell
# PowerShell - Most reliable for model info
Get-WmiObject Win32_ComputerSystem | Select-Object Model, Manufacturer

# Alternative PowerShell
Get-CimInstance Win32_ComputerSystem | Select-Object Model, Manufacturer

# Command Prompt
wmic computersystem get model,manufacturer
```

### 2. Get Serial Number
```powershell
# PowerShell
Get-WmiObject Win32_BIOS | Select-Object SerialNumber

# Command Prompt
wmic bios get serialnumber
```

### 3. Get Battery Information
```powershell
# PowerShell - Battery percentage
Get-WmiObject Win32_Battery | Select-Object EstimatedChargeRemaining

# PowerShell - Full battery info
Get-WmiObject Win32_Battery | Format-List *

# Command Prompt
wmic path win32_battery get EstimatedChargeRemaining
```

### 4. Get Network Information
```powershell
# PowerShell
Get-NetIPAddress | Where-Object {$_.InterfaceAlias -notlike "*Loopback*" -and $_.AddressFamily -eq "IPv4"}

# Command Prompt
ipconfig | findstr IPv4
```

## Testing Without Building EXE

To test changes quickly without building:

### Option 1: Remote PowerShell Testing
Create a test script `test-info.ps1`:
```powershell
# Test what systeminformation npm package would return
$system = Get-WmiObject Win32_ComputerSystem
$bios = Get-WmiObject Win32_BIOS
$battery = Get-WmiObject Win32_Battery

Write-Host "Model: $($system.Model)"
Write-Host "Manufacturer: $($system.Manufacturer)"
Write-Host "Serial: $($bios.SerialNumber)"
Write-Host "Battery: $($battery.EstimatedChargeRemaining)%"
```

### Option 2: Node.js Testing Script
Create `test-sysinfo.js` in agent folder:
```javascript
const si = require('systeminformation');

async function testSystemInfo() {
  console.log('Testing system information...\n');
  
  try {
    const system = await si.system();
    const battery = await si.battery();
    const uuid = await si.uuid();
    
    console.log('System Info:');
    console.log('  Model:', system.model || 'NOT FOUND');
    console.log('  Manufacturer:', system.manufacturer || 'NOT FOUND');
    console.log('  Serial:', system.serial || 'NOT FOUND');
    console.log('  UUID:', uuid.hardware || 'NOT FOUND');
    
    console.log('\nBattery Info:');
    console.log('  Percentage:', battery.percent);
    console.log('  Is Charging:', battery.isCharging);
  } catch (error) {
    console.error('Error:', error);
  }
}

testSystemInfo();
```

Run with: `node test-sysinfo.js`

## Suggested Development Workflow

1. **Use GitHub Codespaces or Remote Development**
   - Develop on Windows directly
   - Or use VS Code Remote-SSH to a Windows machine

2. **Create a Development Build Script**
   Add to `package.json`:
   ```json
   "scripts": {
     "test-info": "node test-sysinfo.js",
     "build-portable": "electron-builder --win portable --publish never"
   }
   ```

3. **Use Environment Variable for Testing**
   Modify your code to skip certain checks in dev:
   ```javascript
   if (process.env.NODE_ENV === 'development') {
     console.log('Dev mode - using mock data');
     // Use mock values for testing
   }
   ```

## Common Windows Laptop Models

Based on manufacturer, here are typical model formats:

- **Dell**: OptiPlex 3070, Latitude 5520, XPS 13 9310
- **HP**: EliteBook 840 G7, ProBook 450 G8
- **Lenovo**: ThinkPad T14 Gen 2, IdeaPad 5 15ARE05
- **ASUS**: VivoBook S15 S533EA

The model field typically contains the exact model number that helps identify the laptop.