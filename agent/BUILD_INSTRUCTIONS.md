# Agent Build Instructions

## Development Setup

1. **Install dependencies**
```bash
cd agent
npm install
```

2. **Configure environment**
- Copy `.env.example` to `.env`
- Add your Supabase credentials to `.env`

3. **Run in development mode**
```bash
npm run dev
```

## Building EXE

1. **Build for Windows (portable EXE)**
```bash
npm run build
```

The EXE will be created in: `agent/dist/Battery Test Agent.exe`

2. **Copy to USB**
- Navigate to `agent/dist/`
- Copy `Battery Test Agent.exe` to your USB drive
- No installation required - just run the EXE

## Quick Build & Copy

For repeated builds during testing:

```bash
# Build and show location
npm run build && echo "EXE created at: $(pwd)/dist/"
```

## Troubleshooting

- If build fails, ensure you have all dependencies: `npm install`
- For code signing issues, the portable build doesn't require signing
- The EXE is about 80-100MB (includes Electron runtime)

## Network Testing

Before deploying:
1. Change `NETWORK_VALIDATION` in `.env` to match your network
2. Test on target network to ensure connection works