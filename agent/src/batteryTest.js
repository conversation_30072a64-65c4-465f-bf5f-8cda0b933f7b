const BatteryMonitor = require('./battery');
const { EventEmitter } = require('events');

class BatteryTest extends EventEmitter {
  constructor(supabaseClient, batteryHealthMonitor = null) {
    super();
    this.supabase = supabaseClient;
    this.batteryMonitor = new BatteryMonitor();
    this.batteryHealthMonitor = batteryHealthMonitor;
    this.isRunning = false;
    this.sessionId = null;
    this.laptopId = null;
    this.currentPhase = null;
    this.phaseStartTime = null;
    this.monitorInterval = null;
    this.waitingForPowerChange = false;
    this.testConfig = {
      chargeMinutes: 20,      // Default 20 minutes
      dischargeMinutes: 20,   // Default 20 minutes
      passThreshold: 80,      // Battery health percentage
      readingInterval: 5000,  // 5 seconds for better real-time updates
      startWithCharge: false  // false = discharge first, true = charge first
    };
    this.readingCounter = 0;    // Track readings for database updates
    this.lastDatabaseUpdate = 0; // Track last database update time
  }

  async startManualTest(laptopId, config = {}) {
    if (this.isRunning) {
      throw new Error('Test already running');
    }

    // Check for existing active session to prevent duplicates
    const { data: activeSessions } = await this.supabase
      .from('test_sessions')
      .select('*')
      .eq('laptop_id', laptopId)
      .eq('test_type', 'battery_manual')
      .is('end_time', null);
      
    if (activeSessions && activeSessions.length > 0) {
      // Use existing session instead of creating new one
      console.log('Using existing active session:', activeSessions[0].id);
      return this.startManualTestWithSession(activeSessions[0].id, laptopId, config);
    }

    this.laptopId = laptopId;
    this.testConfig = { ...this.testConfig, ...config };
    this.waitingForPowerChange = false;
    this.readingCounter = 0;
    this.lastDatabaseUpdate = 0;
    
    try {
      // Get current battery health data before starting test
      let healthData = null;
      if (this.batteryHealthMonitor) {
        console.log('Capturing battery health data for test session...');
        healthData = await this.batteryHealthMonitor.getBatteryHealthData();
      }
      
      // Create test session with battery health snapshot
      const sessionData = {
        laptop_id: laptopId,
        test_type: 'battery_manual',
        notes: JSON.stringify({
          config: {
            chargeMinutes: this.testConfig.chargeMinutes,
            dischargeMinutes: this.testConfig.dischargeMinutes,
            startWithCharge: this.testConfig.startWithCharge
          },
          started_by: 'agent'
        })
      };
      
      // Add health data if available
      if (healthData) {
        sessionData.laptop_battery_health = healthData.healthPercent;
        sessionData.laptop_design_capacity = healthData.designCapacity;
        sessionData.laptop_full_charge_capacity = healthData.fullChargeCapacity;
        sessionData.laptop_cycle_count = healthData.cycleCount;
      }
      
      const { data: session, error } = await this.supabase
        .from('test_sessions')
        .insert(sessionData)
        .select()
        .single();

      if (error) throw error;
      
      this.sessionId = session.id;
      this.isRunning = true;

      // Update laptop status
      await this.supabase
        .from('laptops')
        .update({ status: 'testing' })
        .eq('id', laptopId);

      // Start with configured phase
      if (this.testConfig.startWithCharge) {
        await this.startChargePhase();
      } else {
        await this.startDischargePhase();
      }
      
      return { success: true, sessionId: this.sessionId };
    } catch (error) {
      await this.handleTestError(error);
      throw error;
    }
  }

  // New method to start test with existing session from dashboard
  async startManualTestWithSession(sessionId, laptopId, config = {}) {
    if (this.isRunning) {
      throw new Error('Test already running');
    }

    this.sessionId = sessionId;
    this.laptopId = laptopId;
    this.testConfig = { ...this.testConfig, ...config };
    this.waitingForPowerChange = false;
    this.readingCounter = 0;
    this.lastDatabaseUpdate = 0;
    this.isRunning = true;

    try {
      // Update laptop status
      await this.supabase
        .from('laptops')
        .update({ status: 'testing' })
        .eq('id', laptopId);

      // Start with configured phase
      if (this.testConfig.startWithCharge) {
        await this.startChargePhase();
      } else {
        await this.startDischargePhase();
      }
      
      return { success: true, sessionId: this.sessionId };
    } catch (error) {
      await this.handleTestError(error);
      throw error;
    }
  }

  async startDischargePhase() {
    console.log('Starting discharge phase...');
    this.currentPhase = 'discharge';
    this.waitingForPowerChange = true;
    
    // Get initial battery reading
    const batteryInfo = await this.batteryMonitor.getBatteryInfo();
    
    // Check if already in correct state
    if (!batteryInfo.isCharging) {
      // Already on battery, can start immediately
      this.waitingForPowerChange = false;
      this.phaseStartTime = Date.now();
    } else {
      // Need to wait for power disconnect
      this.phaseStartTime = null; // Don't start timer yet
    }
    
    // Notify UI to prompt user to disconnect power
    this.emit('phase-change', {
      phase: 'discharge',
      instruction: batteryInfo.isCharging ? 'Please disconnect the power cable to start discharge test' : 'Discharge test starting...',
      duration: this.testConfig.dischargeMinutes,
      initialBattery: batteryInfo.percentage,
      waitingForPowerChange: batteryInfo.isCharging
    });

    // Start monitoring
    this.startMonitoring();
  }

  async startChargePhase() {
    console.log('Starting charge phase...');
    this.currentPhase = 'charge';
    this.waitingForPowerChange = true;
    
    // Get initial battery reading
    const batteryInfo = await this.batteryMonitor.getBatteryInfo();
    
    // Check if already in correct state
    if (batteryInfo.isCharging) {
      // Already charging, can start immediately
      this.waitingForPowerChange = false;
      this.phaseStartTime = Date.now();
    } else {
      // Need to wait for power connect
      this.phaseStartTime = null; // Don't start timer yet
    }
    
    // Notify UI to prompt user to connect power
    this.emit('phase-change', {
      phase: 'charge',
      instruction: !batteryInfo.isCharging ? 'Please connect the power cable to start charge test' : 'Charge test starting...',
      duration: this.testConfig.chargeMinutes,
      initialBattery: batteryInfo.percentage,
      waitingForPowerChange: !batteryInfo.isCharging
    });

    // Continue monitoring
    // No need to restart as it's already running
  }

  startMonitoring() {
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
    }

    // Use shorter interval when waiting for power change, but not too aggressive
    const updateInterval = this.waitingForPowerChange ? 3000 : this.testConfig.readingInterval; // Increased to 3s for lower CPU

    this.monitorInterval = setInterval(async () => {
      const batteryInfo = await this.batteryMonitor.getBatteryInfo();
      
      // Check if we're waiting for power state change
      if (this.waitingForPowerChange) {
        const expectedCharging = this.currentPhase === 'charge';
        if (batteryInfo.isCharging === expectedCharging) {
          // Power state matches expected, start/resume test
          this.waitingForPowerChange = false;
          this.phaseStartTime = Date.now(); // Start phase timer now
          
          // Restart monitoring with normal interval
          clearInterval(this.monitorInterval);
          this.startMonitoring();
        } else {
          // Still waiting for power change
          const phaseDuration = this.currentPhase === 'discharge' 
            ? this.testConfig.dischargeMinutes * 60 * 1000
            : this.testConfig.chargeMinutes * 60 * 1000;
            
          this.emit('test-update', {
            phase: this.currentPhase,
            progress: 0,
            batteryInfo,
            timeRemaining: phaseDuration,
            elapsed: 0,
            waitingForPowerChange: true
          });
          
          // Update laptop record with waiting state
          await this.supabase
            .from('laptops')
            .update({
              test_phase: this.currentPhase,
              test_progress: 0,
              test_time_remaining: phaseDuration,
              test_waiting_for_power: true
            })
            .eq('id', this.laptopId);
            
          return; // Don't record reading or progress
        }
      }
      
      // Calculate elapsed time only when not waiting
      const elapsed = this.phaseStartTime ? Date.now() - this.phaseStartTime : 0;
      const phaseDuration = this.currentPhase === 'discharge' 
        ? this.testConfig.dischargeMinutes * 60 * 1000
        : this.testConfig.chargeMinutes * 60 * 1000;
      
      const progress = Math.min(100, (elapsed / phaseDuration) * 100);
      const timeRemaining = Math.max(0, phaseDuration - elapsed);

      // Update counter
      this.readingCounter++;
      
      // Save battery readings to database every 3rd update (15 seconds) to avoid overwhelming
      const now = Date.now();
      const shouldUpdateDatabase = (now - this.lastDatabaseUpdate) >= 15000;
      
      if (shouldUpdateDatabase) {
        const reading = {
          session_id: this.sessionId,
          percentage: batteryInfo.percentage,
          voltage: batteryInfo.voltage || 0,
          temperature: batteryInfo.temperature || 0,
          is_charging: batteryInfo.isCharging,
          power_source: batteryInfo.isCharging ? 'ac' : 'battery'
        };

        // Save to database
        await this.supabase
          .from('battery_readings')
          .insert(reading);
          
        this.lastDatabaseUpdate = now;
      }

      // Emit update for UI
      this.emit('test-update', {
        phase: this.currentPhase,
        progress,
        batteryInfo,
        timeRemaining,
        elapsed,
        waitingForPowerChange: false
      });

      // Update laptop record with test phase info every update for real-time sync
      // This is lightweight and keeps dashboard in sync
      await this.supabase
        .from('laptops')
        .update({
          test_phase: this.currentPhase,
          test_progress: Math.round(progress * 10) / 10, // Round to 1 decimal
          test_time_remaining: Math.round(timeRemaining / 1000) * 1000, // Round to nearest second
          test_waiting_for_power: false,
          battery_percentage: batteryInfo.percentage,
          is_charging: batteryInfo.isCharging
        })
        .eq('id', this.laptopId);

      // Check if phase complete
      if (elapsed >= phaseDuration && !this.waitingForPowerChange) {
        await this.completePhase();
      }
    }, updateInterval);
  }

  async completePhase() {
    this.waitingForPowerChange = true;
    
    // Restart monitoring with shorter interval for waiting state
    clearInterval(this.monitorInterval);
    this.startMonitoring();
    
    if (this.currentPhase === 'discharge') {
      if (this.testConfig.startWithCharge) {
        // Started with charge, discharge is second phase - complete test
        await this.completeTest();
      } else {
        // Started with discharge, move to charge phase
        this.emit('phase-complete', {
          phase: 'discharge',
          message: 'Discharge phase complete. Ready for charge phase.'
        });
        await this.startChargePhase();
      }
    } else if (this.currentPhase === 'charge') {
      if (this.testConfig.startWithCharge) {
        // Started with charge, move to discharge phase
        this.emit('phase-complete', {
          phase: 'charge',
          message: 'Charge phase complete. Ready for discharge phase.'
        });
        await this.startDischargePhase();
      } else {
        // Started with discharge, charge is second phase - complete test
        await this.completeTest();
      }
    }
  }

  async completeTest() {
    clearInterval(this.monitorInterval);
    
    // Calculate test results
    const { data: readings } = await this.supabase
      .from('battery_readings')
      .select('*')
      .eq('session_id', this.sessionId)
      .order('timestamp', { ascending: true });

    const dischargeReadings = readings.filter(r => !r.is_charging);
    const chargeReadings = readings.filter(r => r.is_charging);
    
    console.log('Test Complete - Readings Summary:');
    console.log(`Total readings: ${readings.length}`);
    console.log(`Discharge readings: ${dischargeReadings.length}`);
    console.log(`Charge readings: ${chargeReadings.length}`);
    if (dischargeReadings.length > 0) {
      console.log(`Discharge: ${dischargeReadings[0].percentage}% -> ${dischargeReadings[dischargeReadings.length-1].percentage}%`);
    }
    if (chargeReadings.length > 0) {
      console.log(`Charge: ${chargeReadings[0].percentage}% -> ${chargeReadings[chargeReadings.length-1].percentage}%`);
    }

    const results = {
      discharge: this.analyzePhase(dischargeReadings),
      charge: this.analyzePhase(chargeReadings),
      overall: null
    };

    results.overall = this.calculateTestResult(results);

    // Update test session with detailed results
    const fullResults = {
      ...results,
      config: this.testConfig,
      diagnostic: {
        totalReadings: readings.length,
        dischargeReadings: dischargeReadings.length,
        chargeReadings: chargeReadings.length,
        testDuration: {
          discharge: results.discharge.actualMinutes || 0,
          charge: results.charge.actualMinutes || 0
        }
      }
    };
    
    await this.supabase
      .from('test_sessions')
      .update({
        end_time: new Date().toISOString(),
        result: results.overall.pass ? 'pass' : 'fail',
        notes: JSON.stringify(fullResults)
      })
      .eq('id', this.sessionId);

    // Update laptop status and clear test phase info
    await this.supabase
      .from('laptops')
      .update({ 
        status: 'completed',
        test_phase: null,
        test_progress: null,
        test_time_remaining: null,
        test_waiting_for_power: false
      })
      .eq('id', this.laptopId);

    this.isRunning = false;
    this.sessionId = null;
    this.currentPhase = null;

    this.emit('test-complete', results);
  }

  analyzePhase(readings) {
    if (readings.length < 2) {
      return {
        startPercentage: 0,
        endPercentage: 0,
        rate: 0,
        readings: 0
      };
    }

    const startPercent = readings[0].percentage;
    const endPercent = readings[readings.length - 1].percentage;
    const startTime = new Date(readings[0].timestamp).getTime();
    const endTime = new Date(readings[readings.length - 1].timestamp).getTime();
    const minutes = (endTime - startTime) / (1000 * 60);
    const hours = minutes / 60;
    
    const percentChange = Math.abs(endPercent - startPercent);
    
    // For short tests, extrapolate to hourly rate
    // If test is less than 10 minutes, use minute-based calculation
    let rate;
    if (minutes < 10 && minutes > 0) {
      // Calculate rate per minute and extrapolate to hour
      const ratePerMinute = percentChange / minutes;
      rate = ratePerMinute * 60;
    } else if (hours > 0) {
      rate = percentChange / hours;
    } else {
      rate = 0;
    }

    return {
      startPercentage: startPercent,
      endPercentage: endPercent,
      rate: Math.round(rate * 10) / 10, // Percent per hour
      readings: readings.length,
      actualMinutes: Math.round(minutes * 10) / 10,
      percentChange: Math.round(percentChange * 10) / 10
    };
  }

  calculateTestResult(results) {
    const drainRate = results.discharge.rate;
    const chargeRate = results.charge.rate;
    
    // Expected rates (adjust based on laptop models)
    const expectedDrainRate = 20; // 20% per hour
    const expectedChargeRate = 60; // 60% per hour
    
    // Calculate health score
    const drainScore = drainRate > 0 ? Math.min(100, (expectedDrainRate / drainRate) * 100) : 0;
    const chargeScore = chargeRate > 0 ? Math.min(100, (chargeRate / expectedChargeRate) * 100) : 0;
    
    const healthScore = (drainScore + chargeScore) / 2;
    
    return {
      healthScore: Math.round(healthScore),
      pass: healthScore >= this.testConfig.passThreshold,
      drainRate,
      chargeRate,
      details: {
        drainScore: Math.round(drainScore),
        chargeScore: Math.round(chargeScore),
        totalReadings: results.discharge.readings + results.charge.readings
      }
    };
  }

  async stopTest() {
    if (!this.isRunning) return;
    
    clearInterval(this.monitorInterval);
    await this.handleTestError(new Error('Test stopped by user'));
    
    this.emit('test-stopped', {
      phase: this.currentPhase,
      message: 'Test stopped by user'
    });
  }

  async handleTestError(error) {
    console.error('Battery test error:', error);
    
    if (this.sessionId) {
      await this.supabase
        .from('test_sessions')
        .update({
          end_time: new Date().toISOString(),
          result: 'incomplete',
          notes: `Error: ${error.message}`
        })
        .eq('id', this.sessionId);
    }

    if (this.laptopId) {
      await this.supabase
        .from('laptops')
        .update({ 
          status: 'connected',
          test_phase: null,
          test_progress: null,
          test_time_remaining: null,
          test_waiting_for_power: false
        })
        .eq('id', this.laptopId);
    }

    this.isRunning = false;
    this.sessionId = null;
    this.currentPhase = null;
    
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
    }
  }

  // Allow configuration update
  updateConfig(config) {
    this.testConfig = { ...this.testConfig, ...config };
  }
}

module.exports = BatteryTest;