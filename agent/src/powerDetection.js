const EventEmitter = require('events');
const { exec } = require('child_process');
const os = require('os');

class PowerPatternDetector extends EventEmitter {
  constructor() {
    super();
    this.powerHistory = [];
    this.isMonitoring = false;
    this.detectionTimeout = null;
    this.PATTERN_WINDOW = 15000; // 15 seconds to detect pattern
    this.EXPECTED_PATTERN = [
      { state: 'AC', minDuration: 2000, maxDuration: 4000 }, // Initial AC
      { state: 'Battery', minDuration: 2000, maxDuration: 4000 }, // OFF
      { state: 'AC', minDuration: 2000, maxDuration: 4000 }, // ON
      { state: 'Battery', minDuration: 2000, maxDuration: 4000 }, // OFF
      { state: 'AC', minDuration: 4000, maxDuration: 6000 } // Final ON
    ];
  }

  // Start monitoring power state changes
  startMonitoring() {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    this.powerHistory = [];
    this.checkPowerState();
    
    // Set timeout for pattern detection window
    this.detectionTimeout = setTimeout(() => {
      this.stopMonitoring();
      this.emit('patternTimeout');
    }, this.PATTERN_WINDOW);
  }

  // Stop monitoring
  stopMonitoring() {
    this.isMonitoring = false;
    if (this.detectionTimeout) {
      clearTimeout(this.detectionTimeout);
      this.detectionTimeout = null;
    }
  }

  // Check current power state
  async checkPowerState() {
    if (!this.isMonitoring) return;

    try {
      const state = await this.getPowerState();
      const timestamp = Date.now();
      
      // Add to history if state changed
      if (this.powerHistory.length === 0 || 
          this.powerHistory[this.powerHistory.length - 1].state !== state) {
        this.powerHistory.push({ state, timestamp });
        
        // Check if pattern matches
        if (this.checkPattern()) {
          this.stopMonitoring();
          this.emit('patternDetected', this.extractBayNumber());
        }
      }
    } catch (error) {
      console.error('Error checking power state:', error);
    }

    // Continue monitoring with reduced frequency
    if (this.isMonitoring) {
      setTimeout(() => this.checkPowerState(), 2000); // Reduced to 2 seconds for lower CPU usage
    }
  }

  // Get current power state (AC or Battery)
  async getPowerState() {
    return new Promise((resolve, reject) => {
      if (os.platform() === 'win32') {
        // Windows: Use WMI to check battery status
        exec('wmic path Win32_Battery get BatteryStatus /value', (error, stdout) => {
          if (error) {
            reject(error);
            return;
          }
          
          // BatteryStatus=2 means AC connected
          const isCharging = stdout.includes('BatteryStatus=2');
          resolve(isCharging ? 'AC' : 'Battery');
        });
      } else {
        // For development on non-Windows, simulate with a file
        const fs = require('fs');
        const simFile = '/tmp/laptop_qc_power_state.txt';
        
        if (fs.existsSync(simFile)) {
          const state = fs.readFileSync(simFile, 'utf8').trim();
          resolve(state === 'AC' ? 'AC' : 'Battery');
        } else {
          // Default to AC for development
          resolve('AC');
        }
      }
    });
  }

  // Check if power history matches expected pattern
  checkPattern() {
    if (this.powerHistory.length < this.EXPECTED_PATTERN.length) {
      return false;
    }

    // Get the last N entries matching pattern length
    const recentHistory = this.powerHistory.slice(-this.EXPECTED_PATTERN.length);
    
    // Check each state in the pattern
    for (let i = 0; i < this.EXPECTED_PATTERN.length; i++) {
      const expected = this.EXPECTED_PATTERN[i];
      const actual = recentHistory[i];
      
      // Check state matches
      if (actual.state !== expected.state) {
        return false;
      }
      
      // Check duration (except for the last state)
      if (i < this.EXPECTED_PATTERN.length - 1) {
        const duration = recentHistory[i + 1].timestamp - actual.timestamp;
        if (duration < expected.minDuration || duration > expected.maxDuration) {
          return false;
        }
      }
    }
    
    return true;
  }

  // Extract bay number from pattern timing
  // In real implementation, dashboard would tell us which bay was cycled
  extractBayNumber() {
    // For now, return a placeholder
    // In production, this would be coordinated with dashboard
    return null;
  }

  // Simulate power change for testing
  simulatePowerChange(state) {
    if (process.env.NODE_ENV === 'development') {
      const fs = require('fs');
      fs.writeFileSync('/tmp/laptop_qc_power_state.txt', state);
      console.log(`Simulated power state: ${state}`);
    }
  }
}

module.exports = PowerPatternDetector;