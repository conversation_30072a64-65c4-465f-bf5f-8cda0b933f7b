const { exec } = require('child_process');
const util = require('util');
const execPromise = util.promisify(exec);

class BatteryHealthMonitor {
  constructor() {
    this.lastHealthData = null;
  }

  async getBatteryHealthData() {
    try {
      // Get design capacity - use shorter commands to avoid stack overflow
      const designCmd = `powershell -NoProfile -Command "$b=(Get-WmiObject -Namespace root\\\\WMI -Class BatteryStaticData -ErrorAction SilentlyContinue); if($b){$b.DesignedCapacity}else{''}"`;
      const fullChargeCmd = `powershell -NoProfile -Command "$b=(Get-WmiObject -Namespace root\\\\WMI -Class BatteryFullChargedCapacity -ErrorAction SilentlyContinue); if($b){$b.FullChargedCapacity}else{''}"`;
      const cycleCmd = `powershell -NoProfile -Command "$b=(Get-WmiObject -Namespace root\\\\WMI -Class BatteryCycleCount -ErrorAction SilentlyContinue); if($b){$b.CycleCount}else{''}"`;
      
      // Execute commands sequentially with timeout to avoid stack issues
      const execWithTimeout = (cmd) => {
        return new Promise((resolve) => {
          const timeout = setTimeout(() => {
            resolve({ stdout: '' });
          }, 5000); // 5 second timeout
          
          execPromise(cmd, { timeout: 5000 })
            .then(result => {
              clearTimeout(timeout);
              resolve(result);
            })
            .catch(() => {
              clearTimeout(timeout);
              resolve({ stdout: '' });
            });
        });
      };
      
      // Execute sequentially to avoid overwhelming PowerShell
      const designResult = await execWithTimeout(designCmd);
      const fullChargeResult = await execWithTimeout(fullChargeCmd);
      const cycleResult = await execWithTimeout(cycleCmd);
      
      // Parse results
      const designCapacity = parseInt(designResult.stdout.trim()) || null;
      const fullChargeCapacity = parseInt(fullChargeResult.stdout.trim()) || null;
      const cycleCount = parseInt(cycleResult.stdout.trim()) || null;
      
      // Calculate health percentage
      let healthPercent = null;
      let batteryStatus = 'UNKNOWN';
      
      if (designCapacity && fullChargeCapacity && designCapacity > 0) {
        healthPercent = Math.round((fullChargeCapacity / designCapacity) * 100 * 100) / 100; // Round to 2 decimals
        
        // Determine status
        if (healthPercent >= 80) {
          batteryStatus = 'GOOD';
        } else if (healthPercent >= 50) {
          batteryStatus = 'FAIR';
        } else {
          batteryStatus = 'POOR';
        }
      }
      
      const healthData = {
        designCapacity,
        fullChargeCapacity,
        cycleCount,
        healthPercent,
        batteryStatus,
        timestamp: new Date().toISOString()
      };
      
      this.lastHealthData = healthData;
      console.log('Battery health data collected:', healthData);
      
      return healthData;
    } catch (error) {
      console.error('Error collecting battery health data:', error);
      return {
        designCapacity: null,
        fullChargeCapacity: null,
        cycleCount: null,
        healthPercent: null,
        batteryStatus: 'ERROR',
        timestamp: new Date().toISOString(),
        error: error.message
      };
    }
  }
  
  // Get cached data or fetch new
  async getHealthData(forceRefresh = false) {
    if (!forceRefresh && this.lastHealthData) {
      // Return cached data if less than 15 minutes old (reduced WMI queries)
      const age = Date.now() - new Date(this.lastHealthData.timestamp).getTime();
      if (age < 15 * 60 * 1000) {
        return this.lastHealthData;
      }
    }
    
    return await this.getBatteryHealthData();
  }
}

module.exports = BatteryHealthMonitor;