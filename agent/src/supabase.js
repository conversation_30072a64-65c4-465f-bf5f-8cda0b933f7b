const { createClient } = require('@supabase/supabase-js');
const si = require('systeminformation');
const os = require('os');
const config = require('./config');

class SupabaseConnection {
  constructor() {
    this.client = null;
    this.laptopId = null;
    this.laptopRecord = null;
    this.lastBatteryPercentage = null;
    this.lastChargingStatus = null;
  }

  async init() {
    try {
      // Use embedded config for v1.2 - no .env file needed
      const supabaseUrl = config.SUPABASE_URL;
      const supabaseKey = config.SUPABASE_SERVICE_KEY;
      
      if (!supabaseUrl || !supabaseKey) {
        throw new Error('Supabase configuration missing');
      }
      
      this.client = createClient(supabaseUrl, supabaseKey);
    } catch (error) {
      console.error('Failed to initialize Supabase client:', error);
      throw error;
    }
  }

  async validateNetwork() {
    const networkValidation = config.NETWORK_VALIDATION;
    const interfaces = os.networkInterfaces();
    
    let currentIP = null;
    
    for (const name of Object.keys(interfaces)) {
      for (const iface of interfaces[name]) {
        if (iface.family === 'IPv4' && !iface.internal) {
          currentIP = iface.address;
          // If network validation is disabled (null), accept any network
          if (!networkValidation || iface.address.startsWith(networkValidation)) {
            return { valid: true, ip: iface.address };
          }
        }
      }
    }
    
    // If network validation is disabled, always return valid
    if (!networkValidation) {
      return { valid: true, ip: currentIP || 'No IP detected' };
    }
    
    return { valid: false, ip: null };
  }

  async getSystemInfo() {
    try {
      const system = await si.system();
      const uuid = await si.uuid();
      const networkInterfaces = await si.networkInterfaces();
      
      // Get MAC address - find first active network interface
      let macAddress = '';
      if (networkInterfaces && networkInterfaces.length > 0) {
        // Try to find active interface, preferring non-virtual ones
        const activeInterface = networkInterfaces.find(iface => 
          iface.mac && iface.mac !== '00:00:00:00:00:00'
        );
        macAddress = activeInterface ? activeInterface.mac : '';
      }
      
      return {
        serialNumber: system.serial || uuid.hardware || 'UNKNOWN',
        macAddress: macAddress,
        model: system.model || ''
      };
    } catch (error) {
      console.error('Error getting system info:', error);
      return {
        serialNumber: 'UNKNOWN',
        macAddress: '',
        model: ''
      };
    }
  }

  async connect() {
    try {
      // Validate network - DISABLED FOR TESTING
      // TODO: Enable this for production deployment
      const networkCheck = await this.validateNetwork();
      /*
      if (!networkCheck.valid) {
        throw new Error('Invalid network. Please connect to company network.');
      }
      */
      console.log('Network validation disabled for testing. Current IP:', networkCheck.ip || 'Unknown');

      // Initialize Supabase if not already done
      if (!this.client) {
        await this.init();
      }
      
      // Verify client is properly initialized
      if (!this.client) {
        throw new Error('Failed to initialize Supabase client');
      }

      // Get system info
      const systemInfo = await this.getSystemInfo();
      console.log('System info collected:', systemInfo);

      // Check if laptop already exists
      const { data: existing, error: fetchError } = await this.client
        .from('laptops')
        .select('*')
        .eq('serial_number', systemInfo.serialNumber)
        .single();

      if (fetchError && fetchError.code !== 'PGRST116') {
        throw fetchError;
      }

      let laptop;
      if (existing) {
        // Update existing laptop
        const updateData = {
          status: 'connected',
          last_seen: new Date().toISOString(),
          mac_address: systemInfo.macAddress,
          ip_address: networkCheck.ip || null,
          bay_number: existing.bay_number || null // Preserve bay if already assigned
        };
        
        // Only include model if it has a value
        if (systemInfo.model) {
          updateData.model = systemInfo.model;
        }
        
        const { data, error } = await this.client
          .from('laptops')
          .update(updateData)
          .eq('id', existing.id)
          .select()
          .single();

        if (error) throw error;
        laptop = data;
      } else {
        // Create new laptop
        const shortId = await this.generateShortId();
        
        const insertData = {
          short_id: shortId,
          serial_number: systemInfo.serialNumber,
          mac_address: systemInfo.macAddress,
          ip_address: networkCheck.ip || null,
          status: 'connected',
          bay_number: null // Will be assigned via power pattern
        };
        
        // Only include model if it has a value
        if (systemInfo.model) {
          insertData.model = systemInfo.model;
        }
        
        const { data, error } = await this.client
          .from('laptops')
          .insert(insertData)
          .select()
          .single();

        if (error) throw error;
        laptop = data;
      }

      this.laptopId = laptop.short_id;
      this.laptopRecord = laptop;

      return {
        success: true,
        laptopId: laptop.short_id,
        systemInfo: systemInfo,
        networkInfo: networkCheck.ip,
        bayNumber: laptop.bay_number
      };
    } catch (error) {
      console.error('Connection error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  async generateShortId() {
    // Get the highest existing ID
    const { data, error } = await this.client
      .from('laptops')
      .select('short_id')
      .like('short_id', 'A%')
      .order('short_id', { ascending: false })
      .limit(1);

    if (error) throw error;

    let nextNumber = 1;
    if (data && data.length > 0) {
      const lastId = data[0].short_id;
      const lastNumber = parseInt(lastId.substring(1));
      nextNumber = lastNumber + 1;
    }

    return `A${nextNumber.toString().padStart(3, '0')}`;
  }

  async disconnect() {
    // Stop heartbeat first
    this.stopHeartbeat();
    
    if (this.laptopRecord) {
      await this.client
        .from('laptops')
        .update({
          status: 'idle',
          last_seen: new Date().toISOString()
        })
        .eq('id', this.laptopRecord.id);
    }

    this.laptopId = null;
    this.laptopRecord = null;
  }

  async updateBatteryData(batteryInfo) {
    if (!this.laptopRecord) return;

    // Only update if battery percentage changed or charging status changed
    const hasChanged = 
      this.lastBatteryPercentage !== batteryInfo.percentage ||
      this.lastChargingStatus !== batteryInfo.isCharging;
    
    if (hasChanged) {
      try {
        await this.client
          .from('laptops')
          .update({
            last_seen: new Date().toISOString(),
            battery_percentage: batteryInfo.percentage,
            is_charging: batteryInfo.isCharging
          })
          .eq('id', this.laptopRecord.id);
        
        // Cache last values
        this.lastBatteryPercentage = batteryInfo.percentage;
        this.lastChargingStatus = batteryInfo.isCharging;
      } catch (error) {
        console.error('Failed to update battery data:', error);
      }
    }
  }

  async updateBatteryHealthData(healthData) {
    if (!this.laptopRecord) return;

    try {
      const updateData = {
        battery_health_percent: healthData.healthPercent,
        design_capacity: healthData.designCapacity,
        full_charge_capacity: healthData.fullChargeCapacity,
        battery_status: healthData.batteryStatus,
        cycle_count: healthData.cycleCount,
        battery_data_updated_at: new Date().toISOString()
      };

      // Remove null values
      Object.keys(updateData).forEach(key => {
        if (updateData[key] === null) {
          delete updateData[key];
        }
      });

      await this.client
        .from('laptops')
        .update(updateData)
        .eq('id', this.laptopRecord.id);
      
      console.log('Battery health data updated:', updateData);
      return { success: true };
    } catch (error) {
      console.error('Failed to update battery health data:', error);
      return { success: false, error: error.message };
    }
  }

  // Start heartbeat to maintain connection status
  startHeartbeat() {
    if (!this.laptopRecord) return;
    
    // Only update last_seen every 30 seconds to reduce database writes
    // Dashboard will check timestamps client-side for connection status
    this.heartbeatInterval = setInterval(async () => {
      if (this.laptopRecord) {
        try {
          await this.client
            .from('laptops')
            .update({
              last_seen: new Date().toISOString()
            })
            .eq('id', this.laptopRecord.id);
        } catch (error) {
          console.error('Heartbeat failed:', error);
        }
      }
    }, 30000); // 30 seconds instead of 3 seconds = 90% reduction in writes
  }

  // Stop heartbeat
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  async assignBay(bayNumber) {
    if (!this.laptopRecord) {
      throw new Error('No laptop connected');
    }

    try {
      const { data, error } = await this.client
        .from('laptops')
        .update({
          bay_number: bayNumber,
          status: 'connected'
        })
        .eq('id', this.laptopRecord.id)
        .select()
        .single();

      if (error) throw error;

      this.laptopRecord = data;
      return {
        success: true,
        bayNumber: bayNumber
      };
    } catch (error) {
      console.error('Bay assignment error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Subscribe to bay assignment commands from dashboard
  subscribeToBayAssignment(callback) {
    if (!this.client || !this.laptopRecord) return null;

    const subscription = this.client
      .channel(`bay-assignment-${this.laptopRecord.id}`)
      .on('postgres_changes', {
        event: 'UPDATE',
        schema: 'public',
        table: 'laptops',
        filter: `id=eq.${this.laptopRecord.id}`
      }, (payload) => {
        if (payload.new.bay_assignment_pending) {
          callback(payload.new.bay_assignment_pending);
        }
      })
      .subscribe();

    return subscription;
  }

  // Create test session
  async createTestSession(testType = 'battery') {
    if (!this.laptopRecord) return null;

    try {
      const { data, error } = await this.client
        .from('test_sessions')
        .insert({
          laptop_id: this.laptopRecord.id,
          test_type: testType,
          start_time: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating test session:', error);
      return null;
    }
  }

  // Log battery reading
  async logBatteryReading(sessionId, batteryInfo) {
    if (!this.client || !sessionId) return;

    try {
      await this.client
        .from('battery_readings')
        .insert({
          session_id: sessionId,
          percentage: batteryInfo.percent,
          voltage: batteryInfo.voltage || null,
          temperature: null, // TODO: Add temperature reading
          is_charging: batteryInfo.isCharging
        });
    } catch (error) {
      console.error('Error logging battery reading:', error);
    }
  }
}

module.exports = SupabaseConnection;