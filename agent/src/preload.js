const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON>enderer without exposing the entire object
contextBridge.exposeInMainWorld('api', {
  // Invoke methods
  connect: () => ipcRenderer.invoke('connect'),
  disconnect: () => ipcRenderer.invoke('disconnect'),
  getSystemInfo: () => ipcRenderer.invoke('get-system-info'),
  
  // Battery test methods
  startBatteryTest: (config) => ipcRenderer.invoke('start-battery-test', config),
  stopBatteryTest: () => ipcRenderer.invoke('stop-battery-test'),
  getBatteryTestStatus: () => ipcRenderer.invoke('get-battery-test-status'),
  
  // Event listeners
  onStatusUpdate: (callback) => {
    ipcRenderer.on('status-update', (event, data) => callback(data));
  },
  onBatteryUpdate: (callback) => {
    ipcRenderer.on('battery-update', (event, data) => callback(data));
  },
  onBayAssignmentStatus: (callback) => {
    ipcRenderer.on('bay-assignment-status', (event, data) => callback(data));
  },
  onBatteryTestUpdate: (callback) => {
    ipcRenderer.on('battery-test-update', (event, data) => callback(data));
  },
  onBatteryTestPhaseChange: (callback) => {
    ipcRenderer.on('battery-test-phase-change', (event, data) => callback(data));
  },
  onBatteryTestPhaseComplete: (callback) => {
    ipcRenderer.on('battery-test-phase-complete', (event, data) => callback(data));
  },
  onBatteryTestComplete: (callback) => {
    ipcRenderer.on('battery-test-complete', (event, data) => callback(data));
  },
  onBatteryTestStopped: (callback) => {
    ipcRenderer.on('battery-test-stopped', (event, data) => callback(data));
  },
  onBatteryTestStarted: (callback) => {
    ipcRenderer.on('battery-test-started', (event, data) => callback(data));
  }
});