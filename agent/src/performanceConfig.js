// Simple performance configuration for CPU optimization
// Can be adjusted based on deployment needs

const performanceConfig = {
  // Current mode: 'low', 'normal', 'high'
  mode: 'low',
  
  // Interval settings (in milliseconds)
  intervals: {
    low: {
      powerDetection: 2000,      // Was 500ms
      batteryMonitor: 30000,     // Was 15000ms
      powerStateCheck: 3000,     // Was 1000ms
      testWaitingForPower: 3000, // Was 2000ms
      heartbeat: 30000,          // Keep at 30s
      batteryHealthCheck: 900000 // 15 minutes
    },
    normal: {
      powerDetection: 1000,
      batteryMonitor: 15000,
      powerStateCheck: 2000,
      testWaitingForPower: 2000,
      heartbeat: 30000,
      batteryHealthCheck: 300000 // 5 minutes
    },
    high: {
      powerDetection: 500,       // Original values
      batteryMonitor: 10000,
      powerStateCheck: 1000,
      testWaitingForPower: 1000,
      heartbeat: 30000,
      batteryHealthCheck: 300000
    }
  },
  
  // Get interval for a specific component
  getInterval(component) {
    return this.intervals[this.mode][component] || this.intervals.normal[component];
  }
};

module.exports = performanceConfig;