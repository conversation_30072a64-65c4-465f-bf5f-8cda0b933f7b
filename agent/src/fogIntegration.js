const os = require('os');
const fs = require('fs').promises;
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);

class FOGIntegration {
  constructor() {
    this.configPath = path.join(process.env.APPDATA || process.env.HOME, '.laptop-qc', 'fog.json');
    this.fogConfig = null;
  }

  async initialize() {
    try {
      // Check if running from FOG deployment
      const isFOGDeployment = await this.checkFOGEnvironment();
      
      if (isFOGDeployment) {
        console.log('FOG deployment detected');
        await this.loadFOGConfig();
      } else {
        console.log('Manual deployment detected');
      }
      
      return isFOGDeployment;
    } catch (error) {
      console.error('FOG integration error:', error);
      return false;
    }
  }

  async checkFOGEnvironment() {
    // FOG typically sets specific environment variables or creates marker files
    const fogMarkers = [
      process.env.FOG_SERVER,
      process.env.FOG_HOST_ID,
      await this.fileExists('C:\\fog\\fog.log'),
      await this.fileExists('C:\\Program Files\\FOG\\')
    ];

    return fogMarkers.some(marker => marker);
  }

  async loadFOGConfig() {
    try {
      // Try to read FOG configuration from various sources
      
      // 1. Environment variables
      if (process.env.FOG_CONFIG) {
        this.fogConfig = JSON.parse(process.env.FOG_CONFIG);
        return;
      }

      // 2. FOG client config file
      const fogClientConfig = await this.readFOGClientConfig();
      if (fogClientConfig) {
        this.fogConfig = fogClientConfig;
        return;
      }

      // 3. Custom deployment config
      if (await this.fileExists(this.configPath)) {
        const configData = await fs.readFile(this.configPath, 'utf8');
        this.fogConfig = JSON.parse(configData);
      }
    } catch (error) {
      console.error('Failed to load FOG config:', error);
    }
  }

  async readFOGClientConfig() {
    const possiblePaths = [
      'C:\\Program Files\\FOG\\etc\\config.ini',
      'C:\\Program Files (x86)\\FOG\\etc\\config.ini',
      'C:\\fog\\client\\config.ini'
    ];

    for (const configPath of possiblePaths) {
      if (await this.fileExists(configPath)) {
        try {
          const content = await fs.readFile(configPath, 'utf8');
          return this.parseFOGConfig(content);
        } catch (error) {
          console.error(`Failed to read ${configPath}:`, error);
        }
      }
    }

    return null;
  }

  parseFOGConfig(iniContent) {
    const config = {};
    const lines = iniContent.split('\n');
    
    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('#') && !trimmed.startsWith('[')) {
        const [key, value] = trimmed.split('=').map(s => s.trim());
        if (key && value) {
          config[key] = value;
        }
      }
    }

    return {
      server: config.Server || config.server,
      hostId: config.HostID || config.hostid,
      macAddress: config.MAC || config.mac
    };
  }

  async registerWithFOG(laptopInfo) {
    if (!this.fogConfig?.server) {
      throw new Error('FOG server not configured');
    }

    try {
      // Get system information
      const systemInfo = await this.getSystemInfo();
      
      // Prepare registration data
      const registrationData = {
        mac: systemInfo.macAddress,
        hostname: systemInfo.hostname,
        productName: laptopInfo.model || systemInfo.productName,
        serialNumber: laptopInfo.serial_number || systemInfo.serialNumber,
        // Custom fields for QC tracking
        qc_laptop_id: laptopInfo.short_id,
        qc_bay_number: laptopInfo.bay_number
      };

      // In a real implementation, this would make an API call to FOG
      console.log('FOG Registration data:', registrationData);
      
      // Store FOG ID if received
      if (this.fogConfig.hostId) {
        return {
          fog_id: this.fogConfig.hostId,
          registered: true
        };
      }

      return {
        fog_id: null,
        registered: false
      };
    } catch (error) {
      console.error('FOG registration failed:', error);
      throw error;
    }
  }

  async getSystemInfo() {
    const info = {
      hostname: os.hostname(),
      platform: os.platform(),
      macAddress: await this.getPrimaryMAC(),
      productName: '',
      serialNumber: ''
    };

    try {
      // Get Windows system info
      if (os.platform() === 'win32') {
        const { stdout: product } = await execAsync('wmic computersystem get model /value');
        const { stdout: serial } = await execAsync('wmic bios get serialnumber /value');
        
        info.productName = this.parseWMICValue(product, 'Model');
        info.serialNumber = this.parseWMICValue(serial, 'SerialNumber');
      }
    } catch (error) {
      console.error('Failed to get system info:', error);
    }

    return info;
  }

  async getPrimaryMAC() {
    const interfaces = os.networkInterfaces();
    
    // Find primary ethernet interface
    for (const [name, addresses] of Object.entries(interfaces)) {
      // Skip loopback and virtual interfaces
      if (name.includes('Loopback') || name.includes('Virtual')) continue;
      
      for (const addr of addresses) {
        if (addr.mac && addr.mac !== '00:00:00:00:00:00') {
          return addr.mac;
        }
      }
    }

    return '00:00:00:00:00:00';
  }

  parseWMICValue(output, key) {
    const match = output.match(new RegExp(`${key}=(.+)`));
    return match ? match[1].trim() : '';
  }

  async fileExists(filePath) {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  async reportProgress(progress, status) {
    if (!this.fogConfig?.server) return;

    // In a real implementation, this would update FOG task progress
    console.log(`FOG Progress: ${progress}% - ${status}`);
  }

  async markImageComplete() {
    if (!this.fogConfig?.server) return;

    try {
      // Report to FOG that imaging is complete
      console.log('Marking FOG image task as complete');
      
      // Create marker file for QC agent auto-start
      const markerPath = path.join(process.env.APPDATA || process.env.HOME, '.laptop-qc', 'ready');
      await fs.mkdir(path.dirname(markerPath), { recursive: true });
      await fs.writeFile(markerPath, new Date().toISOString());
      
      return true;
    } catch (error) {
      console.error('Failed to mark image complete:', error);
      return false;
    }
  }
}

module.exports = new FOGIntegration();