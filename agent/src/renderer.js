// UI State Management
let isConnected = false;
let laptopId = null;
let bayNumber = null;
let isBatteryTestRunning = false;

// UI Elements - Status
const statusEl = document.getElementById('status');
const laptopIdEl = document.getElementById('laptop-id');
const connectBtn = document.getElementById('connect-btn');

// UI Elements - Info Grid
const bayInfoEl = document.getElementById('bay-info');
const batteryPercentageEl = document.getElementById('battery-percentage');
const serialNumberEl = document.getElementById('serial-number');
const networkInfoEl = document.getElementById('network-info');

// UI Elements - Bay Assignment
const bayStatusEl = document.getElementById('bay-status');

// UI Elements - Test Section
const testSectionEl = document.getElementById('test-section');

// UI Elements - Battery Test
const batteryTestIdleEl = document.getElementById('battery-test-idle');
const batteryTestRunningEl = document.getElementById('battery-test-running');
const batteryTestCompleteEl = document.getElementById('battery-test-complete');
const testPhaseEl = document.getElementById('test-phase');
const testProgressBarEl = document.getElementById('test-progress-bar');
const testBatteryEl = document.getElementById('test-battery');
const testTimeEl = document.getElementById('test-time');
const phaseInstructionEl = document.getElementById('phase-instruction');
const instructionTextEl = document.getElementById('instruction-text');
const chargingIndicatorEl = document.getElementById('charging-indicator');
const testResultsEl = document.getElementById('test-results');
const dischargeMinutesEl = document.getElementById('discharge-minutes');
const chargeMinutesEl = document.getElementById('charge-minutes');

// Connect/Disconnect
async function connect() {
  if (isConnected) {
    await disconnect();
  } else {
    connectBtn.disabled = true;
    connectBtn.textContent = 'Connecting...';
    
    try {
      const result = await window.api.connect();
      if (result.success) {
        updateConnectionStatus(true, result.laptopId);
        if (result.bayNumber) {
          updateBayInfo(result.bayNumber);
        }
      } else {
        alert(result.error || 'Connection failed');
        resetConnectButton();
      }
    } catch (error) {
      alert('Connection error: ' + error.message);
      resetConnectButton();
    }
  }
}

async function disconnect() {
  try {
    await window.api.disconnect();
    updateConnectionStatus(false);
  } catch (error) {
    console.error('Disconnect error:', error);
  }
}

function updateConnectionStatus(connected, id = null) {
  isConnected = connected;
  laptopId = id;
  
  if (connected) {
    statusEl.textContent = '● Connected';
    statusEl.className = 'status connected';
    connectBtn.textContent = 'Disconnect';
    connectBtn.disabled = false;
    
    laptopIdEl.textContent = id;
    laptopIdEl.classList.remove('hidden');
    testSectionEl.classList.remove('hidden');
  } else {
    statusEl.textContent = '● Disconnected';
    statusEl.className = 'status disconnected';
    connectBtn.textContent = 'Connect';
    connectBtn.disabled = false;
    
    laptopIdEl.classList.add('hidden');
    testSectionEl.classList.add('hidden');
    
    // Reset info
    bayInfoEl.textContent = 'Not Assigned';
    batteryPercentageEl.textContent = '--';
  }
}

function resetConnectButton() {
  connectBtn.disabled = false;
  connectBtn.textContent = 'Connect';
}

function updateBayInfo(bay) {
  bayNumber = bay;
  bayInfoEl.textContent = `Bay ${bay}`;
}

// Tab Switching
function switchTab(tabName) {
  // Update tab buttons
  document.querySelectorAll('.tab').forEach(tab => {
    tab.classList.remove('active');
  });
  event.target.classList.add('active');
  
  // Update tab content
  document.querySelectorAll('.tab-content').forEach(content => {
    content.classList.remove('active');
  });
  document.getElementById(`${tabName}-tab`).classList.add('active');
}

// Battery Testing
async function startBatteryTest() {
  try {
    const config = {
      dischargeMinutes: parseInt(dischargeMinutesEl.value) || 20,
      chargeMinutes: parseInt(chargeMinutesEl.value) || 20
    };
    
    const result = await window.api.startBatteryTest(config);
    if (result.success) {
      isBatteryTestRunning = true;
      batteryTestIdleEl.classList.add('hidden');
      batteryTestCompleteEl.classList.add('hidden');
      batteryTestRunningEl.classList.remove('hidden');
    } else {
      alert('Failed to start battery test: ' + result.error);
    }
  } catch (error) {
    alert('Error starting battery test: ' + error.message);
  }
}

async function stopBatteryTest() {
  try {
    await window.api.stopBatteryTest();
    isBatteryTestRunning = false;
    batteryTestIdleEl.classList.remove('hidden');
    batteryTestRunningEl.classList.add('hidden');
  } catch (error) {
    console.error('Error stopping battery test:', error);
  }
}

function formatTime(milliseconds) {
  const minutes = Math.floor(milliseconds / 60000);
  const seconds = Math.floor((milliseconds % 60000) / 1000);
  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
}

// Event Listeners
window.api.onStatusUpdate((data) => {
  if (data.connected !== undefined) {
    updateConnectionStatus(data.connected, data.laptopId);
  }
});

window.api.onBatteryUpdate((data) => {
  if (data.percentage !== undefined) {
    batteryPercentageEl.textContent = `${data.percentage}%`;
  }
});

window.api.onBayAssignmentStatus((data) => {
  bayStatusEl.classList.remove('hidden');
  
  switch (data.status) {
    case 'waiting':
      bayStatusEl.textContent = data.message || 'Waiting for bay assignment...';
      bayStatusEl.className = 'bay-status waiting';
      break;
    case 'detecting':
      bayStatusEl.textContent = data.message || 'Detecting power pattern...';
      bayStatusEl.className = 'bay-status detecting';
      break;
    case 'assigned':
      bayStatusEl.classList.add('hidden');
      updateBayInfo(data.bayNumber);
      break;
    case 'timeout':
      bayStatusEl.textContent = data.message || 'Assignment timed out';
      bayStatusEl.className = 'bay-status error';
      break;
  }
});

window.api.onBatteryTestUpdate((data) => {
  if (!isBatteryTestRunning) return;
  
  // Update phase
  const phaseText = data.phase === 'discharge' ? 'Discharging' : 'Charging';
  testPhaseEl.textContent = phaseText;
  
  // Update progress smoothly
  const progress = Math.round(data.progress);
  testProgressBarEl.style.width = `${progress}%`;
  testProgressBarEl.style.transition = 'width 1s ease-out';
  
  // Update battery info
  if (data.batteryInfo) {
    testBatteryEl.textContent = `${data.batteryInfo.percentage}%`;
    chargingIndicatorEl.textContent = data.batteryInfo.isCharging ? '⚡' : '';
  }
  
  // Update time remaining
  if (data.timeRemaining !== undefined) {
    testTimeEl.textContent = formatTime(data.timeRemaining);
  }
  
  // Show waiting status
  if (data.waitingForPowerChange) {
    const expectedState = data.phase === 'charge' ? 'Connect Power' : 'Disconnect Power';
    phaseInstructionEl.classList.remove('hidden');
    instructionTextEl.textContent = `Waiting for power change: ${expectedState}`;
  } else {
    // Hide instruction if not waiting
    phaseInstructionEl.classList.add('hidden');
  }
});

// Battery test phase change
window.api.onBatteryTestPhaseChange((data) => {
  phaseInstructionEl.classList.remove('hidden');
  instructionTextEl.textContent = data.instruction;
  
  // Hide instruction after 10 seconds
  setTimeout(() => {
    phaseInstructionEl.classList.add('hidden');
  }, 10000);
});

// Battery test complete
window.api.onBatteryTestComplete((results) => {
  isBatteryTestRunning = false;
  batteryTestRunningEl.classList.add('hidden');
  batteryTestCompleteEl.classList.remove('hidden');
  
  // Display results
  testResultsEl.innerHTML = `
    <div class="result-item">
      <span>Overall Result:</span>
      <span class="${results.overall.pass ? 'result-pass' : 'result-fail'}">
        ${results.overall.pass ? 'PASS' : 'FAIL'}
      </span>
    </div>
    <div class="result-item">
      <span>Battery Health Score:</span>
      <span>${results.overall.healthScore}%</span>
    </div>
    <div class="result-item">
      <span>Discharge Rate:</span>
      <span>${results.discharge.rate}% per hour</span>
    </div>
    <div class="result-item">
      <span>Charge Rate:</span>
      <span>${results.charge.rate}% per hour</span>
    </div>
    <div class="result-item">
      <span>Total Readings:</span>
      <span>${results.overall.details.totalReadings}</span>
    </div>
  `;
});

function resetBatteryTest() {
  batteryTestCompleteEl.classList.add('hidden');
  batteryTestIdleEl.classList.remove('hidden');
}

// Get initial system info
window.api.getSystemInfo().then((info) => {
  if (info.serialNumber) {
    serialNumberEl.textContent = info.serialNumber;
  }
  if (info.networkInfo) {
    networkInfoEl.textContent = info.networkInfo;
  }
});

// Listen for battery test started from dashboard
if (window.api.onBatteryTestStarted) {
  window.api.onBatteryTestStarted((data) => {
    console.log('Battery test started from dashboard');
    isBatteryTestRunning = true;
    
    // Update UI to show test is running
    batteryTestIdleEl.classList.add('hidden');
    batteryTestRunningEl.classList.remove('hidden');
    batteryTestCompleteEl.classList.add('hidden');
    
    // Reset progress
    testProgressBarEl.style.width = '0%';
    testPhaseEl.textContent = 'Starting...';
    testTimeEl.textContent = '--:--';
    
    // Show instruction if provided
    if (data && data.instruction) {
      phaseInstructionEl.classList.remove('hidden');
      instructionTextEl.textContent = data.instruction;
    }
  });
}

// Attach functions to window for onclick handlers
window.connect = connect;
window.switchTab = switchTab;
window.startBatteryTest = startBatteryTest;
window.stopBatteryTest = stopBatteryTest;
window.resetBatteryTest = resetBatteryTest;