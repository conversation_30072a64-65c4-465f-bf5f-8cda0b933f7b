const si = require('systeminformation');

class BatteryMonitor {
  constructor() {
    this.interval = null;
    this.powerCheckInterval = null;
    this.callback = null;
  }

  async getBatteryInfo() {
    try {
      const battery = await si.battery();
      return {
        percentage: Math.round(battery.percent),
        isCharging: battery.isCharging,
        voltage: battery.voltage || 0,
        temperature: battery.temperature || 0,
        cycleCount: battery.cycleCount || 0,
        health: battery.health || 0
      };
    } catch (error) {
      console.error('Error getting battery info:', error);
      return {
        percentage: 0,
        isCharging: false,
        voltage: 0,
        temperature: 0,
        cycleCount: 0,
        health: 0
      };
    }
  }

  startMonitoring(callback, intervalMs = 30000) { // Increased to 30s for lower CPU
    this.callback = callback;
    this.lastChargingState = null;
    
    // Get initial reading
    this.getBatteryInfo().then(info => {
      this.lastChargingState = info.isCharging;
      callback(info);
    });
    
    // Set up main interval for regular updates
    this.interval = setInterval(async () => {
      const info = await this.getBatteryInfo();
      if (this.callback) {
        this.callback(info);
      }
    }, intervalMs);
    
    // Set up separate interval for power state changes (reduced frequency)
    this.powerCheckInterval = setInterval(async () => {
      const info = await this.getBatteryInfo();
      
      // Check if charging state changed
      if (this.lastChargingState !== null && this.lastChargingState !== info.isCharging) {
        console.log(`Power state changed: ${this.lastChargingState ? 'Charging' : 'On Battery'} -> ${info.isCharging ? 'Charging' : 'On Battery'}`);
        // Immediately notify on power state change
        if (this.callback) {
          this.callback(info);
        }
      }
      
      this.lastChargingState = info.isCharging;
    }, 3000); // Reduced to 3 seconds for lower CPU usage
  }

  stopMonitoring() {
    if (this.interval) {
      clearInterval(this.interval);
      this.interval = null;
    }
    if (this.powerCheckInterval) {
      clearInterval(this.powerCheckInterval);
      this.powerCheckInterval = null;
    }
    this.callback = null;
  }
}

module.exports = BatteryMonitor;