{"name": "battery-test-agent", "version": "1.8.0", "description": "Battery testing agent for laptop QC", "main": "src/main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "dist": "electron-builder --win"}, "build": {"appId": "com.laptopqc.battery-agent", "productName": "Battery Test Agent", "directories": {"output": "dist-v1.8"}, "win": {"target": [{"target": "portable", "arch": ["x64"]}], "icon": "assets/icon.ico"}, "asar": {"smartUnpack": false}}, "keywords": ["battery", "testing", "qc"], "author": "Laptop QC Team", "license": "Private", "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.9.1"}, "dependencies": {"@supabase/supabase-js": "^2.39.0", "systeminformation": "^5.21.20", "dotenv": "^16.3.1"}}