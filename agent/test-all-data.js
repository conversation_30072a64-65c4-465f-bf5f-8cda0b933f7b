const si = require('systeminformation');

async function getAllSystemInfo() {
  console.log('Collecting all system information...\n');
  
  try {
    const system = await si.system();
    const bios = await si.bios();
    const cpu = await si.cpu();
    const mem = await si.mem();
    const battery = await si.battery();
    const graphics = await si.graphics();
    const networkInterfaces = await si.networkInterfaces();
    const diskLayout = await si.diskLayout();
    const osInfo = await si.osInfo();
    
    console.log('=== SYSTEM ===');
    console.log(JSON.stringify(system, null, 2));
    
    console.log('\n=== BIOS ===');
    console.log(JSON.stringify(bios, null, 2));
    
    console.log('\n=== CPU ===');
    console.log(JSON.stringify(cpu, null, 2));
    
    console.log('\n=== MEMORY ===');
    console.log(JSON.stringify(mem, null, 2));
    
    console.log('\n=== BATTERY ===');
    console.log(JSON.stringify(battery, null, 2));
    
    console.log('\n=== GRAPHICS ===');
    console.log(JSON.stringify(graphics, null, 2));
    
    console.log('\n=== NETWORK ===');
    console.log(JSON.stringify(networkInterfaces, null, 2));
    
    console.log('\n=== DISKS ===');
    console.log(JSON.stringify(diskLayout, null, 2));
    
    console.log('\n=== OS INFO ===');
    console.log(JSON.stringify(osInfo, null, 2));
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the test
getAllSystemInfo();