# Complete Windows System Data Collection Guide

## All Available System Information

### 1. Computer System Information
```powershell
# Get all computer system properties
Get-WmiObject Win32_ComputerSystem | Format-List *

# Key fields typically available:
# - Model: "OptiPlex 3070" or "EliteBook 840 G7"
# - Manufacturer: "Dell Inc." or "HP"
# - SystemFamily: "OptiPlex" or "EliteBook"
# - TotalPhysicalMemory: RAM in bytes
# - NumberOfProcessors: CPU count
# - Domain: Network domain
# - UserName: Current logged in user
```

### 2. BIOS Information
```powershell
# Get all BIOS properties
Get-WmiObject Win32_BIOS | Format-List *

# Key fields:
# - SerialNumber: System serial number
# - Manufacturer: BIOS manufacturer
# - Version: BIOS version
# - ReleaseDate: BIOS release date
# - SMBIOSBIOSVersion: SMBIOS version
```

### 3. Battery Information
```powershell
# Get all battery properties
Get-WmiObject Win32_Battery | Format-List *

# Key fields:
# - EstimatedChargeRemaining: Current percentage
# - BatteryStatus: 1=Other, 2=Unknown, 3=FullyCharged, 4=Low, 5=Critical, 6=Charging
# - Chemistry: Battery chemistry type (e.g., 6 = Lithium-ion)
# - DesignCapacity: Original capacity in mWh
# - FullChargeCapacity: Current full charge capacity
# - EstimatedRunTime: Minutes remaining
# - TimeToFullCharge: Minutes to full charge
# - Voltage: Current voltage
# - Status: "OK" or error status
```

### 4. Processor Information
```powershell
# Get all CPU properties
Get-WmiObject Win32_Processor | Format-List *

# Key fields:
# - Name: "Intel(R) Core(TM) i5-8265U CPU @ 1.60GHz"
# - NumberOfCores: Physical cores
# - NumberOfLogicalProcessors: Logical processors
# - MaxClockSpeed: Max speed in MHz
# - CurrentClockSpeed: Current speed
# - ProcessorId: Unique CPU ID
# - Manufacturer: "GenuineIntel" or "AuthenticAMD"
```

### 5. Memory (RAM) Information
```powershell
# Get physical memory details
Get-WmiObject Win32_PhysicalMemory | Format-List *

# Key fields per module:
# - Capacity: Size in bytes
# - Speed: Speed in MHz
# - Manufacturer: RAM manufacturer
# - SerialNumber: Module serial
# - PartNumber: Part number
# - FormFactor: 12=SODIMM (laptop), 8=DIMM (desktop)
```

### 6. Disk Information
```powershell
# Get disk drive details
Get-WmiObject Win32_DiskDrive | Format-List *

# Key fields:
# - Model: "Samsung SSD 970 EVO Plus 500GB"
# - SerialNumber: Disk serial
# - Size: Capacity in bytes
# - InterfaceType: "SCSI" (includes SATA/NVMe)
# - MediaType: "Fixed hard disk media"
# - FirmwareRevision: Firmware version
```

### 7. Network Adapter Information
```powershell
# Get network adapter details
Get-WmiObject Win32_NetworkAdapter | Where-Object {$_.PhysicalAdapter -eq $true} | Format-List *

# Key fields:
# - Name: Adapter name
# - MACAddress: MAC address
# - AdapterType: Ethernet or Wireless
# - Speed: Connection speed
# - Manufacturer: Network card manufacturer
```

### 8. Operating System Information
```powershell
# Get OS details
Get-WmiObject Win32_OperatingSystem | Format-List *

# Key fields:
# - Caption: "Microsoft Windows 10 Pro"
# - Version: "10.0.19043"
# - BuildNumber: "19043"
# - InstallDate: OS install date
# - LastBootUpTime: Last boot time
# - SerialNumber: Windows product ID
# - RegisteredUser: Registered to
```

### 9. Video Controller (GPU) Information
```powershell
# Get graphics card details
Get-WmiObject Win32_VideoController | Format-List *

# Key fields:
# - Name: "Intel(R) UHD Graphics 620"
# - AdapterRAM: Video memory in bytes
# - DriverVersion: Driver version
# - VideoProcessor: GPU processor name
```

### 10. Monitor Information
```powershell
# Get monitor details
Get-WmiObject Win32_DesktopMonitor | Format-List *
# Also try:
Get-WmiObject WmiMonitorID -Namespace root\wmi | Format-List *

# Key fields:
# - ManufacturerName: Monitor manufacturer
# - ProductCodeID: Model number
# - SerialNumberID: Monitor serial
# - YearOfManufacture: Manufacturing year
```

### 11. USB Devices
```powershell
# Get USB device details
Get-WmiObject Win32_USBHub | Format-List *

# Key fields:
# - DeviceID: USB device path
# - PNPDeviceID: Plug and Play ID
# - Description: Device description
```

### 12. Audio Devices
```powershell
# Get sound device details
Get-WmiObject Win32_SoundDevice | Format-List *

# Key fields:
# - Name: Audio device name
# - Manufacturer: Audio manufacturer
# - Status: Device status
```

### 13. Keyboard and Mouse
```powershell
# Keyboard
Get-WmiObject Win32_Keyboard | Format-List *

# Mouse/Trackpad
Get-WmiObject Win32_PointingDevice | Format-List *
```

### 14. SMART Disk Health
```powershell
# Get disk health status
Get-WmiObject -Namespace root\wmi -Class MSStorageDriver_FailurePredictStatus | Format-List *

# PredictFailure: False = Healthy, True = Failing
```

### 15. Temperature Sensors (if available)
```powershell
# Try to get temperature data
Get-WmiObject MSAcpi_ThermalZoneTemperature -Namespace "root/wmi" | Format-List *

# CurrentTemperature: In tenths of Kelvin
```

## Currently Collected by Agent

Based on the code, the agent currently collects:
1. **Serial Number** - From BIOS
2. **Model** - From ComputerSystem
3. **MAC Address** - From NetworkInterfaces
4. **Battery Percentage** - From Battery
5. **Battery Charging Status** - From Battery

## Recommended Additional Data to Collect

For laptop QC, consider adding:
1. **Battery Health**:
   - `DesignCapacity` vs `FullChargeCapacity` (health percentage)
   - `Chemistry` (battery type)
   - `CycleCount` (if available)

2. **Hardware Specs**:
   - `TotalPhysicalMemory` (RAM size)
   - `Processor Name` (CPU model)
   - `Disk Model and Size` (storage info)

3. **Diagnostics**:
   - `LastBootUpTime` (uptime)
   - `SMART status` (disk health)
   - `Windows Version` (OS info)

## Testing Script for All Data

Create `test-all-data.js`:
```javascript
const si = require('systeminformation');

async function getAllSystemInfo() {
  console.log('Collecting all system information...\n');
  
  const system = await si.system();
  const bios = await si.bios();
  const cpu = await si.cpu();
  const mem = await si.mem();
  const battery = await si.battery();
  const graphics = await si.graphics();
  const networkInterfaces = await si.networkInterfaces();
  const diskLayout = await si.diskLayout();
  const osInfo = await si.osInfo();
  
  console.log('=== SYSTEM ===');
  console.log(JSON.stringify(system, null, 2));
  
  console.log('\n=== BIOS ===');
  console.log(JSON.stringify(bios, null, 2));
  
  console.log('\n=== CPU ===');
  console.log(JSON.stringify(cpu, null, 2));
  
  console.log('\n=== MEMORY ===');
  console.log(JSON.stringify(mem, null, 2));
  
  console.log('\n=== BATTERY ===');
  console.log(JSON.stringify(battery, null, 2));
  
  console.log('\n=== GRAPHICS ===');
  console.log(JSON.stringify(graphics, null, 2));
  
  console.log('\n=== NETWORK ===');
  console.log(JSON.stringify(networkInterfaces, null, 2));
  
  console.log('\n=== DISKS ===');
  console.log(JSON.stringify(diskLayout, null, 2));
  
  console.log('\n=== OS INFO ===');
  console.log(JSON.stringify(osInfo, null, 2));
}

getAllSystemInfo().catch(console.error);
```

This will show you exactly what data the `systeminformation` npm package can access on Windows.