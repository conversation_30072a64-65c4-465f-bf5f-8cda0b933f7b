<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Laptop QC Agent</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            user-select: none;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
        }
        
        .card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        h1 {
            margin: 0 0 20px 0;
            color: #1e293b;
            font-size: 24px;
        }
        
        h2 {
            margin: 0 0 15px 0;
            color: #334155;
            font-size: 18px;
        }
        
        .status {
            font-size: 18px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .status.connected { color: #22c55e; }
        .status.disconnected { color: #ef4444; }
        
        .laptop-id {
            font-size: 48px;
            font-weight: bold;
            color: #1e293b;
            margin: 20px 0;
            letter-spacing: 2px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin: 15px 0;
        }
        
        .info-item {
            text-align: left;
        }
        
        .info-label {
            font-size: 12px;
            color: #64748b;
            text-transform: uppercase;
        }
        
        .info-value {
            font-size: 16px;
            color: #1e293b;
            font-weight: 500;
        }
        
        .battery-info {
            font-size: 24px;
            color: #64748b;
            margin: 15px 0;
        }
        
        .btn {
            border: none;
            padding: 10px 30px;
            font-size: 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
            font-weight: 500;
        }
        
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2563eb;
        }
        
        .btn-secondary {
            background: #e5e7eb;
            color: #374151;
        }
        
        .btn-secondary:hover {
            background: #d1d5db;
        }
        
        .btn-danger {
            background: #ef4444;
            color: white;
        }
        
        .btn-danger:hover {
            background: #dc2626;
        }
        
        .btn:disabled {
            background: #94a3b8;
            cursor: not-allowed;
        }
        
        .bay-status {
            margin: 15px 0;
            padding: 10px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
        }
        
        .bay-status.waiting {
            background: #fef3c7;
            color: #92400e;
        }
        
        .bay-status.detecting {
            background: #dbeafe;
            color: #1e40af;
            animation: pulse 2s infinite;
        }
        
        .bay-status.assigned {
            background: #d1fae5;
            color: #065f46;
        }
        
        .bay-number {
            font-size: 32px;
            font-weight: bold;
            color: #065f46;
            margin: 10px 0;
        }
        
        /* Battery Test Styles */
        .test-controls {
            margin: 20px 0;
        }
        
        .test-phase {
            font-size: 16px;
            color: #1e293b;
            margin-bottom: 10px;
            font-weight: 500;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e5e7eb;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: #3b82f6;
            transition: width 0.3s;
        }
        
        .test-info {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            font-size: 14px;
            color: #64748b;
        }
        
        .hidden {
            display: none;
        }
        
        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 2px solid #e5e7eb;
        }
        
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            margin-bottom: -2px;
            transition: all 0.2s;
        }
        
        .tab:hover {
            color: #3b82f6;
        }
        
        .tab.active {
            color: #3b82f6;
            border-bottom-color: #3b82f6;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .test-config {
            margin: 20px 0;
            padding: 15px;
            background: #f5f5f5;
            border-radius: 6px;
        }
        
        .config-item {
            margin: 10px 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .config-item label {
            font-size: 14px;
            color: #374151;
        }
        
        .input-number {
            width: 80px;
            padding: 5px 10px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 16px;
            text-align: center;
        }
        
        .instruction-box {
            background: #fef3c7;
            color: #92400e;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 15px;
            font-weight: 500;
            text-align: center;
            animation: pulse 2s infinite;
        }
        
        .test-results {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
        }
        
        .result-item {
            margin: 10px 0;
            display: flex;
            justify-content: space-between;
        }
        
        .result-pass {
            color: #065f46;
            font-weight: bold;
        }
        
        .result-fail {
            color: #dc2626;
            font-weight: bold;
        }
        
        @keyframes pulse {
            0% { opacity: 0.8; }
            50% { opacity: 1; }
            100% { opacity: 0.8; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Main Status Card -->
        <div class="card">
            <h1>Laptop QC Agent</h1>
            
            <div id="status" class="status disconnected">
                ● Disconnected
            </div>
            
            <div id="laptop-id" class="laptop-id hidden">
                A001
            </div>
            
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">Bay Assignment</div>
                    <div id="bay-info" class="info-value">Not Assigned</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Battery Status</div>
                    <div id="battery-percentage" class="info-value">--</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Serial Number</div>
                    <div id="serial-number" class="info-value">--</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Network</div>
                    <div id="network-info" class="info-value">--</div>
                </div>
            </div>
            
            <div id="bay-status" class="bay-status hidden"></div>
            
            <button id="connect-btn" class="btn btn-primary" onclick="connect()">
                Connect
            </button>
        </div>

        <!-- Test Controls (shown when connected) -->
        <div id="test-section" class="card hidden">
            <h2>Testing</h2>
            
            <div class="tabs">
                <div class="tab active" onclick="switchTab('battery')">Battery Test</div>
                <div class="tab" onclick="switchTab('hardware')">Hardware Test</div>
            </div>
            
            <!-- Battery Test Tab -->
            <div id="battery-tab" class="tab-content active">
                <div id="battery-test-idle">
                    <p>Run a timed battery charge/discharge test</p>
                    
                    <div class="test-config">
                        <div class="config-item">
                            <label>Discharge Duration (minutes):</label>
                            <input type="number" id="discharge-minutes" value="20" min="5" max="60" class="input-number">
                        </div>
                        <div class="config-item">
                            <label>Charge Duration (minutes):</label>
                            <input type="number" id="charge-minutes" value="20" min="5" max="60" class="input-number">
                        </div>
                    </div>
                    
                    <button class="btn btn-primary" onclick="startBatteryTest()">
                        Start Battery Test
                    </button>
                </div>
                
                <div id="battery-test-running" class="hidden">
                    <div id="phase-instruction" class="instruction-box hidden">
                        <span id="instruction-text"></span>
                    </div>
                    
                    <div class="test-phase">
                        Phase: <span id="test-phase">Initializing</span>
                    </div>
                    <div class="progress-bar">
                        <div id="test-progress-bar" class="progress-fill" style="width: 0%"></div>
                    </div>
                    <div class="test-info">
                        <span>Battery: <span id="test-battery">--</span>% <span id="charging-indicator"></span></span>
                        <span>Time Remaining: <span id="test-time">--</span></span>
                    </div>
                    <button class="btn btn-danger" onclick="stopBatteryTest()">
                        Stop Test
                    </button>
                </div>
                
                <div id="battery-test-complete" class="hidden">
                    <h3>Test Complete!</h3>
                    <div id="test-results" class="test-results">
                        <!-- Results will be populated here -->
                    </div>
                    <button class="btn btn-primary" onclick="resetBatteryTest()">
                        New Test
                    </button>
                </div>
            </div>
            
            <!-- Hardware Test Tab -->
            <div id="hardware-tab" class="tab-content">
                <p style="color: #64748b;">Hardware testing will be available in Phase 2</p>
                <ul style="text-align: left; color: #64748b;">
                    <li>Keyboard Test</li>
                    <li>Trackpad Test</li>
                    <li>Webcam Test</li>
                    <li>USB Port Test</li>
                    <li>Audio Test</li>
                </ul>
            </div>
        </div>
    </div>
    
    <script src="src/renderer.js"></script>
</body>
</html>