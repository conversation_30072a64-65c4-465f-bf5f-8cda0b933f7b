/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      animation: {
        'pulse-subtle': 'pulse-subtle 3s ease-in-out infinite',
        'slideDown': 'slideDown 0.3s ease-out',
      },
      keyframes: {
        'pulse-subtle': {
          '0%, 100%': { 
            opacity: '1',
            boxShadow: '0 0 0 0 rgba(34, 197, 94, 0)'
          },
          '50%': { 
            opacity: '0.95',
            boxShadow: '0 0 0 8px rgba(34, 197, 94, 0.1)'
          },
        },
        'slideDown': {
          '0%': { 
            opacity: '0',
            transform: 'translateY(-10px)'
          },
          '100%': { 
            opacity: '1',
            transform: 'translateY(0)'
          },
        }
      }
    },
  },
  plugins: [],
}