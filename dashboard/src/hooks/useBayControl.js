import { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { useRealtimeSubscription } from './useRealtimeSubscription';
import { bridgeService } from '../services/bridgeService';

/**
 * Custom hook for bay control and power management
 */
export function useBayControl() {
  const [powerStates, setPowerStates] = useState({});
  const [pendingActions, setPendingActions] = useState({});

  // Subscribe to power log updates with error handling
  useRealtimeSubscription('power_logs', (payload) => {
    try {
      if (payload.eventType === 'INSERT') {
        const { bay_number, action } = payload.new;
        setPowerStates(prev => ({
          ...prev,
          [bay_number]: action === 'on'
        }));
        
        // Clear pending action
        setPendingActions(prev => {
          const updated = { ...prev };
          delete updated[bay_number];
          return updated;
        });
      }
    } catch (error) {
      console.error('Error processing power log update:', error);
    }
  });

  // Power control action
  const controlPower = async (bayNumber, action) => {
    try {
      // Set pending state
      setPendingActions(prev => ({
        ...prev,
        [bayNumber]: action
      }));

      // Try bridge service first if available
      const bridgeAvailable = await bridgeService.checkConnection();
      if (bridgeAvailable) {
        const bridgeResult = await bridgeService.controlBayPower(bayNumber, action);
        if (bridgeResult.success) {
          console.log('Power controlled via bridge service');
        }
      }

      // Always log to database for tracking
      const { error } = await supabase
        .from('power_logs')
        .insert({
          bay_number: bayNumber,
          action: action,
          triggered_by: 'dashboard',
          success: bridgeAvailable
        });

      if (error) throw error;
      
      return { success: true };
    } catch (err) {
      console.error('Error controlling power:', err);
      
      // Clear pending state on error
      setPendingActions(prev => {
        const updated = { ...prev };
        delete updated[bayNumber];
        return updated;
      });
      
      return { success: false, error: err.message };
    }
  };

  // Power cycle pattern for bay assignment
  const powerCycleForAssignment = async (bayNumber) => {
    try {
      // Try bridge service first for coordinated pattern
      const bridgeAvailable = await bridgeService.checkConnection();
      if (bridgeAvailable) {
        const result = await bridgeService.executeBayAssignmentPattern(bayNumber);
        if (result.success) {
          console.log('Bay assignment pattern executed via bridge service');
          return { success: true };
        }
      }

      // Fallback to manual pattern if bridge not available
      console.log('Bridge service not available, using manual power pattern');
      const pattern = [
        { action: 'off', delay: 3000 },
        { action: 'on', delay: 3000 },
        { action: 'off', delay: 3000 },
        { action: 'on', delay: 5000 }
      ];

      for (const step of pattern) {
        await controlPower(bayNumber, step.action);
        await new Promise(resolve => setTimeout(resolve, step.delay));
      }
      
      return { success: true };
    } catch (err) {
      console.error('Error in power cycle pattern:', err);
      return { success: false, error: err.message };
    }
  };

  // Get power state for a bay
  const getPowerState = (bayNumber) => {
    return powerStates[bayNumber] !== false; // Default to ON
  };

  // Check if action is pending
  const isPending = (bayNumber) => {
    return pendingActions[bayNumber] !== undefined;
  };

  // Toggle power state
  const togglePower = async (bayNumber) => {
    const currentState = getPowerState(bayNumber);
    const newAction = currentState ? 'off' : 'on';
    return await controlPower(bayNumber, newAction);
  };

  return {
    controlPower,
    powerCycleForAssignment,
    executePowerPattern: powerCycleForAssignment, // Alias for consistency
    togglePower,
    getPowerState,
    isPending,
    isToggling: isPending, // Alias for backward compatibility
    powerStates
  };
}