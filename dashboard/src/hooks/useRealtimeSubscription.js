import { useEffect, useRef } from 'react';
import { supabase } from '../lib/supabase';

/**
 * Custom hook for Supabase realtime subscriptions
 * @param {string} table - Table name to subscribe to
 * @param {function} callback - Callback function for updates
 * @param {object} filter - Optional filter for subscription
 */
export function useRealtimeSubscription(table, callback, filter = null) {
  const subscriptionRef = useRef(null);
  const channelRef = useRef(null);

  useEffect(() => {
    // Skip if filter is explicitly null and we're expecting a filter
    if (filter === null && arguments.length > 2) {
      return;
    }
    
    // Create unique channel name based on table and filter
    const filterStr = filter ? `-${filter}` : '';
    const channelName = `${table}-changes${filterStr}-${Math.random().toString(36).substring(7)}`;
    
    // Cleanup previous subscription
    if (channelRef.current) {
      supabase.removeChannel(channelRef.current);
    }
    
    const channel = supabase.channel(channelName);
    channelRef.current = channel;
    
    const subscriptionConfig = {
      event: '*',
      schema: 'public',
      table: table
    };

    if (filter) {
      subscriptionConfig.filter = filter;
    }

    channel.on('postgres_changes', subscriptionConfig, (payload) => {
      callback(payload);
    });

    subscriptionRef.current = channel.subscribe();

    // Cleanup
    return () => {
      if (channelRef.current) {
        supabase.removeChannel(channelRef.current);
        channelRef.current = null;
        subscriptionRef.current = null;
      }
    };
  }, [table, filter]); // Remove callback from dependencies to prevent re-subscriptions

  return subscriptionRef.current;
}