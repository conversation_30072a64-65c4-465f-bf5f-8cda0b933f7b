import { useEffect } from 'react';

/**
 * Hook to handle page visibility changes
 * Useful for refreshing data when user returns to the dashboard
 */
export function usePageVisibility(onVisible, onHidden) {
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        if (onHidden) onHidden();
      } else {
        if (onVisible) onVisible();
      }
    };

    // Check initial state
    if (!document.hidden && onVisible) {
      onVisible();
    }

    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [onVisible, onHidden]);
}