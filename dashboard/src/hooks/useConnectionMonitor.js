import { useEffect, useRef } from 'react';
import { supabase } from '../lib/supabase';

/**
 * Hook to monitor laptop connections and detect disconnections
 * Works with 30-second heartbeat to reduce database writes
 */
export function useConnectionMonitor(laptops, onStatusChange) {
  const OFFLINE_THRESHOLD = 45000; // 45 seconds - allows for one missed heartbeat
  const CHECK_INTERVAL = 5000; // Check every 5 seconds
  const offlineTracker = useRef(new Set());

  useEffect(() => {
    const checkConnections = () => {
      const now = new Date();
      
      laptops.forEach(laptop => {
        if (laptop.status === 'connected') {
          const lastSeen = new Date(laptop.last_seen);
          const timeDiff = now - lastSeen;
          
          // If more than threshold and not already marked offline
          if (timeDiff > OFFLINE_THRESHOLD && !offlineTracker.current.has(laptop.id)) {
            offlineTracker.current.add(laptop.id);
            onStatusChange(laptop.id, 'idle');
          } else if (timeDiff <= OFFLINE_THRESHOLD && offlineTracker.current.has(laptop.id)) {
            // Back online
            offlineTracker.current.delete(laptop.id);
          }
        } else {
          // Not connected, remove from tracker
          offlineTracker.current.delete(laptop.id);
        }
      });
    };

    // Start monitoring
    const interval = setInterval(checkConnections, CHECK_INTERVAL);
    
    // Initial check
    checkConnections();
    
    return () => {
      clearInterval(interval);
      offlineTracker.current.clear();
    };
  }, [laptops, onStatusChange]);
}