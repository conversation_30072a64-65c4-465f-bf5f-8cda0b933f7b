import { useState, useEffect, useCallback } from 'react';
import { supabase } from '../lib/supabase';
import { useRealtimeSubscription } from './useRealtimeSubscription';

// Enrich laptop data with defaults for UI
const enrichLaptopData = (laptop) => {
  return {
    ...laptop,
    model: laptop.model || 'Unknown Model',
    last_operator: laptop.last_operator || null,
    current_test: null, // Will be populated from test_sessions
    last_test_result: null, // Will be populated from test_sessions
    battery_history: [], // Will be populated from battery_readings
    test_history: [] // Will be populated from test_sessions
  };
};

/**
 * Custom hook for managing laptop data with realtime updates
 */
export function useLaptops() {
  const [laptops, setLaptops] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch initial data
  useEffect(() => {
    fetchLaptops();
  }, []);

  // Memoize the realtime update handler
  const handleRealtimeUpdate = useCallback((payload) => {
    // Handle different event types
    if (payload.eventType === 'INSERT') {
      setLaptops(prev => {
        // Check if laptop already exists to avoid duplicates
        const exists = prev.find(l => l.id === payload.new.id);
        if (!exists) {
          return [...prev, enrichLaptopData(payload.new)];
        }
        return prev;
      });
    } else if (payload.eventType === 'UPDATE') {
      // Log phase transitions for debugging
      if (payload.new.test_phase !== payload.old?.test_phase) {
        console.log('Laptop phase transition detected:', {
          id: payload.new.id,
          oldPhase: payload.old?.test_phase,
          newPhase: payload.new.test_phase
        });
      }
      setLaptops(prev => {
        return prev.map(laptop => 
          laptop.id === payload.new.id ? enrichLaptopData(payload.new) : laptop
        );
      });
    } else if (payload.eventType === 'DELETE') {
      setLaptops(prev => 
        prev.filter(laptop => laptop.id !== payload.old.id)
      );
    }
  }, []);

  // Subscribe to realtime updates
  useRealtimeSubscription('laptops', handleRealtimeUpdate);

  const fetchLaptops = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('laptops')
        .select('*')
        .order('bay_number', { ascending: true, nullsFirst: false });

      if (error) throw error;
      // Enrich laptop data with mock fields for UI
      const enrichedData = (data || []).map(enrichLaptopData);
      setLaptops(enrichedData);
    } catch (err) {
      console.error('Error fetching laptops:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Update laptop bay assignment
  const assignBay = async (laptopId, bayNumber) => {
    try {
      const { error } = await supabase
        .from('laptops')
        .update({ 
          bay_number: bayNumber,
          bay_assignment_pending: bayNumber // Trigger for agent detection
        })
        .eq('id', laptopId);

      if (error) throw error;
      return { success: true };
    } catch (err) {
      console.error('Error assigning bay:', err);
      return { success: false, error: err.message };
    }
  };

  // Trigger power control for a bay
  const triggerPowerControl = async (bayNumber, action) => {
    try {
      const { error } = await supabase
        .from('power_logs')
        .insert({
          bay_number: bayNumber,
          action: action,
          triggered_by: 'dashboard'
        });

      if (error) throw error;
      return { success: true };
    } catch (err) {
      console.error('Error triggering power control:', err);
      return { success: false, error: err.message };
    }
  };

  // Update laptop status locally
  const updateLaptopStatus = useCallback((laptopId, newStatus) => {
    setLaptops(prev => prev.map(laptop => 
      laptop.id === laptopId 
        ? { ...laptop, status: newStatus }
        : laptop
    ));
  }, []);

  return {
    laptops,
    loading,
    error,
    assignBay,
    triggerPowerControl,
    refresh: fetchLaptops,
    refetch: fetchLaptops,
    updateLaptopStatus
  };
}