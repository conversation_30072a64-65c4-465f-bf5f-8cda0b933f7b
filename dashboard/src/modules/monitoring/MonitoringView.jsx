import React, { useState, useCallback } from 'react';
import { BayGrid } from '../../components/BayGrid';
import { LaptopCard } from '../../components/LaptopCard';
import { useLaptops } from '../../hooks/useLaptops';
import { useBayControl } from '../../hooks/useBayControl';
import { useConnectionMonitor } from '../../hooks/useConnectionMonitor';
import { supabase } from '../../lib/supabase';

/**
 * Main monitoring view for the dashboard
 */
export function MonitoringView() {
  const { laptops, loading, error, assignBay, updateLaptopStatus } = useLaptops();
  const { controlPower, powerCycleForAssignment, getPowerState, isPending, powerStates } = useBayControl();
  
  const [selectedLaptop, setSelectedLaptop] = useState(null);
  const [assignmentMode, setAssignmentMode] = useState(false);
  
  // Handle status changes from connection monitor
  const handleStatusChange = useCallback((laptopId, newStatus) => {
    // Update local state immediately for instant UI update
    updateLaptopStatus(laptopId, newStatus);
    
    // Also update database (fire and forget)
    supabase
      .from('laptops')
      .update({ status: newStatus })
      .eq('id', laptopId)
      .catch(error => console.error('Failed to update database:', error));
  }, [updateLaptopStatus]);
  
  // Monitor connections for quick disconnect detection
  useConnectionMonitor(laptops, handleStatusChange);
  
  // Get unassigned laptops
  const unassignedLaptops = laptops.filter(l => !l.bay_number && l.status === 'connected');
  
  // Handle automatic bay assignment
  const handleAutoAssignment = async (laptop, bayNumber) => {
    try {
      // Assign bay in database
      await assignBay(laptop.id, bayNumber);
      
      // Trigger power cycling pattern
      await powerCycleForAssignment(bayNumber);
      
      setAssignmentMode(false);
    } catch (err) {
      console.error('Assignment error:', err);
      alert('Failed to assign bay: ' + err.message);
    }
  };

  // Pending actions state for UI
  const pendingActions = {};
  for (let i = 1; i <= 30; i++) {
    if (isPending(i)) {
      pendingActions[i] = true;
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
        Error: {error}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Stats */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-4 gap-4">
          <div>
            <div className="text-sm text-gray-500">Total Laptops</div>
            <div className="text-2xl font-bold">{laptops.length}</div>
          </div>
          <div>
            <div className="text-sm text-gray-500">Connected</div>
            <div className="text-2xl font-bold text-green-600">
              {laptops.filter(l => l.status === 'connected').length}
            </div>
          </div>
          <div>
            <div className="text-sm text-gray-500">Testing</div>
            <div className="text-2xl font-bold text-yellow-600">
              {laptops.filter(l => l.status === 'testing').length}
            </div>
          </div>
          <div>
            <div className="text-sm text-gray-500">Unassigned</div>
            <div className="text-2xl font-bold text-blue-600">
              {unassignedLaptops.length}
            </div>
          </div>
        </div>
      </div>

      {/* Unassigned Laptops Alert */}
      {unassignedLaptops.length > 0 && (
        <div className="bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
          <h3 className="font-bold text-yellow-800 mb-2">
            {unassignedLaptops.length} Unassigned Laptop{unassignedLaptops.length > 1 ? 's' : ''}
          </h3>
          <div className="grid grid-cols-4 gap-3">
            {unassignedLaptops.map(laptop => (
              <div key={laptop.id} className="bg-white p-3 rounded border border-yellow-300">
                <div className="font-bold">{laptop.short_id}</div>
                <div className="text-sm text-gray-600">{laptop.serial_number}</div>
                <button
                  onClick={() => {
                    setSelectedLaptop(laptop);
                    setAssignmentMode(true);
                  }}
                  className="mt-2 text-sm bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600"
                >
                  Assign Bay
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Bay Grid */}
      <BayGrid
        laptops={laptops}
        onBaySelect={(bayNumber, laptop) => {
          if (assignmentMode && selectedLaptop && !laptop) {
            // Assign selected laptop to this empty bay
            if (confirm(`Assign ${selectedLaptop.short_id} to Bay ${bayNumber}?`)) {
              handleAutoAssignment(selectedLaptop, bayNumber);
            }
          }
        }}
        onPowerToggle={controlPower}
        powerStates={powerStates}
        pendingActions={pendingActions}
      />

      {/* All Laptops List */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-bold mb-4">All Laptops</h2>
        <div className="grid grid-cols-4 gap-4">
          {laptops.map(laptop => (
            <LaptopCard
              key={laptop.id}
              laptop={laptop}
              onSelect={setSelectedLaptop}
              isSelected={selectedLaptop?.id === laptop.id}
            />
          ))}
        </div>
      </div>
    </div>
  );
}