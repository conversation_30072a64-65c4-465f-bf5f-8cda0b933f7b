import React from 'react';

/**
 * Power control button component
 */
export function PowerControl({ bayNumber, isOn, isPending, onToggle }) {
  const handleClick = () => {
    if (!isPending) {
      onToggle(bayNumber, isOn ? 'off' : 'on');
    }
  };

  return (
    <button
      onClick={handleClick}
      disabled={isPending}
      className={`
        relative px-3 py-1 rounded-md font-medium text-sm transition-all
        ${isPending ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        ${isOn 
          ? 'bg-green-100 text-green-700 hover:bg-green-200' 
          : 'bg-red-100 text-red-700 hover:bg-red-200'
        }
      `}
    >
      {isPending ? (
        <span className="flex items-center">
          <svg className="animate-spin -ml-1 mr-2 h-3 w-3" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          ...
        </span>
      ) : (
        isOn ? 'ON' : 'OFF'
      )}
    </button>
  );
}