import React, { useState, useEffect } from 'react';
import { Battery, Zap, Clock, AlertCircle, Check } from 'lucide-react';
import { supabase } from '../../lib/supabase';
import { useRealtimeSubscription } from '../../hooks/useRealtimeSubscription';

const BatteryTestPanel = ({ laptop, session, onTestComplete }) => {
  const [testData, setTestData] = useState({
    phase: 'waiting',
    batteryPercentage: laptop.battery_percentage || 0,
    isCharging: laptop.is_charging || false,
    timeElapsed: 0,
    phaseTimeRemaining: 0,
    lastReading: null,
    waitingForPowerChange: false
  });
  const [phaseInstruction, setPhaseInstruction] = useState(null);

  // Load session configuration
  let sessionConfig = {
    dischargeMinutes: 20,
    chargeMinutes: 20
  };
  
  try {
    if (session?.notes) {
      const parsed = JSON.parse(session.notes);
      sessionConfig = parsed.config || sessionConfig;
    }
  } catch (e) {
    // Invalid JSON in notes
  }

  // Update test data when laptop data changes (real-time updates from agent)
  useEffect(() => {
    if (!session?.start_time) return;
    
    // Always prefer agent's real-time data when available
    if (laptop?.test_phase !== undefined && laptop?.test_phase !== null) {
      // Agent is providing real-time data, use it exclusively
      console.log('BatteryTestPanel: Updating from agent data', {
        phase: laptop.test_phase,
        progress: laptop.test_progress,
        timeRemaining: laptop.test_time_remaining,
        waitingForPower: laptop.test_waiting_for_power,
        battery: laptop.battery_percentage
      });
      
      // Calculate elapsed time based on session start
      const elapsed = Date.now() - new Date(session.start_time).getTime();
      
      setTestData(prev => ({
        ...prev,
        phase: laptop.test_phase,
        phaseTimeRemaining: laptop.test_time_remaining || 0,
        waitingForPowerChange: laptop.test_waiting_for_power || false,
        batteryPercentage: laptop.battery_percentage || prev.batteryPercentage,
        isCharging: laptop.is_charging !== undefined ? laptop.is_charging : prev.isCharging,
        timeElapsed: elapsed
      }));
    }
  }, [session?.start_time, laptop?.test_phase, laptop?.test_progress, laptop?.test_time_remaining, 
      laptop?.test_waiting_for_power, laptop?.battery_percentage, laptop?.is_charging]);

  // Fallback timer when no agent data
  useEffect(() => {
    if (!session?.start_time) return;
    
    // Only use local timer if no agent data
    if (laptop?.test_phase === undefined || laptop?.test_phase === null) {
      const interval = setInterval(() => {
        const elapsed = Date.now() - new Date(session.start_time).getTime();
        const elapsedMinutes = elapsed / 60000;
        
        let phase, phaseRemaining;
        if (elapsedMinutes < sessionConfig.dischargeMinutes) {
          phase = 'discharge';
          phaseRemaining = (sessionConfig.dischargeMinutes * 60000) - elapsed;
        } else if (elapsedMinutes < sessionConfig.dischargeMinutes + sessionConfig.chargeMinutes) {
          phase = 'charge';
          const chargeElapsed = elapsed - (sessionConfig.dischargeMinutes * 60000);
          phaseRemaining = (sessionConfig.chargeMinutes * 60000) - chargeElapsed;
        } else {
          phase = 'complete';
          phaseRemaining = 0;
        }

        setTestData(prev => ({
          ...prev,
          phase: phase,
          timeElapsed: elapsed,
          phaseTimeRemaining: Math.max(0, phaseRemaining)
        }));
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [session, sessionConfig, laptop?.test_phase]);

  // Subscribe to battery readings
  useRealtimeSubscription('battery_readings', (payload) => {
    if (payload.eventType === 'INSERT' && payload.new.session_id === session.id) {
      setTestData(prev => ({
        ...prev,
        batteryPercentage: payload.new.percentage,
        isCharging: payload.new.is_charging,
        lastReading: payload.new
      }));
    }
  }, session?.id ? `session_id=eq.${session.id}` : null);

  // Removed duplicate subscription - we get laptop updates from parent component

  // Subscribe to test session updates
  useRealtimeSubscription('test_sessions', (payload) => {
    if (payload.eventType === 'UPDATE' && payload.new.id === session.id) {
      // Check if test was stopped
      if (payload.new.end_time && !payload.old.end_time) {
        onTestComplete(payload.new);
      }
    }
  }, session?.id ? `id=eq.${session.id}` : null);


  // Check for phase changes and waiting state
  useEffect(() => {
    if (testData.waitingForPowerChange) {
      if (testData.phase === 'discharge') {
        setPhaseInstruction({
          type: 'warning',
          message: 'Waiting for power disconnection. Please disconnect the power cable.',
          action: 'Disconnect Power'
        });
      } else if (testData.phase === 'charge') {
        setPhaseInstruction({
          type: 'warning', 
          message: 'Waiting for power connection. Please connect the power cable.',
          action: 'Connect Power'
        });
      }
    } else if (testData.phase === 'discharge' && testData.isCharging) {
      setPhaseInstruction({
        type: 'info',
        message: 'Phase will start when power is disconnected',
        action: 'Waiting...'
      });
    } else if (testData.phase === 'charge' && !testData.isCharging) {
      setPhaseInstruction({
        type: 'info',
        message: 'Phase will start when power is connected',
        action: 'Waiting...'
      });
    } else {
      setPhaseInstruction(null);
    }
  }, [testData.phase, testData.isCharging, testData.waitingForPowerChange]);

  // Check for test completion
  useEffect(() => {
    const checkCompletion = async () => {
      const { data } = await supabase
        .from('test_sessions')
        .select('*')
        .eq('id', session.id)
        .single();
      
      if (data && data.end_time) {
        onTestComplete(data);
      }
    };

    if (testData.phase === 'complete') {
      checkCompletion();
    }
  }, [testData.phase, session.id, onTestComplete]);

  const formatTime = (milliseconds) => {
    const minutes = Math.floor(milliseconds / 60000);
    const seconds = Math.floor((milliseconds % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const getPhaseProgress = () => {
    // Use real-time progress from agent if available
    if (laptop?.test_progress !== undefined && laptop?.test_progress !== null) {
      return laptop.test_progress;
    }
    
    // Fallback to local calculation
    if (testData.phase === 'discharge') {
      return ((sessionConfig.dischargeMinutes * 60000 - testData.phaseTimeRemaining) / (sessionConfig.dischargeMinutes * 60000)) * 100;
    } else if (testData.phase === 'charge') {
      return ((sessionConfig.chargeMinutes * 60000 - testData.phaseTimeRemaining) / (sessionConfig.chargeMinutes * 60000)) * 100;
    }
    return 100;
  };

  const stopTest = async () => {
    try {
      await supabase
        .from('test_sessions')
        .update({
          end_time: new Date().toISOString(),
          result: 'incomplete',
          notes: JSON.stringify({
            ...JSON.parse(session.notes || '{}'),
            stopped_at: testData.phase,
            stopped_by: 'dashboard'
          })
        })
        .eq('id', session.id);

      await supabase
        .from('laptops')
        .update({ status: 'connected' })
        .eq('id', laptop.id);
    } catch (error) {
      console.error('Failed to stop test:', error);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold flex items-center">
          <Battery className="mr-2" size={20} />
          Battery Test Active - {laptop.short_id}
        </h3>
        <button
          onClick={stopTest}
          className="px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600"
        >
          Stop Test
        </button>
      </div>

      {/* Phase Instruction Alert */}
      {phaseInstruction && (
        <div className={`mb-4 p-3 rounded-lg flex items-center justify-between ${
          phaseInstruction.type === 'warning' ? 'bg-yellow-50 border border-yellow-200' : 'bg-blue-50 border border-blue-200'
        }`}>
          <div className="flex items-center">
            <AlertCircle className={`mr-2 ${
              phaseInstruction.type === 'warning' ? 'text-yellow-600' : 'text-blue-600'
            }`} size={20} />
            <span className={`font-medium ${
              phaseInstruction.type === 'warning' ? 'text-yellow-800' : 'text-blue-800'
            }`}>
              {phaseInstruction.message}
            </span>
          </div>
          <div className={`px-3 py-1 rounded text-sm font-medium ${
            phaseInstruction.type === 'warning' ? 'bg-yellow-200 text-yellow-800' : 'bg-blue-200 text-blue-800'
          }`}>
            {phaseInstruction.action}
          </div>
        </div>
      )}

      {/* Main Test Info */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
        <div>
          <div className="text-sm text-gray-600">Phase</div>
          <div className="font-semibold capitalize flex items-center">
            {testData.phase === 'discharge' ? (
              <>
                <Battery className="mr-1" size={16} />
                Discharging
              </>
            ) : testData.phase === 'charge' ? (
              <>
                <Zap className="mr-1 text-yellow-500" size={16} />
                Charging
              </>
            ) : testData.phase === 'complete' ? (
              <>
                <Check className="mr-1 text-green-500" size={16} />
                Complete
              </>
            ) : (
              'Waiting'
            )}
          </div>
        </div>

        <div>
          <div className="text-sm text-gray-600">Battery</div>
          <div className="font-semibold flex items-center">
            {testData.batteryPercentage}%
            {testData.isCharging && <Zap className="ml-1 text-yellow-500" size={14} />}
          </div>
        </div>

        <div>
          <div className="text-sm text-gray-600">Time Remaining</div>
          <div className="font-semibold">
            {formatTime(testData.phaseTimeRemaining)}
          </div>
        </div>

        <div>
          <div className="text-sm text-gray-600">Total Elapsed</div>
          <div className="font-semibold">
            {formatTime(testData.timeElapsed)}
          </div>
        </div>
      </div>

      {/* Phase Progress Bar */}
      <div className="mb-4">
        <div className="flex justify-between text-sm text-gray-600 mb-1">
          <span>{testData.phase === 'discharge' ? 'Discharge' : 'Charge'} Progress</span>
          <span>{Math.round(getPhaseProgress())}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-3">
          <div 
            className={`h-3 rounded-full transition-all duration-300 ${
              testData.phase === 'discharge' ? 'bg-orange-500' : 'bg-green-500'
            }`}
            style={{ width: `${getPhaseProgress()}%` }}
          />
        </div>
      </div>

      {/* Test Configuration */}
      <div className="text-sm text-gray-600">
        <Clock className="inline mr-1" size={14} />
        Test Duration: {sessionConfig.dischargeMinutes}min discharge + {sessionConfig.chargeMinutes}min charge
      </div>
    </div>
  );
};

export default BatteryTestPanel;