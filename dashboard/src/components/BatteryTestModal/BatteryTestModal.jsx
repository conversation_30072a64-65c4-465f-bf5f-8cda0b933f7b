import React, { useState } from 'react';
import { X, Battery, Clock } from 'lucide-react';
import { supabase } from '../../lib/supabase';

const BatteryTestModal = ({ laptop, onClose, onTestStarted }) => {
  const [testConfig, setTestConfig] = useState({
    dischargeMinutes: 5,
    chargeMinutes: 5,
    startWithCharge: false
  });
  const [loading, setLoading] = useState(false);

  const presets = [
    { label: '5 min test', discharge: 5, charge: 5 },
    { label: '20 min test', discharge: 20, charge: 20 },
    { label: '30 min test', discharge: 30, charge: 30 }
  ];

  const startTest = async () => {
    setLoading(true);
    try {
      // Create test session in database
      const { data: session, error } = await supabase
        .from('test_sessions')
        .insert({
          laptop_id: laptop.id,
          test_type: 'battery_manual',
          notes: JSON.stringify({
            config: testConfig,
            started_by: 'dashboard'
          })
        })
        .select()
        .single();

      if (error) throw error;

      // Update laptop status
      await supabase
        .from('laptops')
        .update({ status: 'testing' })
        .eq('id', laptop.id);

      onTestStarted(session);
      onClose();
    } catch (error) {
      console.error('Failed to start test:', error);
      alert('Failed to start battery test');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold flex items-center">
            <Battery className="mr-2" size={24} />
            Start Battery Test
          </h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X size={24} />
          </button>
        </div>

        <div className="mb-4">
          <p className="text-sm text-gray-600 mb-2">
            Laptop: <span className="font-medium">{laptop.short_id} - {laptop.model}</span>
          </p>
          <p className="text-sm text-gray-600">
            Bay: <span className="font-medium">{laptop.bay_number || 'Not assigned'}</span>
          </p>
        </div>

        <div className="mb-6">
          <h3 className="text-sm font-medium mb-3">Quick Presets</h3>
          <div className="grid grid-cols-3 gap-2">
            {presets.map(preset => (
              <button
                key={preset.label}
                onClick={() => setTestConfig({
                  dischargeMinutes: preset.discharge,
                  chargeMinutes: preset.charge
                })}
                className={`p-2 rounded border text-sm transition-colors ${
                  testConfig.dischargeMinutes === preset.discharge
                    ? 'bg-blue-50 border-blue-500 text-blue-700'
                    : 'bg-white border-gray-300 hover:border-gray-400'
                }`}
              >
                {preset.label}
              </button>
            ))}
          </div>
        </div>

        <div className="mb-6 space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">
              Discharge Duration (minutes)
            </label>
            <input
              type="number"
              min="1"
              max="120"
              value={testConfig.dischargeMinutes}
              onChange={(e) => setTestConfig({
                ...testConfig,
                dischargeMinutes: parseInt(e.target.value) || 1
              })}
              className="w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">
              Charge Duration (minutes)
            </label>
            <input
              type="number"
              min="1"
              max="120"
              value={testConfig.chargeMinutes}
              onChange={(e) => setTestConfig({
                ...testConfig,
                chargeMinutes: parseInt(e.target.value) || 1
              })}
              className="w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div>
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={testConfig.startWithCharge}
                onChange={(e) => setTestConfig({
                  ...testConfig,
                  startWithCharge: e.target.checked
                })}
                className="rounded text-blue-600"
              />
              <span className="text-sm font-medium">
                Start with charge phase first (default: discharge first)
              </span>
            </label>
          </div>
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded p-3 mb-4">
          <p className="text-sm text-yellow-800">
            <strong>Manual Test Instructions:</strong>
          </p>
          <ol className="text-sm text-yellow-700 mt-1 list-decimal list-inside">
            <li>{testConfig.startWithCharge ? 'Agent will prompt to connect power for charge phase' : 'Agent will prompt to disconnect power for discharge phase'}</li>
            <li>Test waits for actual power state change before proceeding</li>
            <li>{testConfig.startWithCharge ? 'After charge time, disconnect power for discharge phase' : 'After discharge time, reconnect power for charge phase'}</li>
            <li>Test will complete automatically after both phases</li>
          </ol>
        </div>

        <div className="flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={startTest}
            disabled={loading}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Starting...
              </>
            ) : (
              <>
                <Clock className="mr-2" size={16} />
                Start Test ({testConfig.dischargeMinutes + testConfig.chargeMinutes} min total)
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default BatteryTestModal;