import React, { useState } from 'react';
import { PowerControl } from '../PowerControl';
import { StatusIndicator } from '../StatusIndicator';

/**
 * Grid component for bay management (1-30 bays)
 */
export function BayGrid({ laptops, onBaySelect, onPowerToggle, powerStates, pendingActions }) {
  const [selectedBay, setSelectedBay] = useState(null);
  
  // Create array of 30 bays
  const bays = Array.from({ length: 30 }, (_, i) => i + 1);
  
  // Map laptops to bays
  const laptopsByBay = {};
  laptops.forEach(laptop => {
    if (laptop.bay_number) {
      laptopsByBay[laptop.bay_number] = laptop;
    }
  });

  const handleBayClick = (bayNumber) => {
    setSelectedBay(bayNumber);
    if (onBaySelect) {
      onBaySelect(bayNumber, laptopsByBay[bayNumber]);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-xl font-bold mb-4">Bay Management</h2>
      
      <div className="grid grid-cols-6 gap-3">
        {bays.map(bayNumber => {
          const laptop = laptopsByBay[bayNumber];
          const isOn = powerStates ? powerStates[bayNumber] !== false : true;
          const isPending = pendingActions ? pendingActions[bayNumber] !== undefined : false;
          
          return (
            <div
              key={bayNumber}
              onClick={() => handleBayClick(bayNumber)}
              className={`
                relative p-3 border-2 rounded-lg cursor-pointer transition-all
                ${selectedBay === bayNumber ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}
                ${laptop ? 'bg-gray-50' : 'bg-white'}
              `}
            >
              <div className="text-center">
                <div className="font-bold text-lg mb-1">Bay {bayNumber}</div>
                
                {laptop ? (
                  <>
                    <div className="text-sm font-medium text-gray-700 mb-1">
                      {laptop.short_id}
                    </div>
                    <div className="flex justify-center mb-2">
                      <StatusIndicator status={laptop.status} size="sm" />
                    </div>
                  </>
                ) : (
                  <div className="text-sm text-gray-400 mb-3">Empty</div>
                )}
                
                <PowerControl
                  bayNumber={bayNumber}
                  isOn={isOn}
                  isPending={isPending}
                  onToggle={onPowerToggle}
                />
              </div>
            </div>
          );
        })}
      </div>
      
      {/* Legend */}
      <div className="mt-6 flex items-center gap-6 text-sm text-gray-600">
        <div className="flex items-center gap-2">
          <StatusIndicator status="connected" size="sm" />
          <span>Connected</span>
        </div>
        <div className="flex items-center gap-2">
          <StatusIndicator status="testing" size="sm" />
          <span>Testing</span>
        </div>
        <div className="flex items-center gap-2">
          <StatusIndicator status="idle" size="sm" />
          <span>Idle</span>
        </div>
      </div>
    </div>
  );
}