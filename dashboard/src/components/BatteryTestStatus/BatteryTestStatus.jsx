import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { useRealtimeSubscription } from '../../hooks/useRealtimeSubscription';

export function BatteryTestStatus({ laptopId }) {
  const [testSession, setTestSession] = useState(null);
  const [latestReading, setLatestReading] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  // Load active test session
  useEffect(() => {
    loadTestSession();
  }, [laptopId]);

  // Subscribe to battery readings
  useRealtimeSubscription('battery_readings', (payload) => {
    if (payload.eventType === 'INSERT' && 
        payload.new.session_id === testSession?.id) {
      setLatestReading(payload.new);
    }
  }, testSession ? `session_id=eq.${testSession.id}` : null);

  const loadTestSession = async () => {
    try {
      const { data, error } = await supabase
        .from('test_sessions')
        .select('*')
        .eq('laptop_id', laptopId)
        .eq('test_type', 'battery')
        .is('end_time', null)
        .order('start_time', { ascending: false })
        .limit(1)
        .single();

      if (!error && data) {
        setTestSession(data);
        // Load latest reading
        const { data: reading } = await supabase
          .from('battery_readings')
          .select('*')
          .eq('session_id', data.id)
          .order('timestamp', { ascending: false })
          .limit(1)
          .single();
        
        if (reading) setLatestReading(reading);
      }
    } catch (error) {
      console.error('Failed to load test session:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const startBatteryTest = async () => {
    try {
      // Create test session via dashboard (agent will detect and start)
      const { data, error } = await supabase
        .from('test_sessions')
        .insert({
          laptop_id: laptopId,
          test_type: 'battery',
          operator_id: 'dashboard-initiated'
        })
        .select()
        .single();

      if (!error) {
        setTestSession(data);
      }
    } catch (error) {
      console.error('Failed to start battery test:', error);
      alert('Failed to start battery test');
    }
  };

  const stopBatteryTest = async () => {
    if (!testSession) return;

    try {
      await supabase
        .from('test_sessions')
        .update({
          end_time: new Date().toISOString(),
          result: 'incomplete',
          notes: 'Stopped by operator'
        })
        .eq('id', testSession.id);

      setTestSession(null);
      setLatestReading(null);
    } catch (error) {
      console.error('Failed to stop battery test:', error);
    }
  };

  if (isLoading) {
    return <div className="text-gray-500">Loading...</div>;
  }

  if (!testSession) {
    return (
      <div className="space-y-2">
        <p className="text-sm text-gray-600">No active battery test</p>
        <button
          onClick={startBatteryTest}
          className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
        >
          Start Battery Test
        </button>
      </div>
    );
  }

  const testDuration = new Date() - new Date(testSession.start_time);
  const testMinutes = Math.floor(testDuration / 60000);
  
  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <h4 className="font-semibold text-sm">Battery Test Active</h4>
        <button
          onClick={stopBatteryTest}
          className="px-2 py-1 bg-red-500 text-white rounded text-xs hover:bg-red-600"
        >
          Stop Test
        </button>
      </div>

      <div className="grid grid-cols-2 gap-2 text-sm">
        <div>
          <span className="text-gray-600">Duration:</span>
          <span className="ml-1 font-medium">{testMinutes} min</span>
        </div>
        
        {latestReading && (
          <>
            <div>
              <span className="text-gray-600">Battery:</span>
              <span className="ml-1 font-medium">{latestReading.percentage}%</span>
            </div>
            <div>
              <span className="text-gray-600">Status:</span>
              <span className={`ml-1 font-medium ${
                latestReading.is_charging ? 'text-green-600' : 'text-yellow-600'
              }`}>
                {latestReading.is_charging ? 'Charging' : 'Discharging'}
              </span>
            </div>
            <div>
              <span className="text-gray-600">Voltage:</span>
              <span className="ml-1 font-medium">{latestReading.voltage}V</span>
            </div>
          </>
        )}
      </div>

      {/* Progress bar */}
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div 
          className="bg-blue-500 h-2 rounded-full transition-all duration-300"
          style={{ width: `${Math.min(100, (testMinutes / 60) * 100)}%` }}
        />
      </div>
    </div>
  );
}