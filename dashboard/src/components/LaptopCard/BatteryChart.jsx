import React from 'react';

const BatteryChart = ({ data }) => {
  if (!data || data.length === 0) return null;
  
  // Get min and max for scaling
  const percentages = data.map(d => d.percentage);
  const min = Math.min(...percentages);
  const max = Math.max(...percentages);
  const range = max - min || 1;
  
  // Create SVG path
  const width = 300;
  const height = 60;
  const padding = 10;
  
  const points = data.map((d, i) => {
    const x = (i / (data.length - 1)) * (width - 2 * padding) + padding;
    const y = height - ((d.percentage - min) / range) * (height - 2 * padding) - padding;
    return `${x},${y}`;
  }).join(' ');
  
  return (
    <div className="space-y-2">
      <h4 className="text-sm font-medium text-gray-700">Battery Trend</h4>
      <div className="bg-gray-50 rounded p-2">
        <svg width={width} height={height} className="w-full h-auto">
          {/* Grid lines */}
          <line x1={padding} y1={height - padding} x2={width - padding} y2={height - padding} stroke="#e5e7eb" strokeWidth="1" />
          <line x1={padding} y1={padding} x2={padding} y2={height - padding} stroke="#e5e7eb" strokeWidth="1" />
          
          {/* Polyline */}
          <polyline
            points={points}
            fill="none"
            stroke="#3b82f6"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          
          {/* Data points */}
          {data.map((d, i) => {
            const x = (i / (data.length - 1)) * (width - 2 * padding) + padding;
            const y = height - ((d.percentage - min) / range) * (height - 2 * padding) - padding;
            return (
              <circle
                key={i}
                cx={x}
                cy={y}
                r="3"
                fill={d.is_charging ? '#10b981' : '#3b82f6'}
                title={`${d.percentage}% ${d.is_charging ? '(Charging)' : ''}`}
              />
            );
          })}
          
          {/* Labels */}
          <text x={padding} y={padding - 2} fontSize="10" fill="#6b7280">{max}%</text>
          <text x={padding} y={height - 2} fontSize="10" fill="#6b7280">{min}%</text>
        </svg>
        
        <div className="flex justify-between text-xs text-gray-500 mt-1">
          <span>{data[0].timestamp ? new Date(data[0].timestamp).toLocaleTimeString() : 'Start'}</span>
          <span>{data[data.length - 1].timestamp ? new Date(data[data.length - 1].timestamp).toLocaleTimeString() : 'End'}</span>
        </div>
      </div>
    </div>
  );
};

export default BatteryChart;