import React from 'react';

const TestProgress = ({ testType, progress, phase }) => {
  const getTestIcon = (type) => {
    switch (type) {
      case 'battery': return '🔋';
      case 'hardware': return '🖥️';
      case 'full_qc': return '✓';
      default: return '🔧';
    }
  };
  
  const getPhaseColor = (phase) => {
    switch (phase) {
      case 'discharge': return 'text-orange-600';
      case 'charge': return 'text-green-600';
      case 'testing': return 'text-blue-600';
      default: return 'text-gray-600';
    }
  };
  
  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <span className="text-lg">{getTestIcon(testType)}</span>
          <span className="font-medium text-gray-700 capitalize">{testType} Test</span>
          {phase && (
            <span className={`text-sm ${getPhaseColor(phase)}`}>
              ({phase})
            </span>
          )}
        </div>
        <span className="text-sm font-medium text-gray-700">{Math.round(progress)}%</span>
      </div>
      
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div 
          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
          style={{ width: `${progress}%` }}
        />
      </div>
    </div>
  );
};

export default TestProgress;