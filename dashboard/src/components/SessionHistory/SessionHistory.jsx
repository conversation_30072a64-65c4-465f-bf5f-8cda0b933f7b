import React, { useState, useEffect } from 'react';
import { ChevronDown, ChevronUp, Clock, CheckCircle, XCircle, AlertCircle, Database } from 'lucide-react';
import { supabase } from '../../lib/supabase';
import BatteryReadingsModal from '../BatteryReadingsModal';

const SessionHistory = ({ laptopId }) => {
  const [sessions, setSessions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [expanded, setExpanded] = useState(false);
  const [selectedSession, setSelectedSession] = useState(null);

  useEffect(() => {
    loadSessions();
  }, [laptopId]);

  const loadSessions = async () => {
    try {
      const { data, error } = await supabase
        .from('test_sessions')
        .select(`
          *,
          battery_readings (
            id,
            percentage,
            is_charging
          )
        `)
        .eq('laptop_id', laptopId)
        .eq('test_type', 'battery_manual')
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Process sessions to extract useful info
      const processedSessions = data.map(session => {
        let config = null;
        let results = null;
        try {
          if (session.notes) {
            const parsed = JSON.parse(session.notes);
            config = parsed.config;
            if (session.result) {
              results = parsed;
            }
          }
        } catch (e) {
          // Invalid JSON in notes
        }
        const duration = session.end_time 
          ? (new Date(session.end_time) - new Date(session.start_time)) / 60000
          : null;

        return {
          ...session,
          config,
          results,
          duration,
          readingCount: session.battery_readings?.length || 0
        };
      });

      setSessions(processedSessions);
    } catch (error) {
      console.error('Failed to load sessions:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (session) => {
    if (!session.end_time) return <AlertCircle className="text-blue-500" size={16} />;
    if (session.result === 'pass') return <CheckCircle className="text-green-500" size={16} />;
    if (session.result === 'fail') return <XCircle className="text-red-500" size={16} />;
    return <AlertCircle className="text-yellow-500" size={16} />;
  };

  const formatDate = (date) => {
    return new Date(date).toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  if (loading) {
    return <div className="text-sm text-gray-500">Loading sessions...</div>;
  }

  if (sessions.length === 0) {
    return <div className="text-sm text-gray-500">No test sessions yet</div>;
  }

  // Show latest session summary
  const latestSession = sessions[0];

  return (
    <div className="space-y-2">
      {/* Latest Session Summary */}
      <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
        <div className="flex items-center space-x-2">
          {getStatusIcon(latestSession)}
          <span className="text-sm font-medium">
            Latest: {formatDate(latestSession.start_time)}
          </span>
          {latestSession.results?.overall && (
            <span className="text-sm text-gray-600">
              Health: {latestSession.results.overall.healthScore}%
            </span>
          )}
        </div>
        <button
          onClick={() => setExpanded(!expanded)}
          className="flex items-center text-sm text-blue-600 hover:text-blue-700"
        >
          {sessions.length} session{sessions.length > 1 ? 's' : ''}
          {expanded ? <ChevronUp size={16} className="ml-1" /> : <ChevronDown size={16} className="ml-1" />}
        </button>
      </div>

      {/* Expanded Session List */}
      {expanded && (
        <div className="border border-gray-200 rounded-lg overflow-hidden">
          <div className="max-h-64 overflow-y-auto">
            {sessions.map((session, index) => (
              <div
                key={session.id}
                className={`p-3 ${index !== sessions.length - 1 ? 'border-b border-gray-200' : ''}`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      {getStatusIcon(session)}
                      <span className="text-sm font-medium">
                        {formatDate(session.start_time)}
                      </span>
                      <span className="text-xs text-gray-500">
                        {session.readingCount} readings
                      </span>
                    </div>
                    
                    {/* Test Configuration */}
                    {session.config && (
                      <div className="text-xs text-gray-600 mb-1">
                        <Clock size={12} className="inline mr-1" />
                        {session.config.dischargeMinutes}min discharge + {session.config.chargeMinutes}min charge
                        {session.duration && (
                          <span className="ml-2">
                            (Actual: {Math.round(session.duration)}min)
                          </span>
                        )}
                      </div>
                    )}

                    {/* Laptop Battery Report (from laptop) */}
                    {(session.laptop_battery_health !== null || session.laptop_design_capacity !== null) && (
                      <div className="bg-gray-50 rounded p-2 mb-2 text-xs">
                        <div className="font-medium text-gray-700 mb-1">📊 Laptop Battery Report (at test start)</div>
                        {session.laptop_battery_health !== null && (
                          <div>
                            Health: <span className={`font-medium ${
                              session.laptop_battery_health >= 80 ? 'text-green-600' : 
                              session.laptop_battery_health >= 50 ? 'text-yellow-600' : 'text-red-600'
                            }`}>{session.laptop_battery_health}% ({
                              session.laptop_battery_health >= 80 ? 'GOOD' :
                              session.laptop_battery_health >= 50 ? 'FAIR' : 'POOR'
                            })</span>
                          </div>
                        )}
                        {session.laptop_design_capacity && session.laptop_full_charge_capacity && (
                          <div>
                            Capacity: {session.laptop_full_charge_capacity.toLocaleString()} / {session.laptop_design_capacity.toLocaleString()} mWh
                          </div>
                        )}
                        {session.laptop_cycle_count !== null && (
                          <div>Cycles: {session.laptop_cycle_count}</div>
                        )}
                        {!session.laptop_battery_health && session.laptop_design_capacity && session.laptop_full_charge_capacity && (
                          <div>
                            Status: {session.laptop_full_charge_capacity < session.laptop_design_capacity * 0.5 ? 'Replacement recommended' : 'Normal wear'}
                          </div>
                        )}
                      </div>
                    )}

                    {/* Test Results Summary (from our test) */}
                    {session.results?.overall && (
                      <div className="text-xs space-y-1">
                        <div className="font-medium text-gray-700">🧪 Test Results</div>
                        <div>
                          Health Score: <span className="font-medium">{session.results.overall.healthScore}%</span>
                        </div>
                        {session.results.discharge && (
                          <div>
                            Discharge Rate: <span className="font-medium">{session.results.discharge.rate}%/hr</span>
                          </div>
                        )}
                        {session.results.charge && (
                          <div>
                            Charge Rate: <span className="font-medium">{session.results.charge.rate}%/hr</span>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Incomplete Session Info */}
                    {!session.end_time && (
                      <div className="text-xs text-blue-600">Test in progress...</div>
                    )}
                    {session.result === 'incomplete' && (
                      <div className="text-xs text-yellow-600">Test was stopped</div>
                    )}
                  </div>

                  {/* Actions */}
                  <div className="ml-3 flex items-center space-x-2">
                    <button
                      onClick={() => setSelectedSession(session)}
                      className="text-blue-600 hover:text-blue-700 p-1"
                      title="View readings"
                    >
                      <Database size={16} />
                    </button>
                    <span className={`text-xs px-2 py-1 rounded ${
                      !session.end_time ? 'bg-blue-100 text-blue-700' :
                      session.result === 'pass' ? 'bg-green-100 text-green-700' :
                      session.result === 'fail' ? 'bg-red-100 text-red-700' :
                      'bg-yellow-100 text-yellow-700'
                    }`}>
                      {!session.end_time ? 'Active' :
                       session.result === 'pass' ? 'Pass' :
                       session.result === 'fail' ? 'Fail' :
                       'Incomplete'}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Battery Readings Modal */}
      {selectedSession && (
        <BatteryReadingsModal
          session={selectedSession}
          onClose={() => setSelectedSession(null)}
        />
      )}
    </div>
  );
};

export default SessionHistory;