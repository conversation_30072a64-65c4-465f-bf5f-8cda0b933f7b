import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';

export function ConnectionStatus() {
  const [isConnected, setIsConnected] = useState(false);
  const [connectionCount, setConnectionCount] = useState(0);

  useEffect(() => {
    // Monitor all realtime channels
    const checkConnection = () => {
      const channels = supabase.getChannels();
      const activeChannels = channels.filter(channel => 
        channel.state === 'joined' || channel.state === 'joining'
      );
      
      setConnectionCount(activeChannels.length);
      setIsConnected(activeChannels.length > 0);
    };

    // Initial check
    checkConnection();

    // Check periodically
    const interval = setInterval(checkConnection, 5000);

    // Subscribe to connection state changes
    const channel = supabase.channel('connection-monitor')
      .on('system', { event: 'connected' }, () => {
        setIsConnected(true);
        checkConnection();
      })
      .on('system', { event: 'disconnected' }, () => {
        setIsConnected(false);
        checkConnection();
      })
      .subscribe();

    return () => {
      clearInterval(interval);
      supabase.removeChannel(channel);
    };
  }, []);

  return (
    <div className="flex items-center space-x-2">
      {/* Wi-Fi Icon */}
      <div className={`relative ${isConnected ? 'text-green-600' : 'text-gray-400'}`}>
        <svg 
          className="w-5 h-5" 
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path strokeLinecap="round" strokeLinejoin="round" d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0" />
        </svg>
        
        {/* Status dot */}
        <div className={`absolute -bottom-1 -right-1 w-2 h-2 rounded-full ${
          isConnected ? 'bg-green-500 animate-pulse' : 'bg-gray-400'
        }`} />
      </div>
      
      {/* Status text */}
      <span className="text-sm text-gray-600">
        {isConnected ? `Connected (${connectionCount} channels)` : 'Connecting...'}
      </span>
    </div>
  );
}