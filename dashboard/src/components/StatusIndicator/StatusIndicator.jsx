import React from 'react';

/**
 * Reusable status indicator component
 */
export function StatusIndicator({ status, size = 'md' }) {
  const sizeClasses = {
    sm: 'w-2 h-2',
    md: 'w-3 h-3',
    lg: 'w-4 h-4'
  };

  const statusColors = {
    connected: 'bg-green-500',
    testing: 'bg-yellow-500',
    idle: 'bg-gray-400',
    error: 'bg-red-500',
    pending: 'bg-blue-500'
  };

  const pulseAnimation = ['testing', 'pending'].includes(status) ? 'animate-pulse' : '';

  return (
    <span 
      className={`inline-block rounded-full ${sizeClasses[size]} ${statusColors[status] || 'bg-gray-300'} ${pulseAnimation}`}
      title={status}
    />
  );
}