import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  LayoutDashboard, 
  FileText, 
  TestTube2, 
  Download, 
  Settings,
  Wifi,
  User
} from 'lucide-react';
import { ConnectionStatus } from '../ConnectionStatus/ConnectionStatus';

const Layout = ({ children }) => {
  const location = useLocation();
  
  const navigation = [
    { name: 'Dashboard', href: '/', icon: LayoutDashboard },
    { name: 'Reports', href: '/reports', icon: FileText },
    { name: 'Testing', href: '/testing', icon: TestTube2 },
    { name: 'Agent', href: '/agent', icon: Download },
    { name: 'Settings', href: '/settings', icon: Settings },
  ];
  
  const isActive = (path) => {
    return location.pathname === path;
  };
  
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b fixed top-0 left-0 right-0 z-50">
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo and Navigation */}
            <div className="flex items-center space-x-8">
              <div className="flex items-center">
                <span className="text-xl font-bold text-gray-900">Laptop QC</span>
              </div>
              
              <nav className="hidden md:flex space-x-1">
                {navigation.map((item) => (
                  <Link
                    key={item.name}
                    to={item.href}
                    className={`
                      flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors
                      ${isActive(item.href)
                        ? 'bg-blue-50 text-blue-700'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                      }
                    `}
                  >
                    <item.icon size={16} className="mr-2" />
                    {item.name}
                  </Link>
                ))}
              </nav>
            </div>
            
            {/* Right Section */}
            <div className="flex items-center space-x-4">
              <ConnectionStatus />
              
              {/* User Menu */}
              <div className="relative">
                <button className="flex items-center space-x-2 text-gray-600 hover:text-gray-900">
                  <User size={20} />
                  <span className="text-sm font-medium">Admin</span>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </header>
      
      {/* Main Content */}
      <main className="pt-16">
        {children}
      </main>
    </div>
  );
};

export default Layout;