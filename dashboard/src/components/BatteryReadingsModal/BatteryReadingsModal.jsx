import React, { useState, useEffect } from 'react';
import { X, Download, Battery, Zap, Clock, TrendingDown, TrendingUp } from 'lucide-react';
import { supabase } from '../../lib/supabase';

const BatteryReadingsModal = ({ session, onClose }) => {
  const [readings, setReadings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all'); // all, discharge, charge

  useEffect(() => {
    loadReadings();
  }, [session.id]);

  const loadReadings = async () => {
    try {
      const { data, error } = await supabase
        .from('battery_readings')
        .select('*')
        .eq('session_id', session.id)
        .order('timestamp', { ascending: true });

      if (error) throw error;
      setReadings(data || []);
    } catch (error) {
      console.error('Failed to load readings:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredReadings = readings.filter(reading => {
    if (filter === 'all') return true;
    if (filter === 'discharge') return !reading.is_charging;
    if (filter === 'charge') return reading.is_charging;
    return true;
  });

  const exportCSV = () => {
    const headers = ['Timestamp', 'Battery %', 'Voltage', 'Temperature', 'Charging', 'Power Source'];
    const rows = filteredReadings.map(r => [
      new Date(r.timestamp).toLocaleString(),
      r.percentage,
      r.voltage || 'N/A',
      r.temperature || 'N/A',
      r.is_charging ? 'Yes' : 'No',
      r.power_source || 'Unknown'
    ]);

    const csv = [headers, ...rows].map(row => row.join(',')).join('\n');
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `battery-readings-${session.id}.csv`;
    a.click();
  };

  const getChangeIcon = (current, previous) => {
    if (!previous) return null;
    if (current.percentage > previous.percentage) {
      return <TrendingUp className="inline w-3 h-3 text-green-500 ml-1" />;
    } else if (current.percentage < previous.percentage) {
      return <TrendingDown className="inline w-3 h-3 text-red-500 ml-1" />;
    }
    return null;
  };

  const getPercentageChange = (current, previous) => {
    if (!previous) return '';
    const change = current.percentage - previous.percentage;
    if (change === 0) return '';
    return `(${change > 0 ? '+' : ''}${change}%)`;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-[9999] p-4" style={{ marginTop: 0 }}>
      <div className="bg-white rounded-lg w-full max-w-4xl flex flex-col shadow-2xl relative z-[10000]" style={{ maxHeight: 'calc(100vh - 120px)', marginTop: '60px' }}>
        <div className="p-4 border-b flex justify-between items-center">
          <div>
            <h2 className="text-xl font-semibold">Battery Test Readings</h2>
            <p className="text-sm text-gray-600 mt-1">
              Session: {new Date(session.start_time).toLocaleString()} • {readings.length} readings
            </p>
          </div>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X size={24} />
          </button>
        </div>

        <div className="p-4 border-b flex items-center justify-between">
          <div className="flex space-x-2">
            <button
              onClick={() => setFilter('all')}
              className={`px-3 py-1 rounded text-sm ${
                filter === 'all' 
                  ? 'bg-blue-100 text-blue-700 border border-blue-300' 
                  : 'bg-gray-100 text-gray-700 border border-gray-300'
              }`}
            >
              All ({readings.length})
            </button>
            <button
              onClick={() => setFilter('discharge')}
              className={`px-3 py-1 rounded text-sm ${
                filter === 'discharge' 
                  ? 'bg-orange-100 text-orange-700 border border-orange-300' 
                  : 'bg-gray-100 text-gray-700 border border-gray-300'
              }`}
            >
              <Battery className="inline w-4 h-4 mr-1" />
              Discharge ({readings.filter(r => !r.is_charging).length})
            </button>
            <button
              onClick={() => setFilter('charge')}
              className={`px-3 py-1 rounded text-sm ${
                filter === 'charge' 
                  ? 'bg-green-100 text-green-700 border border-green-300' 
                  : 'bg-gray-100 text-gray-700 border border-gray-300'
              }`}
            >
              <Zap className="inline w-4 h-4 mr-1" />
              Charge ({readings.filter(r => r.is_charging).length})
            </button>
          </div>
          <button
            onClick={exportCSV}
            className="flex items-center px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded text-sm"
          >
            <Download className="w-4 h-4 mr-1" />
            Export CSV
          </button>
        </div>

        <div className="flex-1 overflow-auto">
          {loading ? (
            <div className="p-8 text-center text-gray-500">Loading readings...</div>
          ) : filteredReadings.length === 0 ? (
            <div className="p-8 text-center text-gray-500">No readings found</div>
          ) : (
            <table className="w-full">
              <thead className="bg-gray-50 sticky top-0 z-10">
                <tr>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Time</th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Battery %</th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Voltage</th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Temp</th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Power</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {filteredReadings.map((reading, index) => {
                  const previous = index > 0 ? filteredReadings[index - 1] : null;
                  return (
                    <tr key={reading.id} className="hover:bg-gray-50">
                      <td className="px-4 py-2 text-sm">
                        <div className="flex items-center">
                          <Clock className="w-4 h-4 text-gray-400 mr-2" />
                          {new Date(reading.timestamp).toLocaleTimeString()}
                        </div>
                      </td>
                      <td className="px-4 py-2 text-sm font-medium">
                        {reading.percentage}%
                        {getChangeIcon(reading, previous)}
                        <span className="text-xs text-gray-500 ml-1">
                          {getPercentageChange(reading, previous)}
                        </span>
                      </td>
                      <td className="px-4 py-2 text-sm">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs ${
                          reading.is_charging 
                            ? 'bg-green-100 text-green-700' 
                            : 'bg-orange-100 text-orange-700'
                        }`}>
                          {reading.is_charging ? (
                            <>
                              <Zap className="w-3 h-3 mr-1" />
                              Charging
                            </>
                          ) : (
                            <>
                              <Battery className="w-3 h-3 mr-1" />
                              Discharging
                            </>
                          )}
                        </span>
                      </td>
                      <td className="px-4 py-2 text-sm text-gray-600">
                        {reading.voltage ? `${reading.voltage}V` : 'N/A'}
                      </td>
                      <td className="px-4 py-2 text-sm text-gray-600">
                        {reading.temperature ? `${reading.temperature}°C` : 'N/A'}
                      </td>
                      <td className="px-4 py-2 text-sm text-gray-600">
                        {reading.power_source || 'Unknown'}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          )}
        </div>

        {!loading && filteredReadings.length > 0 && (
          <div className="p-4 border-t bg-gray-50 text-sm text-gray-600">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <span className="font-medium">First Reading:</span> {filteredReadings[0].percentage}%
              </div>
              <div>
                <span className="font-medium">Last Reading:</span> {filteredReadings[filteredReadings.length - 1].percentage}%
              </div>
              <div>
                <span className="font-medium">Total Change:</span> {
                  filteredReadings[filteredReadings.length - 1].percentage - filteredReadings[0].percentage
                }%
              </div>
              <div>
                <span className="font-medium">Duration:</span> {
                  Math.round((new Date(filteredReadings[filteredReadings.length - 1].timestamp) - 
                    new Date(filteredReadings[0].timestamp)) / 60000)
                } min
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default BatteryReadingsModal;