import React, { useState } from 'react';
import { Download, FileText, Package, Calendar, HardDrive } from 'lucide-react';

const AgentDownloads = () => {
  const [showNewVersionAlert, setShowNewVersionAlert] = useState(true);
  
  // Mock data - this would come from database
  const versions = {
    current: {
      version: '1.2.0',
      releaseDate: '2024-12-15',
      fileSize: '65MB',
      downloadUrl: '/downloads/agent-v1.2.0.exe',
      notes: [
        'Added battery test UI with real-time progress',
        'Integrated FOG deployment detection',
        'Improved automatic bay assignment',
        'Fixed WebSocket connection issues',
        'Added tab-based interface'
      ]
    },
    previous: {
      version: '1.1.0',
      releaseDate: '2024-12-10',
      fileSize: '64MB',
      downloadUrl: '/downloads/agent-v1.1.0.exe',
      notes: [
        'Initial battery testing support',
        'Basic UI implementation',
        'Network validation'
      ]
    }
  };
  
  const handleDownload = (version) => {
    // In production, this would download from actual URL
    console.log(`Downloading version ${version.version}`);
    // window.location.href = version.downloadUrl;
  };
  
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Agent Downloads</h1>
              <p className="text-sm text-gray-600 mt-1">
                Download and distribute the QC Agent to testing workstations
              </p>
            </div>
            <Package size={32} className="text-gray-400" />
          </div>
        </div>
      </header>
      
      {/* New Version Alert */}
      {showNewVersionAlert && (
        <div className="bg-blue-50 border-b border-blue-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Download className="h-5 w-5 text-blue-400" />
                </div>
                <div className="ml-3">
                  <p className="text-sm text-blue-800">
                    <span className="font-medium">New version available!</span>
                    {' '}Version {versions.current.version} includes battery test UI and improved stability.
                  </p>
                </div>
              </div>
              <button
                onClick={() => setShowNewVersionAlert(false)}
                className="text-blue-500 hover:text-blue-600 text-sm"
              >
                Dismiss
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid gap-6 md:grid-cols-2">
          {/* Current Version Card */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 px-6 py-4">
              <h2 className="text-xl font-semibold text-white">Current Version</h2>
              <p className="text-blue-100 text-sm">Recommended for all new installations</p>
            </div>
            
            <div className="p-6 space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-3xl font-bold text-gray-900">v{versions.current.version}</p>
                  <div className="flex items-center space-x-4 mt-2 text-sm text-gray-600">
                    <span className="flex items-center">
                      <Calendar size={14} className="mr-1" />
                      {versions.current.releaseDate}
                    </span>
                    <span className="flex items-center">
                      <HardDrive size={14} className="mr-1" />
                      {versions.current.fileSize}
                    </span>
                  </div>
                </div>
                <button
                  onClick={() => handleDownload(versions.current)}
                  className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  <Download size={16} className="mr-2" />
                  Download
                </button>
              </div>
              
              {/* Release Notes */}
              <div className="border-t pt-4">
                <h3 className="font-medium text-gray-900 mb-2 flex items-center">
                  <FileText size={16} className="mr-2" />
                  What's New
                </h3>
                <ul className="space-y-1 text-sm text-gray-600">
                  {versions.current.notes.map((note, index) => (
                    <li key={index} className="flex items-start">
                      <span className="text-green-500 mr-2">✓</span>
                      {note}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
          
          {/* Previous Version Card */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div className="bg-gray-100 px-6 py-4">
              <h2 className="text-xl font-semibold text-gray-700">Previous Version</h2>
              <p className="text-gray-600 text-sm">Fallback option if issues occur</p>
            </div>
            
            <div className="p-6 space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-3xl font-bold text-gray-900">v{versions.previous.version}</p>
                  <div className="flex items-center space-x-4 mt-2 text-sm text-gray-600">
                    <span className="flex items-center">
                      <Calendar size={14} className="mr-1" />
                      {versions.previous.releaseDate}
                    </span>
                    <span className="flex items-center">
                      <HardDrive size={14} className="mr-1" />
                      {versions.previous.fileSize}
                    </span>
                  </div>
                </div>
                <button
                  onClick={() => handleDownload(versions.previous)}
                  className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
                >
                  <Download size={16} className="mr-2" />
                  Download
                </button>
              </div>
              
              {/* Release Notes */}
              <div className="border-t pt-4">
                <h3 className="font-medium text-gray-900 mb-2 flex items-center">
                  <FileText size={16} className="mr-2" />
                  Release Notes
                </h3>
                <ul className="space-y-1 text-sm text-gray-600">
                  {versions.previous.notes.map((note, index) => (
                    <li key={index} className="flex items-start">
                      <span className="text-gray-400 mr-2">•</span>
                      {note}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
        
        {/* Instructions Section */}
        <div className="mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Deployment Instructions</h3>
          <div className="prose text-gray-600">
            <ol className="space-y-2">
              <li>Download the latest agent version from above</li>
              <li>Copy the EXE file to a network share accessible by all workstations</li>
              <li>Run the agent on each laptop after Windows installation</li>
              <li>The agent will automatically connect to the dashboard when launched</li>
              <li>Bay assignment happens automatically via power pattern detection</li>
            </ol>
            
            <div className="mt-6 p-4 bg-amber-50 border border-amber-200 rounded-md">
              <p className="text-sm text-amber-800">
                <strong>FOG Integration:</strong> For automated deployment, place the agent EXE in your FOG post-installation scripts. 
                The agent will detect FOG environment and configure itself automatically.
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default AgentDownloads;