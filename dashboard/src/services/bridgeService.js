// Bridge service communication for relay control
const BRIDGE_SERVICE_URL = process.env.REACT_APP_BRIDGE_URL || 'http://localhost:5200';

export const bridgeService = {
  // Check if bridge service is available
  async checkConnection() {
    try {
      const response = await fetch(`${BRIDGE_SERVICE_URL}/health`);
      return response.ok;
    } catch (error) {
      console.error('Bridge service not available:', error);
      return false;
    }
  },

  // Execute power pattern for bay assignment
  async executeBayAssignmentPattern(bayNumber) {
    try {
      const response = await fetch(`${BRIDGE_SERVICE_URL}/api/power/pattern`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          bay: bayNumber,
          pattern: 'assignment', // Bridge service knows the assignment pattern
        }),
      });

      if (!response.ok) {
        throw new Error(`Bridge service error: ${response.statusText}`);
      }

      const result = await response.json();
      return { success: true, data: result };
    } catch (error) {
      console.error('Failed to execute power pattern:', error);
      return { success: false, error: error.message };
    }
  },

  // Control individual bay power
  async controlBayPower(bayNumber, action) {
    try {
      const response = await fetch(`${BRIDGE_SERVICE_URL}/api/power/control`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          bay: bayNumber,
          action: action, // 'on' or 'off'
        }),
      });

      if (!response.ok) {
        throw new Error(`Bridge service error: ${response.statusText}`);
      }

      const result = await response.json();
      return { success: true, data: result };
    } catch (error) {
      console.error('Failed to control bay power:', error);
      return { success: false, error: error.message };
    }
  },

  // Get relay status
  async getRelayStatus() {
    try {
      const response = await fetch(`${BRIDGE_SERVICE_URL}/api/relay/status`);
      
      if (!response.ok) {
        throw new Error(`Bridge service error: ${response.statusText}`);
      }

      const status = await response.json();
      return { success: true, data: status };
    } catch (error) {
      console.error('Failed to get relay status:', error);
      return { success: false, error: error.message };
    }
  },
};