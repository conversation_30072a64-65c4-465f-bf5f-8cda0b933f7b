{"name": "laptop-qc-system", "version": "1.0.0", "description": "Comprehensive QC System for Refurbished Laptops", "private": true, "scripts": {"dev": "npm run dev:dashboard", "dev:all": "concurrently -n \"agent,dashboard,bridge\" -c \"blue,green,yellow\" \"npm run dev:agent\" \"npm run dev:dashboard\" \"npm run dev:bridge\"", "dev:agent": "cd agent && npm run dev", "dev:dashboard": "cd dashboard && npm run dev", "dev:bridge": "cd bridge && npm start", "build": "npm run build:dashboard", "build:dashboard": "cd dashboard && npm run build", "build:agent": "cd agent && npm run build", "install:all": "npm install && cd agent && npm install && cd ../dashboard && npm install && cd ../bridge && npm install && cd ..", "vercel-build": "cd dashboard && npm install && npm run build", "test": "echo \"No tests configured yet\"", "clean": "rm -rf node_modules agent/node_modules dashboard/node_modules bridge/node_modules"}, "dependencies": {"@supabase/supabase-js": "^2.50.0", "dotenv": "^16.5.0"}, "devDependencies": {"concurrently": "^8.2.2"}, "repository": {"type": "git", "url": "git+https://github.com/bpipl/qc.git"}, "keywords": ["laptop", "qc", "testing", "refurbishment"], "author": "BPIPL", "license": "UNLICENSED"}