# CLAUDE.md - Living Project Document

**Last Updated**: 19 June 2025

## 📌 IMPORTANT: Read this file EVERY time before working on the project

This file tracks current progress and must be updated after each task completion.
Keep this file under 200 lines.

## 🎯 Project: Comprehensive QC System for Refurbished Laptops

### Core Technologies
- **Agent v1.8**: Electron with battery testing (90% working)
- **Agent v2.0**: ✅ Core framework implemented (modular architecture)
- **Frontend**: React + Vite + Tailwind CSS + React Router
- **Database**: Supabase (PostgreSQL + Realtime)
- **Deployment**: Vercel (Frontend), FOG/PXE + USB (Agent)
- **Power Control**: 30-channel Ethernet Relay Module (Modbus)
- **Bridge Service**: Enhanced with dashboard integration
- **MCP**: ✅ Integrated for database access

### User Preferences & Guidelines
- 👤 **Non-technical users** - Visual guidance, minimal text
- 📝 **Documentation focused** - Keep updated, avoid bloat
- 🏗️ **Modular architecture** - Reusable test modules
- ❓ **Ask questions** - Don't assume, clarify when uncertain
- 🚀 **Offline-first** - Local storage with background sync

### Key Principles
- ✅ Keep everything SIMPLE
- ✅ Agent-first approach for testing
- ✅ Reusable hardware test modules
- ✅ Smart real-time (only where needed)
- ❌ No over-engineering

## 🔄 Current Status (Updated June 19, Session 4)

### ✅ Working Features
- **Battery Testing**: Manual power control with timer (v1.8)
- **Dashboard**: Real-time monitoring via WebSocket  
- **Battery Health**: WMI data collection (design/full capacity)
- **Data Collection**: Model, serial, MAC, battery info
- **Test Sync**: Dashboard-agent communication working
- **Bay Assignment**: ✅ Complete UI modal and coordination
- **Agent v2.0 Core**: Modular framework, offline storage, sync engine
- **PIN Authentication**: 4-6 digit PIN with session management
- **Visual Interfaces**: ✅ Keyboard, USB, Physical inspection (Session 2)
- **Smart Tests**: ✅ Model-aware adaptation implemented (Session 2)
- **Fuzzy Matching**: ✅ 80% threshold for model variations (Session 2)
- **SystemInfoDetection**: ✅ Wired as entry point (Session 3)
- **Manual Model Selection**: ✅ UI implemented (Session 3)
- **DisplayUI Events**: ✅ Full event chain working (Session 3)
- **Flexible Test UI**: ✅ Run tests in any order (Session 4)
- **macOS Debugging**: ✅ Preview UIs without hardware (Session 4)
- **Manual Test Controls**: ✅ Pass/fail/skip buttons (Session 4)

### 🚧 Code Written, Hardware Testing Needed
- **Modbus Integration**: `modbus-relay.js` complete but never tested
- **Bridge Service**: Basic implementation exists
- **Power Control**: Mock relay working, real hardware pending
- **Smart Test Modules**: Visual UIs and logic complete, needs validation

### 📋 Next Priority Tasks (Post Session 3)
1. **Hardware Validation**: Test visual interfaces on real devices
2. **Battery Test Implementation**: Port from v1.8 using new documentation
3. **Stage-Based Testing**: Implement IQC/Mid/Final differences
4. **Windows PE Build**: Create image with drivers from actual machines
5. **Model Profile Database**: Create profiles for top 10 models
6. **Audio Test Visuals**: Add waveform visualization

### ❌ Deferred Tasks
- **FOG Server**: Hardware setup pending
- **Modbus Testing**: Physical relay required
- **Auto-update System**: Phase 3 priority

## 📊 Real Implementation Status

### Phase 1: Battery Testing (95% Complete)
- ✅ Manual battery testing working
- ✅ Dashboard sync working
- ✅ Battery health collection working
- ✅ Bay assignment UI complete
- 🚧 Modbus code written, hardware pending
- ❌ FOG server deployment pending

### Phase 2: Agent v2.0 & Hardware Tests (95% Complete)
- ✅ Core framework implemented
- ✅ Offline storage with SQLite
- ✅ Background sync engine
- ✅ PIN authentication system
- ✅ Visual test interfaces (Keyboard, USB, Physical)
- ✅ Smart model-aware tests
- ✅ Fuzzy model matching
- ✅ SystemInfo integration as entry point
- ✅ Manual model selection UI
- ✅ Flexible test selection UI
- ✅ macOS debugging support
- 📋 Battery implementation pending

### Phase 3-6: Future Phases
- Not started

## 🆕 June 19 Sessions Summary

### Session 1 (Analysis):
1. ✅ Fixed better-sqlite3 for Electron
2. ✅ Created initial test modules (System Info, Physical, Audio)
3. 🔍 Discovered tests were "informed but not smart"
4. 🔍 Identified missing visual interfaces
5. 📝 Clarified driver strategy and scale requirements

### Session 2 (Implementation):
1. ✅ **Visual Interfaces**: Added ASCII-art keyboard, USB diagram, part visuals
2. ✅ **Smart Tests**: Tests now skip irrelevant options based on model
3. ✅ **Expanded Results**: Added driver-issue, not-tested, skipped, bios-disabled
4. ✅ **Fuzzy Matching**: ModelMatcher handles "HP 840 G5" vs "840G5"
5. ✅ **Driver Awareness**: Tests detect live boot and adapt accordingly

### Session 3 (Integration):
1. ✅ **Module ID Fix**: Fixed workflow to use correct module IDs
2. ✅ **SystemInfo Integration**: Wired as entry point, passes model to all tests
3. ✅ **Manual Model Selection**: Full UI with fuzzy match display
4. ✅ **DisplayUI Events**: Completed event chain for visual interfaces
5. ✅ **Battery Documentation**: Created comprehensive porting guide

### Session 4 (Debugging & Flexible UI):
1. ✅ **Fixed Module Loading**: Resolved syntax errors and missing IDs
2. ✅ **Flexible Test Selection**: Operators can run tests in any order
3. ✅ **macOS Debugging Mode**: Preview all UIs without Windows hardware
4. ✅ **Manual Test Controls**: Pass/fail/skip for debugging
5. ✅ **Visual Status Indicators**: Clear progress tracking

## 🗄️ Database Schema
- **Schema**: Consolidated in `supabase_schema_complete.sql`
- **Bridge Service**: Enhanced in `dashboard/src/services/bridgeService.js`
- **Bay Assignment**: See `dashboard/src/components/BayAssignmentModal/`

## 🐛 Remaining Issues
- **Hardware Validation**: Visual interfaces untested on real Windows devices
- **Battery Test Port**: Implementation pending (documentation ready)
- **Audio Visual**: Needs waveform/level visualization
- **Model Profiles**: Database integration needed for profiles
- **Stage Awareness**: IQC/Mid/Final parameter exists but unused

## 📋 Pending Hardware Setup
1. **FOG Server**: Network configuration needed
2. **Modbus Relay**: Physical connection required
3. **Bridge Service**: Production deployment pending

## 🎮 Commands & Scripts

```bash
# Agent v1.8
cd agent
npm run dev      # Development
npm run dist     # Build portable EXE

# Agent v2.0
cd agent-v2
npm run dev      # Development
npm run dist     # Build portable EXE

# Frontend
cd dashboard
npm run dev      # Development (http://localhost:5100)
npm run build    # Production build

# Bridge Service
cd bridge
npm start        # Start service (http://localhost:5200)
```

## 📝 Key Implementation Notes

### What Actually Works
1. Battery testing with manual power control
2. Real-time dashboard updates
3. Battery health data collection
4. Model and hardware detection
5. WebSocket communication

### Agent v2.0 Structure
```
agent-v2/
├── src/
│   ├── core/
│   │   ├── framework/     # TestModule, ModuleManager
│   │   ├── storage/       # OfflineStorage (SQLite)
│   │   ├── sync/          # SyncEngine
│   │   └── auth/          # PINAuth
│   ├── modules/
│   │   ├── hardware/      # KeyboardTest (done), others pending
│   │   ├── battery/       # Port from v1.8
│   │   └── inspection/    # Physical checks
│   └── main.js           # Entry point
```

## 🔌 Database Connection
- **Project URL**: https://primrdkulctsjvugdiyu.supabase.co
- **Schema**: See `supabase_schema_complete.sql` for current state

## 📈 Scale Targets
- Current: 50 laptops/day
- Target: 100 laptops/day
- Monthly: 3000 units

## ⚡ Quick Checklist
1. Battery testing: ✅ Working (v1.8)
2. Visual interfaces: ✅ Implemented & debugged
3. Smart tests: ✅ Model-aware logic
4. Flexible test UI: ✅ Working on macOS
5. Module loading: ✅ Fixed and verified
6. Modbus control: 📝 Code only
7. FOG deployment: ❌ Not started
8. Windows hardware validation: 📋 Needed

---
**Remember**: Test on macOS first, then validate on Windows hardware!