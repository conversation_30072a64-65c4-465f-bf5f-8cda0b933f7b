{"permissions": {"allow": ["<PERSON><PERSON>(mv:*)", "mcp__supabase__get_project", "Bash(npm run dev:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(chmod:*)", "Bash(git add:*)", "mcp__supabase__list_tables", "Bash(grep:*)", "mcp__supabase__execute_sql", "mcp__supabase__apply_migration", "Bash(git add:*)", "Bash(find:*)", "<PERSON><PERSON>(diff:*)", "Bash(for file in AudioTest.js DisplayTest.js KeyboardTest.js SystemInfoDetection.js USBTest.js)", "Bash(do echo \"=== $file ===\")", "Bash(done)", "Bash(ls:*)"], "deny": []}}