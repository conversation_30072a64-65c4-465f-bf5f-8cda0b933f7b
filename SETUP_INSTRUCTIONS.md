# Setup Instructions

## 1. Database Setup

Since you have your Supabase credentials, you need to create the database tables:

1. Go to: https://supabase.com/dashboard/project/primrdkulctsjvugdiyu/sql/new
2. Copy and paste the contents of `supabase_schema.sql`
3. Click "Run"

## 2. Test Agent Locally

### For Mac (Development)
```bash
cd agent
npm run dev
```

### For Windows (Testing)
```bash
cd agent
npm run build
# Copy dist/Battery Test Agent.exe to Windows machine
```

## 3. Network Testing

For testing on your Mac, temporarily change the network validation:

Edit `agent/.env`:
```
NETWORK_VALIDATION=192.168  # This will work with most networks
```

## 4. Build Dashboard

```bash
cd dashboard
npm install
npm run dev
```

## Quick Test

1. Run the agent: `cd agent && npm run dev`
2. Click "Connect" button
3. Check Supabase dashboard: https://supabase.com/dashboard/project/primrdkulctsjvugdiyu/editor/laptops
4. You should see a new laptop entry!

## Troubleshooting

- If network validation fails, check your IP and update NETWORK_VALIDATION
- If connection fails, verify your Supabase keys
- Check console for detailed error messages