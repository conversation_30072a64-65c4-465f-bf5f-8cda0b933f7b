-- Comprehensive QC System - Complete Supabase Schema
-- This file contains ALL columns currently in production
-- Last Updated: June 18, 2025

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- COMPLETE LAPTOPS TABLE (with ALL current columns)
-- =====================================================

CREATE TABLE laptops (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  short_id VARCHAR(10) UNIQUE NOT NULL,
  serial_number VARCHAR(100),
  mac_address VARCHAR(17),
  model VARCHAR(100),
  bay_number INTEGER CHECK (bay_number >= 1 AND bay_number <= 30),
  bay_assignment_pending INTEGER,
  fog_id VARCHAR(50),
  status VARCHAR(20) DEFAULT 'idle',
  last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Additional columns from production
  ip_address VARCHAR(45),
  battery_percentage INTEGER,
  is_charging BOOLEAN,
  
  -- Real-time test tracking columns
  test_phase TEXT,
  test_progress NUMERIC(5,2),
  test_time_remaining BIGINT,
  test_waiting_for_power BOOLEAN DEFAULT false,
  
  -- Battery health columns
  battery_health_percent NUMERIC(5,2),
  design_capacity INTEGER,
  full_charge_capacity INTEGER,
  battery_status VARCHAR(20),
  cycle_count INTEGER,
  battery_data_updated_at TIMESTAMP WITH TIME ZONE
);

-- =====================================================
-- TEST SESSIONS TABLE (with health data)
-- =====================================================

CREATE TABLE test_sessions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  laptop_id UUID REFERENCES laptops(id) ON DELETE CASCADE,
  test_type VARCHAR(50) NOT NULL,
  start_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  end_time TIMESTAMP WITH TIME ZONE,
  result VARCHAR(20),
  notes TEXT,
  operator_id VARCHAR(100),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Battery health at test start
  laptop_battery_health NUMERIC(5,2),
  laptop_design_capacity INTEGER,
  laptop_full_charge_capacity INTEGER,
  laptop_cycle_count INTEGER
);

-- =====================================================
-- REMAINING TABLES (unchanged from original schema)
-- =====================================================

-- Battery readings
CREATE TABLE battery_readings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  session_id UUID REFERENCES test_sessions(id) ON DELETE CASCADE,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  percentage INTEGER CHECK (percentage >= 0 AND percentage <= 100),
  voltage DECIMAL(5,2),
  temperature DECIMAL(5,2),
  current_ma INTEGER,
  is_charging BOOLEAN,
  power_source VARCHAR(20),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Power control logs
CREATE TABLE power_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  bay_number INTEGER NOT NULL CHECK (bay_number >= 1 AND bay_number <= 30),
  action VARCHAR(10) NOT NULL,
  triggered_by VARCHAR(50),
  laptop_id UUID REFERENCES laptops(id),
  success BOOLEAN DEFAULT true,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Hardware tests (Phase 2)
CREATE TABLE hardware_tests (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  session_id UUID REFERENCES test_sessions(id) ON DELETE CASCADE,
  component VARCHAR(50) NOT NULL,
  status VARCHAR(20) NOT NULL,
  notes TEXT,
  test_data JSONB,
  tested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Test profiles
CREATE TABLE test_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  model_name VARCHAR(100) UNIQUE NOT NULL,
  battery_test_config JSONB,
  hardware_test_config JSONB,
  active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- MIGRATION TRACKING (New)
-- =====================================================

CREATE TABLE schema_migrations (
  version VARCHAR(50) PRIMARY KEY,
  applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  description TEXT
);

-- Record this schema version
INSERT INTO schema_migrations (version, description) VALUES 
('v2.0_complete', 'Complete schema with all production columns');

-- =====================================================
-- INDEXES
-- =====================================================

CREATE INDEX idx_laptops_short_id ON laptops(short_id);
CREATE INDEX idx_laptops_status ON laptops(status);
CREATE INDEX idx_laptops_bay_number ON laptops(bay_number);
CREATE INDEX idx_laptops_bay_assignment_pending ON laptops(bay_assignment_pending) WHERE bay_assignment_pending IS NOT NULL;
CREATE INDEX idx_test_sessions_laptop_id ON test_sessions(laptop_id);
CREATE INDEX idx_battery_readings_session_id ON battery_readings(session_id);

-- =====================================================
-- FUNCTIONS & TRIGGERS
-- =====================================================

-- Auto-update updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_laptops_updated_at BEFORE UPDATE
  ON laptops FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Clear bay assignment pending after successful assignment
CREATE OR REPLACE FUNCTION clear_bay_assignment_pending()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.bay_number IS NOT NULL AND NEW.bay_number = NEW.bay_assignment_pending THEN
    NEW.bay_assignment_pending = NULL;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER clear_bay_pending_trigger
BEFORE UPDATE ON laptops
FOR EACH ROW
EXECUTE FUNCTION clear_bay_assignment_pending();

-- =====================================================
-- ROW LEVEL SECURITY
-- =====================================================

ALTER TABLE laptops ENABLE ROW LEVEL SECURITY;
ALTER TABLE test_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE battery_readings ENABLE ROW LEVEL SECURITY;

-- Service role policies
CREATE POLICY "Service role full access" ON laptops FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Service role full access" ON test_sessions FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Service role full access" ON battery_readings FOR ALL USING (auth.role() = 'service_role');

-- =====================================================
-- REALTIME SUBSCRIPTIONS
-- =====================================================

ALTER PUBLICATION supabase_realtime ADD TABLE laptops;
ALTER PUBLICATION supabase_realtime ADD TABLE test_sessions;