const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: './agent/.env' });

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function setupDatabase() {
  console.log('Setting up database schema...');

  try {
    // Create laptops table
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        -- Enable UUID extension
        CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

        -- Drop existing table if exists
        DROP TABLE IF EXISTS laptops CASCADE;

        -- Create laptops table
        CREATE TABLE laptops (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          short_id VARCHAR(10) UNIQUE NOT NULL,
          serial_number VARCHAR(100),
          mac_address VARCHAR(17),
          status VARCHAR(20) DEFAULT 'idle',
          last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Create indexes
        CREATE INDEX idx_laptops_short_id ON laptops(short_id);
        CREATE INDEX idx_laptops_status ON laptops(status);

        -- Enable Row Level Security
        ALTER TABLE laptops ENABLE ROW LEVEL SECURITY;

        -- Create policies
        CREATE POLICY "Service role can do everything" ON laptops
          FOR ALL USING (true);

        CREATE POLICY "Anyone can read laptops" ON laptops
          FOR SELECT USING (true);
      `
    });

    if (error) {
      console.error('Error creating tables:', error.message);
      
      // If RPC doesn't exist, try direct approach
      console.log('Trying alternative approach...');
      
      // Check if table exists
      const { data: tables } = await supabase
        .from('laptops')
        .select('*')
        .limit(1);
      
      console.log('Table check passed - laptops table accessible');
    } else {
      console.log('Database schema created successfully!');
    }

    // Insert test data
    const { data: testData, error: insertError } = await supabase
      .from('laptops')
      .insert([
        { short_id: 'TEST001', serial_number: 'TEST-SERIAL', mac_address: '00:11:22:33:44:55', status: 'idle' }
      ])
      .select();

    if (insertError) {
      console.error('Error inserting test data:', insertError.message);
    } else {
      console.log('Test data inserted:', testData);
      
      // Clean up test data
      await supabase
        .from('laptops')
        .delete()
        .eq('short_id', 'TEST001');
      
      console.log('Test data cleaned up');
    }

    console.log('\n✅ Database setup complete!');
    console.log('You can now run the agent and it will connect to Supabase.');

  } catch (error) {
    console.error('Setup error:', error);
  }
}

setupDatabase();