# Laptop QC System - Testing Guide

## 🚀 Quick Start Testing

### Prerequisites
1. Node.js installed (v16 or higher)
2. Supabase project credentials
3. Network configured (192.168.50.x for production simulation)

### Step 1: Set Up Environment Files

Create `.env` files in each component directory:

**agent/.env**
```
SUPABASE_URL=https://primrdkulctsjvugdiyu.supabase.co
SUPABASE_SERVICE_KEY=your_service_key_here
NETWORK_VALIDATION=192.168.50
```

**dashboard/.env**
```
VITE_SUPABASE_URL=https://primrdkulctsjvugdiyu.supabase.co
VITE_SUPABASE_ANON_KEY=your_anon_key_here
```

**bridge/.env**
```
SUPABASE_URL=https://primrdkulctsjvugdiyu.supabase.co
SUPABASE_SERVICE_KEY=your_service_key_here
PORT=8080
```

### Step 2: Apply Database Schema

1. Go to your Supabase dashboard
2. Navigate to SQL Editor
3. Run the contents of `supabase_schema.sql`
4. Then run `supabase_schema_update.sql`

### Step 3: Install Dependencies

⚠️ **IMPORTANT**: Always run `npm install` before starting any service!

```bash
# Agent
cd agent
npm install

# Dashboard
cd ../dashboard
npm install

# Bridge Service
cd ../bridge
npm install
```

### Step 4: Start All Services

Open 3 terminal windows:

**Terminal 1 - Bridge Service (Mock)**
```bash
cd bridge
npm start
# Visit http://localhost:5200 to see mock relay control
```

**Terminal 2 - Dashboard**
```bash
cd dashboard
npm run dev
# Visit http://localhost:5100
```

**Terminal 3 - Agent**
```bash
cd agent
npm run dev
```

## 🧪 Testing Automatic Bay Assignment

### Scenario 1: New Laptop Connection

1. **Start the agent** - Click "Connect" button
2. **Check dashboard** - You'll see an unassigned laptop appear
3. **Assign bay** - Click "Assign Bay" on the laptop, then click an empty bay
4. **Watch power pattern** - Dashboard triggers power cycling
5. **Verify assignment** - Agent detects pattern and confirms bay

### Scenario 2: Multiple Laptops

1. Start multiple agent instances (different terminals)
2. Each will get a unique ID (A001, A002, etc.)
3. Assign each to different bays
4. Monitor power states in bridge UI

### Scenario 3: Power Control Testing

1. Use the dashboard bay grid to toggle power
2. Watch the bridge service UI update in real-time
3. Check power_logs table in Supabase

## 🔍 Debugging Tips

### Agent Not Connecting?
- Check network validation in .env
- Verify Supabase credentials
- Look for errors in console

### Bay Assignment Not Working?
- Check bridge service is running
- Verify Supabase realtime is enabled
- Look for bay_assignment_pending in laptops table

### Dashboard Not Updating?
- Check browser console for errors
- Verify Supabase anon key
- Ensure realtime is enabled for tables

## 📊 Database Verification

Run these queries in Supabase SQL editor:

```sql
-- Check connected laptops
SELECT * FROM laptops ORDER BY created_at DESC;

-- Check power logs
SELECT * FROM power_logs ORDER BY created_at DESC LIMIT 20;

-- Check bay assignments
SELECT short_id, bay_number, status, last_seen 
FROM laptops 
WHERE bay_number IS NOT NULL 
ORDER BY bay_number;
```

## 🏭 Production Simulation

To simulate production environment:

1. **Network**: Configure your machine to use 192.168.50.x IP
2. **Multiple Agents**: Run agent on different machines/VMs
3. **Relay**: Replace mock with actual Modbus TCP calls
4. **Scale**: Test with 10+ simultaneous connections

## 📝 Test Checklist

- [ ] Agent connects successfully
- [ ] Laptop ID generated (A001, A002, etc.)
- [ ] Dashboard shows connected laptops
- [ ] Unassigned laptops highlighted
- [ ] Bay assignment workflow works
- [ ] Power control toggles work
- [ ] Bridge service responds to commands
- [ ] Real-time updates work
- [ ] Multiple agents can connect
- [ ] Database stores all events

## 🚨 Common Issues

### "Invalid network" error
- The agent checks for 192.168.50.x network
- For testing, change NETWORK_VALIDATION in agent/.env
- Or configure your network adapter

### Supabase connection errors
- Verify your project URL and keys
- Check if RLS policies are applied
- Ensure service_role key is used for agent/bridge

### Real-time not working
- Check Supabase dashboard > Database > Replication
- Ensure tables are added to publication
- Restart services if needed

## 🎯 Next Steps

After successful testing:

1. **Build Agent EXE**: `cd agent && npm run build`
2. **Deploy Dashboard**: Push to GitHub and connect Vercel
3. **Setup FOG Server**: For PXE boot integration
4. **Connect Real Relay**: Replace mock with actual hardware
5. **Train Operators**: Simple workflow documentation