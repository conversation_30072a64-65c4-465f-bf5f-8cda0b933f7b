<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Laptop QC Agent v2.0</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f5f5f5;
      color: #333;
      line-height: 1.6;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    
    /* Header */
    .header {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      margin-bottom: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .logo {
      display: flex;
      align-items: center;
      font-size: 24px;
      font-weight: bold;
    }
    
    .status-bar {
      display: flex;
      gap: 20px;
      align-items: center;
    }
    
    .status-item {
      display: flex;
      align-items: center;
      gap: 5px;
    }
    
    .status-indicator {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background: #ccc;
    }
    
    .status-indicator.online { background: #4caf50; }
    .status-indicator.offline { background: #f44336; }
    .status-indicator.syncing { background: #ff9800; animation: pulse 1s infinite; }
    
    @keyframes pulse {
      0% { opacity: 1; }
      50% { opacity: 0.5; }
      100% { opacity: 1; }
    }
    
    /* Login Screen */
    .login-screen {
      background: white;
      padding: 40px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      max-width: 400px;
      margin: 100px auto;
      text-align: center;
    }
    
    .pin-input {
      display: flex;
      justify-content: center;
      gap: 10px;
      margin: 30px 0;
    }
    
    .pin-digit {
      width: 50px;
      height: 60px;
      font-size: 24px;
      text-align: center;
      border: 2px solid #ddd;
      border-radius: 8px;
      transition: border-color 0.3s;
    }
    
    .pin-digit:focus {
      outline: none;
      border-color: #2196f3;
    }
    
    /* Main Screen */
    .main-screen {
      display: none;
    }
    
    .main-screen.active {
      display: block;
    }
    
    .laptop-info {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      margin-bottom: 20px;
    }
    
    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
      margin-top: 15px;
    }
    
    .info-item {
      display: flex;
      flex-direction: column;
    }
    
    .info-label {
      font-size: 12px;
      color: #666;
      text-transform: uppercase;
    }
    
    .info-value {
      font-size: 16px;
      font-weight: 500;
    }
    
    /* Test Options */
    .test-options {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      margin-bottom: 20px;
    }
    
    .test-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 15px;
      margin-top: 20px;
    }
    
    .test-card {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 8px;
      border: 2px solid transparent;
      cursor: pointer;
      transition: all 0.3s;
    }
    
    .test-card:hover {
      border-color: #2196f3;
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .test-card.running {
      border-color: #ff9800;
      background: #fff3e0;
    }
    
    .test-card h3 {
      margin-bottom: 10px;
      color: #333;
    }
    
    .test-card p {
      font-size: 14px;
      color: #666;
    }
    
    /* Test Status Button Styles */
    .test-status-btn {
      padding: 12px 20px;
      border: 2px solid #e0e0e0;
      border-radius: 8px;
      background: white;
      cursor: pointer;
      transition: all 0.3s;
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      font-weight: 500;
    }
    
    .test-status-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .test-status-btn.not-started {
      border-color: #e0e0e0;
      color: #666;
    }
    
    .test-status-btn.in-progress {
      border-color: #2196f3;
      background: #e3f2fd;
      color: #1976d2;
      animation: pulse 2s infinite;
    }
    
    .test-status-btn.passed {
      border-color: #4caf50;
      background: #e8f5e9;
      color: #388e3c;
    }
    
    .test-status-btn.failed {
      border-color: #f44336;
      background: #ffebee;
      color: #c62828;
    }
    
    .test-status-btn.skipped {
      border-color: #ff9800;
      background: #fff3e0;
      color: #f57c00;
    }
    
    @keyframes pulse {
      0% { opacity: 1; }
      50% { opacity: 0.7; }
      100% { opacity: 1; }
    }
    
    /* Button Styles */
    .btn {
      padding: 10px 20px;
      border: none;
      border-radius: 6px;
      font-size: 16px;
      cursor: pointer;
      transition: all 0.3s;
    }
    
    .btn-primary {
      background: #2196f3;
      color: white;
    }
    
    .btn-primary:hover {
      background: #1976d2;
    }
    
    .btn-success {
      background: #4caf50;
      color: white;
    }
    
    .btn-danger {
      background: #f44336;
      color: white;
    }
    
    .btn-large {
      padding: 15px 30px;
      font-size: 18px;
    }
    
    /* Progress Modal */
    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
      z-index: 1000;
    }
    
    .modal.active {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .modal-content {
      background: white;
      padding: 30px;
      border-radius: 8px;
      max-width: 600px;
      width: 90%;
      max-height: 80vh;
      overflow-y: auto;
    }
    
    .progress-bar {
      width: 100%;
      height: 20px;
      background: #e0e0e0;
      border-radius: 10px;
      overflow: hidden;
      margin: 20px 0;
    }
    
    .progress-fill {
      height: 100%;
      background: #2196f3;
      width: 0%;
      transition: width 0.3s;
    }
    
    /* Model Selection Styles */
    .model-option {
      display: block;
      width: 100%;
      padding: 15px;
      margin: 8px 0;
      background: #f8f9fa;
      border: 2px solid #e0e0e0;
      border-radius: 6px;
      text-align: left;
      cursor: pointer;
      transition: all 0.2s;
    }
    
    .model-option:hover {
      background: #e3f2fd;
      border-color: #2196f3;
    }
    
    .model-option.selected {
      background: #e3f2fd;
      border-color: #1976d2;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .model-option-title {
      font-weight: bold;
      margin-bottom: 5px;
    }
    
    .model-option-confidence {
      font-size: 14px;
      color: #666;
    }
    
    .confidence-high {
      color: #4caf50;
    }
    
    .confidence-medium {
      color: #ff9800;
    }
    
    .confidence-low {
      color: #f44336;
    }
    
    /* Visual Keyboard */
    .keyboard-visual {
      background: #333;
      padding: 20px;
      border-radius: 10px;
      margin: 20px 0;
    }
    
    .keyboard-row {
      display: flex;
      gap: 5px;
      margin-bottom: 5px;
      justify-content: center;
    }
    
    .key {
      background: #555;
      color: white;
      padding: 10px;
      min-width: 40px;
      text-align: center;
      border-radius: 4px;
      font-size: 12px;
      border: 2px solid transparent;
    }
    
    .key.pressed {
      background: #4caf50;
      border-color: #2e7d32;
    }
    
    .key.wide-1-5 { min-width: 60px; }
    .key.wide-2 { min-width: 80px; }
    .key.wide-2-5 { min-width: 100px; }
    
    /* Laptop ID Badge */
    .laptop-badge {
      background: #e3f2fd;
      border: 2px solid #2196f3;
      border-radius: 8px;
      padding: 10px 20px;
      display: inline-block;
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- Header -->
    <div class="header">
      <div class="logo">
        🖥️ Laptop QC Agent v2.0
      </div>
      <div class="status-bar">
        <div class="status-item">
          <span class="status-indicator" id="connection-status"></span>
          <span id="connection-text">Offline</span>
        </div>
        <div class="status-item">
          <span class="status-indicator" id="sync-status"></span>
          <span id="sync-text">Not syncing</span>
        </div>
        <div class="status-item" id="user-info" style="display: none;">
          <span>👤 <span id="user-name"></span></span>
          <span id="session-timer"></span>
        </div>
      </div>
    </div>
    
    <!-- Login Screen -->
    <div class="login-screen" id="login-screen">
      <h2>Enter PIN to Login</h2>
      <p style="margin-top: 10px; color: #666;">Enter your 4-6 digit PIN</p>
      
      <div class="pin-input" id="pin-input">
        <input type="password" class="pin-digit" maxlength="1" pattern="[0-9]">
        <input type="password" class="pin-digit" maxlength="1" pattern="[0-9]">
        <input type="password" class="pin-digit" maxlength="1" pattern="[0-9]">
        <input type="password" class="pin-digit" maxlength="1" pattern="[0-9]">
      </div>
      
      <button class="btn btn-primary btn-large" onclick="handleLogin()">Login</button>
      
      <p style="margin-top: 20px; font-size: 14px; color: #888;">
        Demo PINs: 1234 (Tech), 4321 (Senior), 9999 (Admin)
      </p>
    </div>
    
    <!-- Main Screen -->
    <div class="main-screen" id="main-screen">
      <!-- Laptop Info -->
      <div class="laptop-info">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
          <h2>System Information</h2>
          <div class="laptop-badge">
            <div style="color: #1976d2; font-size: 24px; font-weight: bold; line-height: 1.2;">
              <span id="laptop-uid">-</span>
            </div>
            <div style="color: #555; font-size: 14px; margin-top: 4px; font-weight: 500;">
              <span id="laptop-model-header">-</span>
            </div>
          </div>
        </div>
        <div class="info-grid" id="system-info">
          <div class="info-item">
            <span class="info-label">Model</span>
            <span class="info-value" id="info-model">Loading...</span>
          </div>
          <div class="info-item">
            <span class="info-label">Serial Number</span>
            <span class="info-value" id="info-serial">Loading...</span>
          </div>
          <div class="info-item">
            <span class="info-label">Processor</span>
            <span class="info-value" id="info-cpu">Loading...</span>
          </div>
          <div class="info-item">
            <span class="info-label">Memory</span>
            <span class="info-value" id="info-ram">Loading...</span>
          </div>
        </div>
      </div>
      
      <!-- Test Options -->
      <div class="test-options">
        <h2>Test Control Panel</h2>
        
        <!-- Test Status Bar -->
        <div id="test-status-bar" style="display: flex; gap: 10px; margin: 20px 0; flex-wrap: wrap;">
          <!-- Test buttons will be populated here -->
        </div>
        
        <!-- Current Test Display -->
        <div id="current-test-area" style="margin-top: 20px; padding: 20px; background: #f8f9fa; border-radius: 8px; min-height: 400px; display: none;">
          <h3 id="current-test-title">No Test Running</h3>
          <div id="test-display-content" style="margin-top: 20px;">
            <!-- Test UI will be displayed here -->
          </div>
          
          <!-- Test Controls -->
          <div style="margin-top: 20px; display: flex; gap: 10px; justify-content: center;">
            <button class="btn btn-warning" onclick="skipCurrentTest()">Skip Test</button>
            <button class="btn btn-success" onclick="markTestPass()">Mark as Pass</button>
            <button class="btn btn-danger" onclick="markTestFail()">Mark as Fail</button>
            <button class="btn btn-primary" onclick="nextTest()">Next Test →</button>
          </div>
        </div>
        
        <!-- Legacy test grid (hidden by default) -->
        <div class="test-grid" id="test-grid" style="display: none;">
          <!-- Test modules will be populated here -->
        </div>
      </div>
      
      <!-- Quick Actions -->
      <div style="text-align: center; margin-top: 30px;">
        <button class="btn btn-success btn-large" onclick="runAllTests()">
          🚀 Run All Tests in Sequence
        </button>
        <button class="btn btn-primary" onclick="toggleTestView()" style="margin-left: 10px;">
          Toggle View
        </button>
        <button class="btn btn-danger" onclick="handleLogout()" style="margin-left: 10px;">
          Logout
        </button>
      </div>
    </div>
    
    <!-- Test Progress Modal -->
    <div class="modal" id="test-modal">
      <div class="modal-content">
        <h2 id="test-title">Test in Progress</h2>
        <div id="test-instructions"></div>
        <div class="progress-bar">
          <div class="progress-fill" id="progress-fill"></div>
        </div>
        <p id="progress-text">0% Complete</p>
        <div id="test-content"></div>
        <button class="btn btn-danger" onclick="stopCurrentTest()">Stop Test</button>
      </div>
    </div>
    
    <!-- Manual Model Selection Modal -->
    <div class="modal" id="model-selection-modal">
      <div class="modal-content">
        <h2>Manual Model Selection</h2>
        <p id="model-selection-reason">Multiple models matched. Please select the correct one:</p>
        <div id="model-options" style="margin: 20px 0;">
          <!-- Model options will be dynamically added here -->
        </div>
        <div style="margin-top: 20px;">
          <h4>Or enter model manually:</h4>
          <input type="text" id="manual-model-input" class="form-input" placeholder="e.g., HP EliteBook 840 G5" style="width: 100%; padding: 10px; margin: 10px 0; border: 1px solid #ddd; border-radius: 4px;">
        </div>
        <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
          <button class="btn btn-secondary" onclick="cancelModelSelection()">Cancel</button>
          <button class="btn btn-primary" onclick="confirmModelSelection()">Confirm</button>
        </div>
      </div>
    </div>
  </div>
  
  <script src="src/renderer.js"></script>
</body>
</html>