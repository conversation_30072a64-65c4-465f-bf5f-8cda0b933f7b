{"name": "laptop-qc-agent-v2", "version": "2.0.0", "description": "Modular QC testing agent for refurbished laptops", "main": "src/main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "dist": "electron-builder --publish=never", "dist:win32": "electron-builder --win --ia32 --publish=never", "dist:portable": "electron-builder --win portable --ia32 --publish=never", "postinstall": "electron-builder install-app-deps"}, "keywords": ["laptop", "qc", "testing", "refurbishment"], "author": "Laptop QC Team", "license": "UNLICENSED", "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"@supabase/supabase-js": "^2.38.0", "better-sqlite3": "^11.10.0", "electron-updater": "^6.1.7", "node-cron": "^3.0.3", "systeminformation": "^5.21.15", "uuid": "^9.0.1"}, "build": {"appId": "com.laptopqc.agent.v2", "productName": "Laptop QC Agent v2", "directories": {"output": "dist"}, "files": ["src/**/*", "index.html", "package.json", "!src/**/*.map", "!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}", "!**/node_modules/*.d.ts", "!**/node_modules/.bin"], "win": {"target": [{"target": "portable", "arch": ["ia32"]}], "icon": "assets/icon.ico"}, "portable": {"artifactName": "LaptopQC-Agent-v2-${version}-Win32-Portable.exe", "requestExecutionLevel": "admin"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "artifactName": "LaptopQC-Agent-v2-${version}-Win32-Setup.exe"}}}