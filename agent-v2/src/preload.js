const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // Authentication
  auth: {
    login: (pin) => ipcRenderer.invoke('auth:login', pin),
    logout: () => ipcRenderer.invoke('auth:logout'),
    getSession: () => ipcRenderer.invoke('auth:get-session'),
    hasPermission: (action) => ipcRenderer.invoke('auth:has-permission', action),
    onStatusChange: (callback) => {
      ipcRenderer.on('auth-status', (event, data) => callback(data));
    }
  },
  
  // System
  system: {
    getInfo: () => ipcRenderer.invoke('system:get-info')
  },
  
  // Modules
  modules: {
    getAvailable: () => ipcRenderer.invoke('modules:get-available'),
    getByCategory: (category) => ipcRenderer.invoke('modules:get-by-category', category),
    run: (moduleId, config) => ipcRenderer.invoke('modules:run', moduleId, config),
    stop: (moduleId) => ipcRenderer.invoke('modules:stop', moduleId),
    sendUserInput: (moduleId, response) => ipcRenderer.invoke('modules:user-input', moduleId, response),
    sendEvent: (moduleId, eventName, data) => ipcRenderer.invoke('modules:user-input', moduleId, { event: eventName, data }),
    onProgress: (callback) => {
      ipcRenderer.on('module-progress', (event, data) => callback(data));
    },
    onUserInputRequired: (callback) => {
      ipcRenderer.on('user-input-required', (event, data) => callback(data));
    },
    onInstructions: (callback) => {
      ipcRenderer.on('display-instructions', (event, data) => callback(data));
    },
    onDisplayUI: (callback) => {
      ipcRenderer.on('display-ui', (event, data) => callback(data));
    }
  },
  
  // Model profiles
  models: {
    detect: () => ipcRenderer.invoke('models:detect'),
    loadProfile: (model) => ipcRenderer.invoke('models:load-profile', model),
    getCurrent: () => ipcRenderer.invoke('models:get-current'),
    sendSelectionResponse: (model) => ipcRenderer.invoke('models:selection-response', model),
    onSelectionRequired: (callback) => {
      ipcRenderer.on('model-selection-required', (event, data) => callback(data));
    }
  },
  
  // Storage
  storage: {
    saveLaptop: (data) => ipcRenderer.invoke('storage:save-laptop', data),
    getLaptop: (identifier) => ipcRenderer.invoke('storage:get-laptop', identifier),
    saveHardwareTest: (data) => ipcRenderer.invoke('storage:save-hardware-test', data),
    saveInspection: (data) => ipcRenderer.invoke('storage:save-inspection', data)
  },
  
  // Sync
  sync: {
    getStatus: () => ipcRenderer.invoke('sync:status'),
    syncNow: () => ipcRenderer.invoke('sync:now'),
    onStatusChange: (callback) => {
      ipcRenderer.on('sync-status', (event, data) => callback(data));
    },
    onConnectionChange: (callback) => {
      ipcRenderer.on('connection-status', (event, data) => callback(data));
    }
  },
  
  // Workflow
  workflow: {
    run: (stage, laptopInfo, config) => ipcRenderer.invoke('workflow:run', stage, laptopInfo, config)
  }
});