const TestModule = require('../../core/framework/TestModule');
const si = require('systeminformation');
const { exec } = require('child_process');
const { promisify } = require('util');
const fs = require('fs').promises;
const path = require('path');

const execAsync = promisify(exec);

/**
 * Audio Test Module
 * Tests microphone recording and speaker playback
 * Note: May have limited functionality in live boot environments
 */
class AudioTest extends TestModule {
  constructor() {
    super({
      id: 'audio-test',
      name: 'Audio Test',
      description: 'Test microphone and speaker functionality',
      category: 'hardware',
      estimatedTime: 3,
      requiresUserInteraction: true
    });
    
    this.audioDevices = [];
    this.recordingPath = null;
    this.testTonePath = null;
  }

  async onInitialize() {
    // Create temp directory for audio files
    const tempDir = path.join(process.cwd(), 'temp', 'audio');
    try {
      await fs.mkdir(tempDir, { recursive: true });
      this.recordingPath = path.join(tempDir, 'test_recording.wav');
      this.testTonePath = path.join(tempDir, 'test_tone.wav');
    } catch (error) {
      console.error('Failed to create temp directory:', error);
    }
    
    // Try to detect audio devices
    try {
      const audio = await si.audio();
      this.audioDevices = audio;
      console.log(`Detected ${audio.length} audio devices`);
    } catch (error) {
      console.error('Failed to detect audio devices:', error);
    }
  }

  async onRunTest(config) {
    this.updateProgress(0, 'Starting audio test...');
    
    // Check if we're in a live boot environment
    const isLiveBoot = await this.checkLiveBootEnvironment();
    
    if (isLiveBoot) {
      await this.displayInstructions({
        title: 'Audio Test - Live Boot Mode',
        content: `
          <div style="padding: 20px; background: #fff3cd; border: 2px solid #ffc107; border-radius: 8px;">
            <h3 style="color: #856404;">⚠️ Limited Audio in Live Boot</h3>
            <p>Audio drivers may not be available in this environment.</p>
            <p>You can still perform basic physical checks:</p>
            <ul>
              <li>Check if speakers are physically present</li>
              <li>Check for visible damage to speaker grilles</li>
              <li>Verify microphone holes are not blocked</li>
              <li>Note the audio hardware for later testing</li>
            </ul>
          </div>
        `
      });
    }
    
    const results = {
      microphone: await this.testMicrophone(isLiveBoot),
      speakers: await this.testSpeakers(isLiveBoot),
      devices: this.audioDevices,
      driverStatus: isLiveBoot ? 'limited' : 'full'
    };
    
    const overallResult = this.calculateResult(results);
    
    return {
      component: 'audio',
      result: overallResult,
      details: results,
      notes: this.generateNotes(results, isLiveBoot)
    };
  }

  async checkLiveBootEnvironment() {
    // Check common indicators of live boot
    try {
      // Check if running from USB/CD
      const { stdout } = await execAsync('df / | grep -E "(tmpfs|overlay|aufs|squashfs)"');
      if (stdout) return true;
    } catch (error) {
      // Command failed, continue checking
    }
    
    // Check if common audio services are running
    try {
      const { stdout } = await execAsync('which pactl || which amixer');
      if (!stdout) return true;
    } catch (error) {
      return true; // No audio tools available
    }
    
    return false;
  }

  async testMicrophone(isLiveBoot) {
    this.updateProgress(20, 'Testing microphone...');
    
    if (isLiveBoot) {
      // Manual test only
      const hasBuiltInMic = await this.getUserInput(
        'Can you see a built-in microphone (usually near webcam or keyboard)?',
        ['Yes - Near webcam', 'Yes - On keyboard deck', 'No - Not visible', 'Not sure']
      );
      
      const micCondition = await this.getUserInput(
        'Physical condition of microphone area:',
        ['Clean and clear', 'Dusty/Dirty', 'Blocked', 'Damaged']
      );
      
      return {
        detected: hasBuiltInMic.startsWith('Yes'),
        location: hasBuiltInMic,
        condition: micCondition,
        tested: false,
        driverMissing: true
      };
    }
    
    // Full test with recording
    try {
      await this.displayInstructions({
        title: 'Microphone Test',
        content: `
          <div style="padding: 20px; text-align: center;">
            <h3>🎤 Microphone Recording Test</h3>
            <p>We will record 5 seconds of audio.</p>
            <p>Please speak clearly when prompted.</p>
            <div style="margin: 20px 0;">
              <strong>Say something like:</strong><br>
              "Testing microphone on laptop ${config.laptopModel || ''}"
            </div>
          </div>
        `
      });
      
      await this.waitForUserInput('Press any key when ready to start recording...', null, 5000);
      
      this.updateProgress(30, 'Recording... Speak now!');
      
      // Attempt to record audio
      const recordSuccess = await this.recordAudio();
      
      if (recordSuccess) {
        this.updateProgress(40, 'Recording complete. Playing back...');
        
        // Play back the recording
        const playbackSuccess = await this.playAudio(this.recordingPath);
        
        const quality = await this.getUserInput(
          'How was the recording quality?',
          ['Clear', 'Slightly Muffled', 'Very Quiet', 'Distorted', 'No Sound']
        );
        
        return {
          detected: true,
          tested: true,
          recordingSuccess: true,
          playbackSuccess: playbackSuccess,
          quality: quality,
          passed: quality !== 'No Sound'
        };
      } else {
        // Recording failed - manual test
        return await this.manualMicrophoneTest();
      }
    } catch (error) {
      console.error('Microphone test error:', error);
      return await this.manualMicrophoneTest();
    }
  }

  async manualMicrophoneTest() {
    const manualTest = await this.getUserInput(
      'Automatic test failed. Can you test microphone with another app?',
      ['Yes - Works fine', 'Yes - Poor quality', 'No - Not working', 'Cannot test']
    );
    
    return {
      detected: this.audioDevices.length > 0,
      tested: manualTest !== 'Cannot test',
      recordingSuccess: false,
      manualResult: manualTest,
      passed: manualTest === 'Yes - Works fine'
    };
  }

  async testSpeakers(isLiveBoot) {
    this.updateProgress(50, 'Testing speakers...');
    
    if (isLiveBoot) {
      // Physical inspection only
      const speakerLocation = await this.getUserInput(
        'Where are the speakers located?',
        ['Bottom front', 'Keyboard deck (top)', 'Side grilles', 'Not visible']
      );
      
      const speakerCondition = await this.getUserInput(
        'Physical condition of speaker grilles:',
        ['Clean and clear', 'Dusty', 'Partially blocked', 'Damaged/Missing']
      );
      
      return {
        left: { location: speakerLocation, condition: speakerCondition, tested: false },
        right: { location: speakerLocation, condition: speakerCondition, tested: false },
        driverMissing: true
      };
    }
    
    // Full speaker test
    try {
      // Generate test tone if not exists
      await this.generateTestTone();
      
      // Test left speaker
      this.updateProgress(60, 'Testing left speaker...');
      const leftResult = await this.testSingleSpeaker('left');
      
      // Test right speaker
      this.updateProgress(70, 'Testing right speaker...');
      const rightResult = await this.testSingleSpeaker('right');
      
      // Test both speakers
      this.updateProgress(80, 'Testing both speakers...');
      const bothResult = await this.testBothSpeakers();
      
      return {
        left: leftResult,
        right: rightResult,
        both: bothResult,
        passed: leftResult.passed && rightResult.passed
      };
    } catch (error) {
      console.error('Speaker test error:', error);
      return await this.manualSpeakerTest();
    }
  }

  async testSingleSpeaker(channel) {
    await this.displayInstructions({
      title: `Testing ${channel} speaker`,
      content: `
        <div style="padding: 20px; text-align: center;">
          <h3>🔊 ${channel.toUpperCase()} Speaker Test</h3>
          <p>Playing a test tone through the ${channel} speaker.</p>
          <p>You should hear a clear tone from the ${channel} side.</p>
        </div>
      `
    });
    
    const played = await this.playTestTone(channel);
    
    if (played) {
      const result = await this.getUserInput(
        `Did you hear sound from the ${channel} speaker?`,
        ['Yes - Clear', 'Yes - Distorted', 'Yes - Very Quiet', 'No Sound', 'Wrong Side']
      );
      
      return {
        tested: true,
        result: result,
        passed: result.startsWith('Yes') && result !== 'Wrong Side'
      };
    } else {
      return {
        tested: false,
        error: 'Could not play test tone',
        passed: false
      };
    }
  }

  async testBothSpeakers() {
    await this.displayInstructions({
      title: 'Testing both speakers',
      content: `
        <div style="padding: 20px; text-align: center;">
          <h3>🔊 Stereo Speaker Test</h3>
          <p>Playing test tone through both speakers.</p>
          <p>You should hear balanced sound from both sides.</p>
        </div>
      `
    });
    
    const played = await this.playTestTone('both');
    
    if (played) {
      const balance = await this.getUserInput(
        'How is the stereo balance?',
        ['Balanced', 'Left Louder', 'Right Louder', 'Only Left Works', 'Only Right Works']
      );
      
      const volume = await this.getUserInput(
        'Overall volume level:',
        ['Good/Normal', 'Too Quiet', 'Acceptable', 'No Sound']
      );
      
      return {
        tested: true,
        balance: balance,
        volume: volume,
        passed: balance !== 'Only Left Works' && balance !== 'Only Right Works' && volume !== 'No Sound'
      };
    } else {
      return {
        tested: false,
        error: 'Could not play test tone'
      };
    }
  }

  async manualSpeakerTest() {
    const manualTest = await this.getUserInput(
      'Automatic test failed. Can you test speakers with another app?',
      ['Yes - Both work fine', 'Left only works', 'Right only works', 'Poor quality', 'No sound', 'Cannot test']
    );
    
    return {
      left: { tested: true, manual: true, passed: manualTest.includes('Both') || manualTest.includes('Left') },
      right: { tested: true, manual: true, passed: manualTest.includes('Both') || manualTest.includes('Right') },
      manualResult: manualTest,
      passed: manualTest === 'Yes - Both work fine'
    };
  }

  async recordAudio() {
    try {
      // Try different recording methods based on OS
      if (process.platform === 'win32') {
        // Windows: Use PowerShell
        await execAsync(`powershell -Command "Add-Type -TypeDefinition @'
using System;
using System.Runtime.InteropServices;
public class Sound {
    [DllImport("winmm.dll")]
    public static extern int mciSendString(string command, string buffer, int bufferSize, IntPtr callback);
}
'@; [Sound]::mciSendString('record new waveaudio alias mic', '', 0, 0); Start-Sleep -Seconds 5; [Sound]::mciSendString('stop mic', '', 0, 0); [Sound]::mciSendString('save mic ${this.recordingPath}', '', 0, 0)"`, { timeout: 8000 });
      } else {
        // macOS/Linux: Use sox or arecord
        try {
          await execAsync(`arecord -d 5 -f cd ${this.recordingPath}`, { timeout: 8000 });
        } catch (e) {
          await execAsync(`sox -d -r 44100 -c 2 ${this.recordingPath} trim 0 5`, { timeout: 8000 });
        }
      }
      
      // Check if file was created
      const stats = await fs.stat(this.recordingPath);
      return stats.size > 1000; // At least 1KB
    } catch (error) {
      console.error('Recording failed:', error);
      return false;
    }
  }

  async playAudio(filePath) {
    try {
      if (process.platform === 'win32') {
        await execAsync(`powershell -Command "(New-Object Media.SoundPlayer '${filePath}').PlaySync()"`, { timeout: 8000 });
      } else if (process.platform === 'darwin') {
        await execAsync(`afplay "${filePath}"`, { timeout: 8000 });
      } else {
        // Linux
        try {
          await execAsync(`aplay "${filePath}"`, { timeout: 8000 });
        } catch (e) {
          await execAsync(`paplay "${filePath}"`, { timeout: 8000 });
        }
      }
      return true;
    } catch (error) {
      console.error('Playback failed:', error);
      return false;
    }
  }

  async generateTestTone() {
    // In a real implementation, we would generate a proper WAV file
    // For now, we'll check if audio tools are available
    try {
      if (process.platform === 'win32') {
        // Windows: Use PowerShell to generate beep
        this.playTestTone = async (channel) => {
          try {
            await execAsync('powershell -Command "[console]::beep(1000,1000)"');
            return true;
          } catch (error) {
            return false;
          }
        };
      } else {
        // macOS/Linux: Use speaker-test or other tools
        this.playTestTone = async (channel) => {
          try {
            const channelFlag = channel === 'left' ? '-c 2 -s 1' : channel === 'right' ? '-c 2 -s 2' : '-c 2';
            await execAsync(`speaker-test -t sine -f 1000 ${channelFlag} -l 1`, { timeout: 3000 });
            return true;
          } catch (error) {
            // Try alternative
            try {
              await execAsync(`play -n synth 1 sine 1000`, { timeout: 3000 });
              return true;
            } catch (e) {
              return false;
            }
          }
        };
      }
    } catch (error) {
      console.error('Test tone generation setup failed:', error);
    }
  }

  calculateResult(results) {
    // If in live boot, we can only do limited testing
    if (results.driverStatus === 'limited') {
      // Check physical condition
      const micOk = results.microphone.condition !== 'Damaged' && results.microphone.condition !== 'Blocked';
      const speakersOk = results.speakers.left?.condition !== 'Damaged/Missing';
      
      if (micOk && speakersOk) return 'partial'; // Needs full testing later
      return 'fail';
    }
    
    // Full test results
    const micPass = results.microphone.passed || results.microphone.manualResult === 'Yes - Works fine';
    const speakersPass = results.speakers.passed || results.speakers.manualResult === 'Yes - Both work fine';
    
    if (micPass && speakersPass) return 'pass';
    if (micPass || speakersPass) return 'partial';
    return 'fail';
  }

  generateNotes(results, isLiveBoot) {
    const notes = [];
    
    if (isLiveBoot) {
      notes.push('Limited testing due to missing audio drivers in live boot');
      notes.push(`Microphone location: ${results.microphone.location}`);
      notes.push(`Speaker condition: ${results.speakers.left?.condition || 'Unknown'}`);
      notes.push('Full audio test required after OS installation');
    } else {
      // Microphone results
      if (results.microphone.tested) {
        notes.push(`Microphone: ${results.microphone.quality || results.microphone.manualResult || 'Not tested'}`);
      }
      
      // Speaker results
      if (results.speakers.tested || results.speakers.manualResult) {
        if (results.speakers.both) {
          notes.push(`Speakers: ${results.speakers.both.balance}, Volume: ${results.speakers.both.volume}`);
        } else if (results.speakers.manualResult) {
          notes.push(`Speakers: ${results.speakers.manualResult}`);
        }
      }
    }
    
    // Device count
    if (results.devices.length > 0) {
      notes.push(`${results.devices.length} audio device(s) detected`);
    }
    
    return notes.join('. ');
  }

  async onCleanup() {
    // Clean up temp files
    try {
      if (this.recordingPath && await fs.access(this.recordingPath).then(() => true).catch(() => false)) {
        await fs.unlink(this.recordingPath);
      }
    } catch (error) {
      console.error('Cleanup error:', error);
    }
    
    this.emit('clearUI');
  }
}

module.exports = AudioTest;