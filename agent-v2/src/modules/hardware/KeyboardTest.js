const TestModule = require('../../core/framework/TestModule');

/**
 * Visual Keyboard Test Module with Smart Model Adaptation
 * Shows ASCII-art style keyboard with real-time visual feedback
 */
class KeyboardTest extends TestModule {
  constructor() {
    super({
      id: 'keyboard-test',
      name: 'Keyboard Test',
      description: 'Visual keyboard testing with real-time feedback',
      category: 'hardware',
      estimatedTime: 3,
      requiresUserInteraction: true
    });
    
    this.pressedKeys = new Set();
    this.failedKeys = new Set();
    this.modelProfile = null;
    this.keyMap = new Map(); // Maps display position to key info
    this.backlightIssue = false;
    this.testComplete = false;
  }

  async onInitialize() {
    console.log('Visual Keyboard Test initialized');
  }

  async onRunTest(config) {
    this.modelProfile = config.modelProfile;
    this.debugMode = config.debugMode || false;
    this.updateProgress(0, 'Starting keyboard test...');
    
    // SMART: Check if model has keyboard backlight
    const hasBacklight = this.modelProfile?.specifications?.keyboard?.backlight || false;
    const hasNumpad = this.modelProfile?.specifications?.keyboard?.numpad || false;
    
    // Show instructions adapted to model
    await this.displayInstructions({
      title: 'Keyboard Test Instructions',
      steps: [
        this.debugMode ? '** DEBUG MODE - Visual UI Only **' : null,
        'Press every key shown on the visual keyboard',
        'Green keys = Successfully tested ✓',
        'Red keys = Failed/Not working ✗',
        'Yellow keys = Not yet tested',
        hasBacklight ? 'Test keyboard backlight (usually Fn + Space or F5)' : null,
        hasNumpad ? 'Don\'t forget to test the numpad' : null,
        'Click "Report Issue" for any problems',
        'Click "Complete Test" when done'
      ].filter(Boolean)
    });

    // Display visual keyboard adapted to model
    await this.displayKeyboardUI(hasBacklight, hasNumpad);
    
    // In debug mode, just show UI and return after a delay
    if (this.debugMode) {
      this.updateProgress(50, 'Debug mode - showing UI only');
      // Don't wait for actual completion in debug mode
      return {
        result: 'debug',
        message: 'Debug mode - visual UI displayed',
        data: { debugMode: true }
      };
    }
    
    // Wait for test completion in normal mode
    const result = await this.waitForTestCompletion();
    
    this.updateProgress(100, 'Keyboard test completed');
    
    return result;
  }

  async displayKeyboardUI(hasBacklight, hasNumpad) {
    const keyboardLayout = this.generateKeyboardLayout(hasNumpad);
    
    this.emit('displayUI', {
      type: 'keyboard-test',
      content: `
        <div id="keyboard-test-container" style="padding: 20px; background: #f0f0f0; font-family: monospace;">
          <h2 style="text-align: center; margin-bottom: 20px;">
            ${this.modelProfile?.fullModel || 'Keyboard'} Test
          </h2>
          
          <!-- Visual ASCII-style Keyboard -->
          <div id="visual-keyboard" style="background: #333; padding: 20px; border-radius: 10px; box-shadow: 0 4px 8px rgba(0,0,0,0.3);">
            ${keyboardLayout}
          </div>
          
          <!-- Status Display -->
          <div style="margin-top: 20px; display: flex; justify-content: space-between; align-items: center;">
            <div style="font-size: 16px;">
              <span style="color: #4CAF50;">■</span> Tested (<span id="tested-count">0</span>)
              <span style="color: #F44336; margin-left: 20px;">■</span> Failed (<span id="failed-count">0</span>)
              <span style="color: #FFC107; margin-left: 20px;">■</span> Remaining (<span id="remaining-count">${this.keyMap.size}</span>)
            </div>
            <div>
              <button onclick="window.reportKeyboardIssue()" style="padding: 10px 20px; margin-right: 10px; background: #FF9800; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;">
                Report Issue
              </button>
              <button onclick="window.completeKeyboardTest()" style="padding: 10px 20px; background: #4CAF50; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;">
                Complete Test
              </button>
            </div>
          </div>
          
          <!-- Issue Reporting Panel (hidden by default) -->
          <div id="issue-panel" style="display: none; margin-top: 20px; padding: 15px; background: white; border-radius: 5px;">
            <h3>Report Keyboard Issues</h3>
            <label style="display: block; margin: 10px 0;">
              <input type="checkbox" id="missing-keys"> Missing Keys
              <input type="text" id="missing-keys-list" placeholder="e.g., F5, Enter" style="margin-left: 10px; display: none;">
            </label>
            <label style="display: block; margin: 10px 0;">
              <input type="checkbox" id="sticky-keys"> Sticky/Stuck Keys
              <input type="text" id="sticky-keys-list" placeholder="e.g., Space, Shift" style="margin-left: 10px; display: none;">
            </label>
            ${hasBacklight ? `
            <label style="display: block; margin: 10px 0;">
              <input type="checkbox" id="backlight-issue"> Backlight Not Working
            </label>
            ` : ''}
            <button onclick="window.submitKeyboardIssues()" style="margin-top: 10px; padding: 8px 16px; background: #2196F3; color: white; border: none; border-radius: 3px;">
              Submit Issues
            </button>
          </div>
        </div>
        
        <script>
          // Initialize counters
          let testedCount = 0;
          let failedCount = 0;
          const totalKeys = ${this.keyMap.size};
          
          // Track key presses
          document.addEventListener('keydown', (e) => {
            e.preventDefault();
            const keyElement = document.querySelector('[data-key="' + e.code + '"]');
            if (keyElement && !keyElement.classList.contains('tested')) {
              keyElement.classList.add('tested');
              keyElement.style.background = '#4CAF50';
              keyElement.style.color = 'white';
              testedCount++;
              updateCounts();
              window.electronAPI.modules.sendEvent('keyboard-test', 'keyPressed', { code: e.code, key: e.key });
            }
          });
          
          function updateCounts() {
            document.getElementById('tested-count').textContent = testedCount;
            document.getElementById('failed-count').textContent = failedCount;
            document.getElementById('remaining-count').textContent = totalKeys - testedCount - failedCount;
          }
          
          // UI functions
          window.reportKeyboardIssue = function() {
            document.getElementById('issue-panel').style.display = 'block';
          }
          
          window.completeKeyboardTest = function() {
            window.electronAPI.modules.sendEvent('keyboard-test', 'testComplete', { testedCount, failedCount });
          }
          
          window.submitKeyboardIssues = function() {
            const issues = {
              missingKeys: document.getElementById('missing-keys').checked ? 
                document.getElementById('missing-keys-list').value : null,
              stickyKeys: document.getElementById('sticky-keys').checked ?
                document.getElementById('sticky-keys-list').value : null,
              backlightIssue: document.getElementById('backlight-issue')?.checked || false
            };
            window.electronAPI.modules.sendEvent('keyboard-test', 'issuesReported', issues);
            document.getElementById('issue-panel').style.display = 'none';
          }
          
          // Show/hide text inputs based on checkbox
          document.getElementById('missing-keys').addEventListener('change', (e) => {
            document.getElementById('missing-keys-list').style.display = e.target.checked ? 'inline' : 'none';
          });
          document.getElementById('sticky-keys').addEventListener('change', (e) => {
            document.getElementById('sticky-keys-list').style.display = e.target.checked ? 'inline' : 'none';
          });
        </script>
      `
    });
  }

  generateKeyboardLayout(hasNumpad) {
    // Generate ASCII-art style keyboard layout with visual feedback
    const rows = [
      // Function row
      [
        { code: 'Escape', label: 'ESC', width: 1.5 },
        { code: 'F1', label: 'F1' },
        { code: 'F2', label: 'F2' },
        { code: 'F3', label: 'F3' },
        { code: 'F4', label: 'F4' },
        { code: 'F5', label: 'F5' },
        { code: 'F6', label: 'F6' },
        { code: 'F7', label: 'F7' },
        { code: 'F8', label: 'F8' },
        { code: 'F9', label: 'F9' },
        { code: 'F10', label: 'F10' },
        { code: 'F11', label: 'F11' },
        { code: 'F12', label: 'F12' },
        { code: 'Delete', label: 'DEL', width: 1.5 }
      ],
      // Number row
      [
        { code: 'Backquote', label: '`' },
        { code: 'Digit1', label: '1' },
        { code: 'Digit2', label: '2' },
        { code: 'Digit3', label: '3' },
        { code: 'Digit4', label: '4' },
        { code: 'Digit5', label: '5' },
        { code: 'Digit6', label: '6' },
        { code: 'Digit7', label: '7' },
        { code: 'Digit8', label: '8' },
        { code: 'Digit9', label: '9' },
        { code: 'Digit0', label: '0' },
        { code: 'Minus', label: '-' },
        { code: 'Equal', label: '=' },
        { code: 'Backspace', label: '←', width: 2 }
      ],
      // QWERTY row
      [
        { code: 'Tab', label: 'TAB', width: 1.5 },
        { code: 'KeyQ', label: 'Q' },
        { code: 'KeyW', label: 'W' },
        { code: 'KeyE', label: 'E' },
        { code: 'KeyR', label: 'R' },
        { code: 'KeyT', label: 'T' },
        { code: 'KeyY', label: 'Y' },
        { code: 'KeyU', label: 'U' },
        { code: 'KeyI', label: 'I' },
        { code: 'KeyO', label: 'O' },
        { code: 'KeyP', label: 'P' },
        { code: 'BracketLeft', label: '[' },
        { code: 'BracketRight', label: ']' },
        { code: 'Backslash', label: '\\', width: 1.5 }
      ],
      // ASDF row
      [
        { code: 'CapsLock', label: 'CAPS', width: 1.75 },
        { code: 'KeyA', label: 'A' },
        { code: 'KeyS', label: 'S' },
        { code: 'KeyD', label: 'D' },
        { code: 'KeyF', label: 'F' },
        { code: 'KeyG', label: 'G' },
        { code: 'KeyH', label: 'H' },
        { code: 'KeyJ', label: 'J' },
        { code: 'KeyK', label: 'K' },
        { code: 'KeyL', label: 'L' },
        { code: 'Semicolon', label: ';' },
        { code: 'Quote', label: '\'' },
        { code: 'Enter', label: '↵', width: 2.25 }
      ],
      // ZXCV row
      [
        { code: 'ShiftLeft', label: 'SHIFT', width: 2.25 },
        { code: 'KeyZ', label: 'Z' },
        { code: 'KeyX', label: 'X' },
        { code: 'KeyC', label: 'C' },
        { code: 'KeyV', label: 'V' },
        { code: 'KeyB', label: 'B' },
        { code: 'KeyN', label: 'N' },
        { code: 'KeyM', label: 'M' },
        { code: 'Comma', label: ',' },
        { code: 'Period', label: '.' },
        { code: 'Slash', label: '/' },
        { code: 'ShiftRight', label: 'SHIFT', width: 2.75 }
      ],
      // Bottom row
      [
        { code: 'ControlLeft', label: 'CTRL', width: 1.5 },
        { code: 'MetaLeft', label: 'WIN', width: 1.25 },
        { code: 'AltLeft', label: 'ALT', width: 1.25 },
        { code: 'Space', label: 'SPACE', width: 6.25 },
        { code: 'AltRight', label: 'ALT', width: 1.25 },
        { code: 'MetaRight', label: 'FN', width: 1.25 },
        { code: 'ContextMenu', label: '☰', width: 1.25 },
        { code: 'ControlRight', label: 'CTRL', width: 1.5 }
      ]
    ];

    let html = '';
    const keyWidth = 50; // Base key width in pixels
    const keyHeight = 50; // Key height in pixels
    const keyGap = 4; // Gap between keys
    
    // Generate main keyboard
    rows.forEach((row, rowIndex) => {
      html += `<div style="display: flex; margin-bottom: ${keyGap}px;">`;
      
      row.forEach(key => {
        const width = (key.width || 1) * keyWidth + ((key.width || 1) - 1) * keyGap;
        
        // Store key in map for tracking
        this.keyMap.set(key.code, key);
        
        html += `
          <div class="keyboard-key" 
               data-key="${key.code}"
               style="width: ${width}px; 
                      height: ${keyHeight}px; 
                      margin-right: ${keyGap}px;
                      background: #FFC107;
                      border: 2px solid #222;
                      border-radius: 5px;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      font-size: 14px;
                      font-weight: bold;
                      color: #000;
                      cursor: pointer;
                      transition: all 0.1s;
                      user-select: none;
                      box-shadow: 0 2px 4px rgba(0,0,0,0.2);">
            ${key.label}
          </div>
        `;
      });
      
      html += '</div>';
    });
    
    return html;
  }

  async waitForTestCompletion() {
    return new Promise((resolve) => {
      // Set up event handlers
      this.on('keyPressed', (data) => {
        this.pressedKeys.add(data.code);
        this.updateProgress(
          (this.pressedKeys.size / this.keyMap.size) * 100,
          `${this.pressedKeys.size}/${this.keyMap.size} keys tested`
        );
      });
      
      this.on('testComplete', (data) => {
        this.testComplete = true;
        resolve(this.generateResults());
      });
      
      this.on('issuesReported', (issues) => {
        this.processReportedIssues(issues);
      });
    });
  }

  processReportedIssues(issues) {
    if (issues.missingKeys) {
      issues.missingKeys.split(',').forEach(key => {
        this.failedKeys.add(key.trim());
      });
    }
    
    if (issues.stickyKeys) {
      issues.stickyKeys.split(',').forEach(key => {
        this.failedKeys.add(key.trim());
      });
    }
    
    this.backlightIssue = issues.backlightIssue;
  }

  generateResults() {
    const totalKeys = this.keyMap.size;
    const testedKeys = this.pressedKeys.size;
    const untestedKeys = totalKeys - testedKeys;
    
    // SMART result determination based on issues
    let result = 'pass';
    let resultReason = null;
    
    if (this.failedKeys.size > 0) {
      result = 'fail';
      resultReason = 'Hardware failure - keys not working';
    } else if (untestedKeys > totalKeys * 0.1) { // More than 10% untested
      result = 'partial';
      resultReason = 'Test incomplete - too many keys untested';
    } else if (this.modelProfile?.specifications?.keyboard?.backlight && this.backlightIssue) {
      result = 'partial';
      resultReason = 'Keyboard functional but backlight not working';
    }
    
    return {
      component: 'keyboard',
      result: result,
      resultReason: resultReason,
      details: {
        totalKeys: totalKeys,
        testedKeys: testedKeys,
        untestedKeys: untestedKeys,
        failedKeys: Array.from(this.failedKeys),
        backlightExpected: this.modelProfile?.specifications?.keyboard?.backlight || false,
        backlightWorking: !this.backlightIssue,
        keyboardLayout: this.modelProfile?.specifications?.keyboard?.layout || 'US'
      },
      notes: this.generateNotes()
    };
  }

  generateNotes() {
    const notes = [];
    
    if (this.failedKeys.size > 0) {
      notes.push(`Failed keys: ${Array.from(this.failedKeys).join(', ')}`);
    }
    
    const untestedCount = this.keyMap.size - this.pressedKeys.size;
    if (untestedCount > 0) {
      notes.push(`${untestedCount} keys not tested`);
    }
    
    if (this.backlightIssue && this.modelProfile?.specifications?.keyboard?.backlight) {
      notes.push('Keyboard backlight not working');
    }
    
    return notes.join('. ') || 'All keys tested successfully';
  }

  async onCleanup() {
    this.pressedKeys.clear();
    this.failedKeys.clear();
    this.keyMap.clear();
    this.testComplete = false;
    this.emit('clearUI');
  }
}

module.exports = KeyboardTest;