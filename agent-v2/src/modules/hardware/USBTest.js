const TestModule = require('../../core/framework/TestModule');
const si = require('systeminformation');

/**
 * Smart USB Port Test Module
 * Dynamically adapts based on model capabilities and driver availability
 */
class USBTest extends TestModule {
  constructor() {
    super({
      id: 'usb-test',
      name: 'USB Port Test',
      description: 'Smart USB testing with visual port layout and model adaptation',
      category: 'hardware',
      estimatedTime: 5,
      requiresUserInteraction: true
    });
    
    this.portResults = [];
    this.modelProfile = null;
    this.isLiveBoot = false;
    this.hasUSB3Driver = true;
    this.currentPortTest = null;
  }

  async onInitialize() {
    // Detect if we're in a live boot environment
    this.isLiveBoot = await this.detectLiveBoot();
    
    // Check for USB 3.0 driver availability
    this.hasUSB3Driver = await this.checkUSB3Driver();
    
    console.log(`USB Test initialized - Live boot: ${this.isLiveBoot}, USB3 driver: ${this.hasUSB3Driver}`);
  }

  async detectLiveBoot() {
    try {
      const osInfo = await si.osInfo();
      return osInfo.distro.toLowerCase().includes('pe') || 
             osInfo.hostname.toLowerCase().includes('livecd') ||
             process.env.LIVE_BOOT === 'true';
    } catch (error) {
      return false;
    }
  }

  async checkUSB3Driver() {
    try {
      const usb = await si.usb();
      // Look for USB 3.0/xHCI controllers
      return usb.some(device => 
        device.name?.toLowerCase().includes('usb 3') || 
        device.name?.toLowerCase().includes('xhci') ||
        device.type?.toLowerCase().includes('usb3')
      );
    } catch (error) {
      return false;
    }
  }

  async onRunTest(config) {
    this.modelProfile = config.modelProfile;
    this.debugMode = config.debugMode || false;
    this.updateProgress(0, 'Starting smart USB test...');
    
    // Extract USB configuration from model profile with smart defaults
    const usbConfig = this.modelProfile?.specifications?.usb_ports || {
      total: 3,
      usb2: 1,
      usb3: 2,
      usbc: 0,
      locations: { left: 2, right: 1, back: 0 }
    };
    
    // SMART: Adapt instructions based on what's actually present
    await this.displaySmartInstructions(usbConfig);
    
    // Display visual USB port layout
    await this.displayUSBPortLayout(usbConfig);
    
    // In debug mode, just show UI and return
    if (this.debugMode) {
      this.updateProgress(50, 'Debug mode - showing UI only');
      return {
        result: 'debug',
        message: 'Debug mode - visual UI displayed',
        data: { debugMode: true }
      };
    }
    
    // Wait for test completion in normal mode
    const result = await this.performSmartUSBTest(usbConfig);
    
    this.updateProgress(100, 'USB test completed');
    
    return result;
  }

  async displaySmartInstructions(usbConfig) {
    const steps = [
      this.debugMode ? '** DEBUG MODE - Visual UI Only **' : null,
      `This model has ${usbConfig.total} USB ports:`
    ];
    
    // SMART: Only show port types that actually exist
    if (usbConfig.usb2 > 0) {
      steps.push(`• ${usbConfig.usb2} USB 2.0 port${usbConfig.usb2 > 1 ? 's' : ''} (black inside)`);
    }
    if (usbConfig.usb3 > 0) {
      steps.push(`• ${usbConfig.usb3} USB 3.0 port${usbConfig.usb3 > 1 ? 's' : ''} (blue inside)`);
    }
    if (usbConfig.usbc > 0) {
      steps.push(`• ${usbConfig.usbc} USB Type-C port${usbConfig.usbc > 1 ? 's' : ''}`);
    }
    
    steps.push('', 'You will need a USB test device');
    steps.push('Click on each port in the diagram to test it');
    
    // SMART: Warn about driver limitations if detected
    if (this.isLiveBoot && !this.hasUSB3Driver && usbConfig.usb3 > 0) {
      steps.push('', '⚠️ Live Boot Environment Detected:');
      steps.push('USB 3.0 ports may work at 2.0 speed due to missing drivers');
    }
    
    await this.displayInstructions({
      title: `USB Port Test - ${this.modelProfile?.fullModel || 'Unknown Model'}`,
      steps: steps
    });
  }

  async displayUSBPortLayout(usbConfig) {
    // Generate dynamic port layout based on model
    const portMap = this.generatePortMap(usbConfig);
    
    this.emit('displayUI', {
      type: 'usb-test',
      content: `
        <div id="usb-test-container" style="padding: 20px; font-family: Arial, sans-serif;">
          <h2 style="text-align: center;">${this.modelProfile?.fullModel || 'Laptop'} USB Port Layout</h2>
          
          <!-- Visual laptop diagram with clickable USB ports -->
          <div style="position: relative; width: 700px; height: 400px; margin: 20px auto; background: #f5f5f5; border-radius: 15px; padding: 20px;">
            <!-- Laptop body representation -->
            <div style="position: absolute; top: 50px; left: 150px; width: 400px; height: 250px; background: #333; border-radius: 10px; box-shadow: 0 4px 8px rgba(0,0,0,0.3);">
              <!-- Screen area -->
              <div style="position: absolute; top: 10px; left: 10px; right: 10px; height: 200px; background: #000; border-radius: 5px;">
                <div style="color: white; text-align: center; padding-top: 80px; font-size: 20px;">
                  ${this.modelProfile?.manufacturer || ''} ${this.modelProfile?.model || ''}
                </div>
              </div>
              <!-- Keyboard area -->
              <div style="position: absolute; bottom: 10px; left: 10px; right: 10px; height: 30px; background: #555; border-radius: 3px;"></div>
            </div>
            
            <!-- USB Ports with visual representation -->
            ${portMap}
            
            <!-- Legend -->
            <div style="position: absolute; bottom: 10px; left: 10px; background: white; padding: 10px; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
              <div style="font-size: 12px; margin-bottom: 5px;"><span style="display: inline-block; width: 20px; height: 10px; background: #333; border: 2px solid #666;"></span> USB 2.0</div>
              ${usbConfig.usb3 > 0 ? '<div style="font-size: 12px; margin-bottom: 5px;"><span style="display: inline-block; width: 20px; height: 10px; background: #0066CC; border: 2px solid #666;"></span> USB 3.0</div>' : ''}
              ${usbConfig.usbc > 0 ? '<div style="font-size: 12px;"><span style="display: inline-block; width: 16px; height: 8px; background: #666; border: 2px solid #666; border-radius: 4px;"></span> USB-C</div>' : ''}
            </div>
          </div>
          
          <!-- Status panel -->
          <div id="usb-status-panel" style="margin-top: 30px; padding: 20px; background: white; border-radius: 10px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <h3>Test Progress</h3>
            <div id="port-status-list" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 10px; margin-top: 15px;"></div>
          </div>
          
          <!-- Test controls -->
          <div style="margin-top: 20px; text-align: center;">
            <button id="report-issue-btn" onclick="window.reportUSBIssue()" style="padding: 12px 24px; margin-right: 10px; background: #ff9800; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;">
              Report Issue
            </button>
            <button id="complete-test-btn" onclick="window.completeUSBTest()" style="padding: 12px 24px; background: #4CAF50; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;">
              Complete Test
            </button>
          </div>
          
          <!-- Port test dialog (hidden by default) -->
          <div id="port-test-dialog" style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.3); z-index: 1000; min-width: 400px;">
            <h3 id="port-test-title">Testing Port</h3>
            <div id="port-test-content" style="margin: 20px 0;"></div>
            <div id="port-test-buttons" style="text-align: center;"></div>
          </div>
        </div>
        
        <script>
          let currentPort = null;
          let testResults = {};
          
          window.testUSBPort = function(portId, portType, location) {
            currentPort = { id: portId, type: portType, location: location };
            
            // Highlight selected port
            document.querySelectorAll('.usb-port').forEach(p => {
              p.style.borderWidth = '3px';
              p.style.borderColor = '#666';
            });
            document.getElementById('port-' + portId).style.borderWidth = '4px';
            document.getElementById('port-' + portId).style.borderColor = '#2196F3';
            
            // Show test dialog
            showPortTestDialog();
          }
          
          function showPortTestDialog() {
            const dialog = document.getElementById('port-test-dialog');
            const title = document.getElementById('port-test-title');
            const content = document.getElementById('port-test-content');
            const buttons = document.getElementById('port-test-buttons');
            
            title.textContent = 'Testing ' + currentPort.type.toUpperCase() + ' Port #' + currentPort.id;
            content.innerHTML = \`
              <p>Please insert your USB test device into the <strong>\${currentPort.location}</strong> \${currentPort.type.toUpperCase()} port.</p>
              <p style="margin-top: 15px;">Is the device detected?</p>
            \`;
            
            buttons.innerHTML = \`
              <button onclick="portTestResult('working')" style="padding: 10px 20px; margin: 5px; background: #4CAF50; color: white; border: none; border-radius: 5px; cursor: pointer;">✓ Yes - Working</button>
              <button onclick="portTestResult('failed')" style="padding: 10px 20px; margin: 5px; background: #f44336; color: white; border: none; border-radius: 5px; cursor: pointer;">✗ No - Not Working</button>
              <button onclick="portTestResult('partial')" style="padding: 10px 20px; margin: 5px; background: #ff9800; color: white; border: none; border-radius: 5px; cursor: pointer;">⚠ Partial (2.0 only)</button>
              \${window.isLiveBoot ? '<button onclick="portTestResult(\'driver-issue\')" style="padding: 10px 20px; margin: 5px; background: #2196f3; color: white; border: none; border-radius: 5px; cursor: pointer;">🔧 Driver Issue</button>' : ''}
            \`;
            
            dialog.style.display = 'block';
          }
          
          window.portTestResult = function(result) {
            testResults[currentPort.id] = {
              port: currentPort,
              result: result,
              timestamp: new Date()
            };
            
            // Update port visual
            updatePortVisual(currentPort.id, result);
            
            // Update status panel
            updateStatusPanel();
            
            // Hide dialog
            document.getElementById('port-test-dialog').style.display = 'none';
            
            // Send result to backend
            window.electronAPI.modules.sendEvent('usb-test', 'portTested', testResults[currentPort.id]);
          }
          
          function updatePortVisual(portId, result) {
            const port = document.getElementById('port-' + portId);
            const colors = {
              'working': '#4CAF50',
              'partial': '#FF9800',
              'failed': '#F44336',
              'driver-issue': '#2196F3'
            };
            port.style.background = colors[result] || '#FFC107';
            port.style.borderColor = '#333';
          }
          
          function updateStatusPanel() {
            const statusList = document.getElementById('port-status-list');
            statusList.innerHTML = '';
            
            Object.values(testResults).forEach(test => {
              const statusCard = document.createElement('div');
              statusCard.style.cssText = 'padding: 10px; border-radius: 5px; background: #f5f5f5;';
              
              const icons = {
                'working': '✓',
                'partial': '⚠',
                'failed': '✗',
                'driver-issue': '🔧'
              };
              
              statusCard.innerHTML = \`
                <strong>\${icons[test.result] || '?'} Port \${test.port.id}</strong><br>
                <small>\${test.port.type.toUpperCase()} - \${test.port.location}</small><br>
                <small>\${test.result.charAt(0).toUpperCase() + test.result.slice(1).replace('-', ' ')}</small>
              \`;
              
              statusList.appendChild(statusCard);
            });
          }
          
          window.reportUSBIssue = function() {
            // Would show issue reporting dialog
            window.electronAPI.modules.sendEvent('usb-test', 'reportIssue', { currentPort, testResults });
          }
          
          window.completeUSBTest = function() {
            window.electronAPI.modules.sendEvent('usb-test', 'testComplete', testResults);
          }
          
          // Pass live boot status to frontend
          window.isLiveBoot = \${this.isLiveBoot};
        </script>
      `
    });
  }

  generatePortMap(usbConfig) {
    let html = '';
    let portId = 1;
    const ports = [];
    
    // SMART: Distribute port types based on common patterns
    // Usually USB-C comes last, USB 3.0 in middle positions
    
    // Left side ports
    if (usbConfig.locations.left > 0) {
      for (let i = 0; i < usbConfig.locations.left; i++) {
        const portType = this.assignSmartPortType(portId, usbConfig, 'left');
        const yPos = 120 + (i * 60);
        html += this.createPortElement(portId, portType, 'left', 100, yPos);
        ports.push({ id: portId, type: portType, location: 'left' });
        portId++;
      }
    }
    
    // Right side ports
    if (usbConfig.locations.right > 0) {
      for (let i = 0; i < usbConfig.locations.right; i++) {
        const portType = this.assignSmartPortType(portId, usbConfig, 'right');
        const yPos = 120 + (i * 60);
        html += this.createPortElement(portId, portType, 'right', 560, yPos);
        ports.push({ id: portId, type: portType, location: 'right' });
        portId++;
      }
    }
    
    // Back ports
    if (usbConfig.locations.back > 0) {
      for (let i = 0; i < usbConfig.locations.back; i++) {
        const portType = this.assignSmartPortType(portId, usbConfig, 'back');
        const xPos = 250 + (i * 80);
        html += this.createPortElement(portId, portType, 'back', xPos, 30);
        ports.push({ id: portId, type: portType, location: 'back' });
        portId++;
      }
    }
    
    return html;
  }

  assignSmartPortType(portId, usbConfig, location) {
    // SMART port type assignment based on typical patterns
    const totalPorts = usbConfig.total;
    const assignedPorts = {
      usb2: 0,
      usb3: 0,
      usbc: 0
    };
    
    // Count already assigned ports
    for (let i = 1; i < portId; i++) {
      // This is simplified - in real implementation, track assignments
    }
    
    // USB-C typically on sides, rarely on back
    if (usbConfig.usbc > 0 && location !== 'back' && portId > totalPorts - usbConfig.usbc) {
      return 'usbc';
    }
    
    // USB 3.0 common on all sides
    if (usbConfig.usb3 > 0 && portId > usbConfig.usb2) {
      return 'usb3';
    }
    
    // Default to USB 2.0
    return 'usb2';
  }

  createPortElement(id, type, location, x, y) {
    const specs = {
      usb2: { width: 40, height: 15, color: '#333', label: '2.0' },
      usb3: { width: 40, height: 15, color: '#0066CC', label: '3.0' },
      usbc: { width: 25, height: 12, color: '#666', label: 'C', radius: '6px' }
    };
    
    const port = specs[type] || specs.usb2;
    
    return `
      <div id="port-${id}" 
           class="usb-port"
           onclick="window.testUSBPort(${id}, '${type}', '${location}')"
           style="position: absolute; 
                  left: ${x}px; 
                  top: ${y}px; 
                  width: ${port.width}px; 
                  height: ${port.height}px;
                  background: ${port.color};
                  border: 3px solid #666;
                  border-radius: ${port.radius || '2px'};
                  cursor: pointer;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  color: white;
                  font-size: 11px;
                  font-weight: bold;
                  transition: all 0.2s;
                  box-shadow: 0 2px 4px rgba(0,0,0,0.2);">
        ${port.label}
      </div>
    `;
  }

  async performSmartUSBTest(usbConfig) {
    return new Promise((resolve) => {
      const testResults = new Map();
      
      // Set up event handlers
      this.on('portTested', (data) => {
        testResults.set(data.port.id, data);
        
        // Update progress
        const progress = (testResults.size / usbConfig.total) * 100;
        this.updateProgress(progress, `${testResults.size}/${usbConfig.total} ports tested`);
      });
      
      this.on('testComplete', (allResults) => {
        // Process all results
        Object.values(allResults).forEach(result => {
          this.portResults.push({
            portNumber: result.port.id,
            type: result.port.type,
            location: result.port.location,
            result: result.result,
            reason: this.getResultReason(result)
          });
        });
        
        resolve(this.calculateSmartResult(usbConfig));
      });
      
      this.on('reportIssue', (data) => {
        // Handle issue reporting
        console.log('USB issue reported:', data);
      });
    });
  }

  getResultReason(testResult) {
    switch (testResult.result) {
      case 'working':
        return 'Port functioning correctly';
      case 'partial':
        return testResult.port.type === 'usb3' ? 
          'USB 3.0 port working at USB 2.0 speed only' : 
          'Port partially functional';
      case 'failed':
        return 'Port not working - no device detection';
      case 'driver-issue':
        return 'Cannot verify full functionality - driver missing in live environment';
      default:
        return 'Unknown status';
    }
  }

  calculateSmartResult(usbConfig) {
    const results = {
      working: 0,
      partial: 0,
      failed: 0,
      driverIssue: 0,
      notTested: 0
    };
    
    // Count results
    this.portResults.forEach(port => {
      if (port.result === 'driver-issue') {
        results.driverIssue++;
      } else if (port.result === 'working') {
        results.working++;
      } else if (port.result === 'partial') {
        results.partial++;
      } else if (port.result === 'failed') {
        results.failed++;
      }
    });
    
    // Calculate untested ports
    results.notTested = usbConfig.total - this.portResults.length;
    
    // SMART result determination
    let overallResult = 'pass';
    let resultReason = null;
    
    if (results.failed > 0) {
      overallResult = 'fail';
      resultReason = `${results.failed} port(s) not working`;
    } else if (results.partial > 0) {
      overallResult = 'partial';
      resultReason = `${results.partial} port(s) with limited functionality`;
    } else if (results.driverIssue > 0 && results.working === 0) {
      overallResult = 'driver-issue';
      resultReason = 'Unable to fully test due to missing drivers';
    } else if (results.notTested > 0) {
      overallResult = 'not-tested';
      resultReason = `${results.notTested} port(s) not tested`;
    }
    
    return {
      component: 'usb_ports',
      result: overallResult,
      resultReason: resultReason,
      details: {
        modelExpected: usbConfig,
        tested: this.portResults.length,
        results: results,
        portDetails: this.portResults,
        environment: {
          isLiveBoot: this.isLiveBoot,
          hasUSB3Driver: this.hasUSB3Driver
        }
      },
      notes: this.generateSmartNotes(results, usbConfig)
    };
  }

  generateSmartNotes(results, usbConfig) {
    const notes = [];
    
    // Summary of results
    if (results.working === usbConfig.total) {
      notes.push('All USB ports working correctly');
    } else {
      notes.push(`${results.working}/${usbConfig.total} ports fully functional`);
    }
    
    // Specific issues
    if (results.failed > 0) {
      notes.push(`${results.failed} port(s) completely non-functional`);
    }
    
    if (results.partial > 0) {
      const partialPorts = this.portResults.filter(p => p.result === 'partial');
      const usb3Degraded = partialPorts.filter(p => p.type === 'usb3').length;
      if (usb3Degraded > 0) {
        notes.push(`${usb3Degraded} USB 3.0 port(s) limited to USB 2.0 speed`);
      }
    }
    
    if (results.driverIssue > 0) {
      notes.push(`${results.driverIssue} port(s) couldn't be fully tested (driver missing)`);
      if (this.isLiveBoot) {
        notes.push('Live boot environment detected - full testing requires proper OS');
      }
    }
    
    if (results.notTested > 0) {
      notes.push(`${results.notTested} port(s) were not tested`);
    }
    
    return notes.join('. ');
  }

  async onCleanup() {
    this.portResults = [];
    this.currentPortTest = null;
    this.emit('clearUI');
  }
}

module.exports = USBTest;