const TestModule = require('../../core/framework/TestModule');

/**
 * Smart Display Test Module
 * Adapts based on display type (touchscreen, resolution, external display support)
 */
class DisplayTest extends TestModule {
  constructor() {
    super({
      id: 'display-test',
      name: 'Display Test',
      description: 'Smart display testing with model-aware features',
      category: 'hardware',
      estimatedTime: 3,
      requiresUserInteraction: true
    });
    
    this.testColors = [
      { name: 'White', color: '#FFFFFF' },
      { name: 'Black', color: '#000000' },
      { name: 'Red', color: '#FF0000' },
      { name: 'Green', color: '#00FF00' },
      { name: 'Blue', color: '#0000FF' }
    ];
    
    this.currentColorIndex = 0;
    this.modelProfile = null;
    this.displayBroken = false;
    this.issues = {
      deadPixels: { count: 0, locations: [] },
      lines: { horizontal: false, vertical: false, description: '' },
      patches: { present: false, description: '' },
      backlight: { uniform: true, description: '' },
      touchscreen: { tested: false, working: null },
      overall: 'good'
    };
  }

  async onInitialize() {
    console.log('Display test module initialized');
  }

  async onRunTest(config) {
    this.modelProfile = config.modelProfile;
    this.debugMode = config.debugMode || false;
    this.updateProgress(0, 'Starting smart display test...');
    
    // SMART: Check display capabilities
    const displaySpecs = this.modelProfile?.specifications?.display || {};
    const hasTouchscreen = displaySpecs.touchscreen || false;
    const resolution = displaySpecs.resolution || '1920x1080';
    const displayType = displaySpecs.type || 'LCD';
    
    // In debug mode, skip display functionality check
    const displayWorking = this.debugMode ? true : await this.checkDisplayFunctionality();
    
    if (!displayWorking) {
      // SMART: Offer external display test option
      return await this.handleBrokenDisplay();
    }
    
    // Show adapted instructions
    await this.displaySmartInstructions(hasTouchscreen, displayType);
    
    // In debug mode, show color test UI and return
    if (this.debugMode) {
      await this.displayColorTest(this.colors[0]);
      this.updateProgress(50, 'Debug mode - showing UI only');
      return {
        component: 'display',
        result: 'debug',
        details: { debugMode: true },
        message: 'Debug mode - visual UI displayed'
      };
    }
    
    // Run appropriate tests
    await this.runColorCycleTest();
    
    // SMART: Only test touchscreen if model has it
    if (hasTouchscreen) {
      await this.testTouchscreen();
    }
    
    // Get final assessment
    await this.getFinalAssessment();
    
    // Calculate smart result
    const result = this.calculateSmartResult();
    
    this.updateProgress(100, 'Display test completed');
    
    return result;
  }

  async checkDisplayFunctionality() {
    const working = await this.getUserInput(
      'Is the laptop display showing anything?',
      ['Yes - Display Working', 'No - Black Screen', 'Broken/Cracked']
    );
    
    if (working.includes('No') || working.includes('Broken')) {
      this.displayBroken = true;
      return false;
    }
    return true;
  }

  async handleBrokenDisplay() {
    const hasExternal = await this.getUserInput(
      'Display not working. Do you have an external monitor to test with?',
      ['Yes - Test with External', 'No - Skip Display Test']
    );
    
    if (hasExternal.includes('No')) {
      return {
        component: 'display',
        result: 'not-tested',
        resultReason: 'Display not functioning, no external monitor available',
        details: {
          displayBroken: true,
          externalTestAvailable: false
        },
        notes: 'Display test skipped - screen not working'
      };
    }
    
    // Guide external display test
    await this.displayInstructions({
      title: 'External Display Test',
      steps: [
        'Connect external monitor via HDMI/DisplayPort/USB-C',
        'Press Windows+P (or Fn+F4/F5 on some models)',
        'Select "Duplicate" or "Extend" display',
        'Run the color tests on external monitor'
      ]
    });
    
    await this.waitForUserInput('Press any key when external display is connected...', null);
    
    // Continue with modified test
    return await this.runExternalDisplayTest();
  }

  async displaySmartInstructions(hasTouchscreen, displayType) {
    const steps = [
      this.debugMode ? '** DEBUG MODE - Visual UI Only **' : null,
      `Testing ${displayType} display on ${this.modelProfile?.fullModel || 'laptop'}`,
      'Different colors will be shown on screen',
      'Look for:',
      '  • Dead pixels (dots that don\'t change color)',
      '  • Lines (horizontal or vertical)',
      '  • Dark patches or bright spots',
      '  • Backlight bleeding (light leaks at edges)'
    ].filter(Boolean);
    
    if (hasTouchscreen) {
      steps.push('', '✅ This model has touchscreen - we will test it after colors');
    }
    
    steps.push('', 'Use arrow keys to change colors');
    steps.push('Press SPACE to report issues');
    steps.push('Press ENTER when complete');
    
    await this.displayInstructions({
      title: 'Display Test Instructions',
      steps: steps
    });
    
    await this.waitForUserInput('Press any key to start...', null);
  }

  async runColorCycleTest() {
    return new Promise((resolve) => {
      let isTestComplete = false;
      
      // Show first color
      this.showTestColor(this.currentColorIndex);
      
      // Set up keyboard listener
      const handleKeyPress = (key) => {
        switch(key) {
          case 'ArrowRight':
          case 'ArrowDown':
            this.nextColor();
            break;
          case 'ArrowLeft':
          case 'ArrowUp':
            this.previousColor();
            break;
          case ' ':
          case 'Space':
            this.reportIssue();
            break;
          case 'Enter':
            isTestComplete = true;
            resolve();
            break;
        }
      };
      
      // Listen for keyboard events
      this.onKeyPress = handleKeyPress;
      
      // Show UI for reporting issues
      this.displayColorTestUI();
      
      // Wait for completion
      const checkInterval = setInterval(() => {
        if (isTestComplete) {
          clearInterval(checkInterval);
          this.onKeyPress = null;
        }
      }, 100);
    });
  }

  showTestColor(index) {
    const color = this.testColors[index];
    this.updateProgress(
      20 + (index * 15), 
      `Testing ${color.name} screen...`
    );
    
    // Emit event to show full screen color
    this.emit('displayFullScreenColor', {
      color: color.color,
      name: color.name,
      index: index,
      total: this.testColors.length
    });
  }

  displayColorTestUI() {
    this.emit('displayUI', {
      type: 'color-test',
      content: `
        <div style="position: fixed; bottom: 20px; left: 50%; transform: translateX(-50%); 
                    background: rgba(0,0,0,0.8); color: white; padding: 20px; 
                    border-radius: 10px; text-align: center;">
          <h3 id="current-color">Testing: White</h3>
          <p>Use ← → arrows to change colors</p>
          <p>Press SPACE to report an issue</p>
          <p>Press ENTER when complete</p>
          <div id="issue-buttons" style="margin-top: 15px; display: none;">
            <button onclick="reportDeadPixel()">Dead Pixel</button>
            <button onclick="reportLine()">Line</button>
            <button onclick="reportPatch()">Dark Patch</button>
            <button onclick="reportBacklight()">Backlight Issue</button>
          </div>
        </div>
      `
    });
  }

  nextColor() {
    if (this.currentColorIndex < this.testColors.length - 1) {
      this.currentColorIndex++;
      this.showTestColor(this.currentColorIndex);
    }
  }

  previousColor() {
    if (this.currentColorIndex > 0) {
      this.currentColorIndex--;
      this.showTestColor(this.currentColorIndex);
    }
  }

  async reportIssue() {
    const issueType = await this.getUserInput(
      'What type of issue do you see?',
      ['Dead Pixel', 'Line', 'Dark Patch', 'Backlight Issue', 'Cancel']
    );
    
    if (issueType === 'Cancel') return;
    
    switch(issueType) {
      case 'Dead Pixel':
        await this.reportDeadPixel();
        break;
      case 'Line':
        await this.reportLine();
        break;
      case 'Dark Patch':
        await this.reportPatch();
        break;
      case 'Backlight Issue':
        await this.reportBacklight();
        break;
    }
  }

  async reportDeadPixel() {
    const location = await this.getUserInput(
      'Where is the dead pixel located?',
      ['Top-Left', 'Top-Right', 'Center', 'Bottom-Left', 'Bottom-Right', 'Multiple']
    );
    
    this.issues.deadPixels.count++;
    this.issues.deadPixels.locations.push({
      color: this.testColors[this.currentColorIndex].name,
      location: location
    });
  }

  async reportLine() {
    const orientation = await this.getUserInput(
      'What type of line do you see?',
      ['Horizontal', 'Vertical', 'Both']
    );
    
    if (orientation === 'Horizontal' || orientation === 'Both') {
      this.issues.lines.horizontal = true;
    }
    if (orientation === 'Vertical' || orientation === 'Both') {
      this.issues.lines.vertical = true;
    }
    
    this.issues.lines.description = await this.getUserInput(
      'Describe the line (color, thickness, location):',
      null
    );
  }

  async reportPatch() {
    this.issues.patches.present = true;
    this.issues.patches.description = await this.getUserInput(
      'Describe the patch (size, location, darkness):',
      null
    );
  }

  async reportBacklight() {
    this.issues.backlight.uniform = false;
    this.issues.backlight.description = await this.getUserInput(
      'Describe the backlight issue:',
      null
    );
  }

  async getFinalAssessment() {
    // Brightness test
    const brightness = await this.getUserInput(
      'How is the overall screen brightness?',
      ['Excellent', 'Good', 'Acceptable', 'Dim', 'Very Dim']
    );
    
    // Overall condition
    const condition = await this.getUserInput(
      'Overall display condition:',
      ['Perfect', 'Very Good', 'Good', 'Acceptable', 'Poor', 'Failed']
    );
    
    // Map to our scale
    const conditionMap = {
      'Perfect': 'good',
      'Very Good': 'good',
      'Good': 'good',
      'Acceptable': 'acceptable',
      'Poor': 'poor',
      'Failed': 'failed'
    };
    
    this.issues.overall = conditionMap[condition] || 'acceptable';
    this.issues.brightness = brightness;
  }

  async testTouchscreen() {
    this.updateProgress(85, 'Testing touchscreen functionality...');
    
    await this.displayInstructions({
      title: 'Touchscreen Test',
      steps: [
        'Try the following touch gestures:',
        '1. Single tap in different screen areas',
        '2. Swipe from edges (for Windows gestures)',
        '3. Pinch to zoom (in a browser or image)',
        '4. Two-finger scroll',
        'Test all areas of the screen'
      ]
    });
    
    const touchResult = await this.getUserInput(
      'How is the touchscreen working?',
      [
        'Perfect - All gestures work',
        'Partial - Some areas not responding',
        'Not Working - No touch response',
        'Not Tested - Cannot verify'
      ]
    );
    
    this.issues.touchscreen.tested = true;
    this.issues.touchscreen.working = touchResult.includes('Perfect') ? 'perfect' :
                                      touchResult.includes('Partial') ? 'partial' :
                                      touchResult.includes('Not Working') ? 'failed' : 'unknown';
  }

  async runExternalDisplayTest() {
    // Modified test for external display
    await this.runColorCycleTest();
    
    return {
      component: 'display',
      result: 'partial',
      resultReason: 'Internal display broken, external display test performed',
      details: {
        internalDisplay: 'broken',
        externalDisplay: 'tested',
        issues: this.issues
      },
      notes: 'Internal display not working - tested via external monitor'
    };
  }

  calculateSmartResult() {
    let result = 'pass';
    let resultReason = null;
    
    // Handle broken display
    if (this.displayBroken) {
      return {
        component: 'display',
        result: 'fail',
        resultReason: 'Display not functioning',
        details: this.issues,
        notes: 'Display completely non-functional'
      };
    }
    
    // Critical failures
    if (this.issues.overall === 'failed' || this.issues.deadPixels.count > 5) {
      result = 'fail';
      resultReason = 'Display has critical defects';
    }
    // Multiple issues
    else if (this.issues.lines.horizontal && this.issues.lines.vertical) {
      result = 'fail';
      resultReason = 'Multiple line defects present';
    }
    // Moderate issues
    else if (this.issues.deadPixels.count > 3 || this.issues.overall === 'poor') {
      result = 'partial';
      resultReason = 'Display has noticeable defects';
    }
    // Minor issues
    else if (this.issues.deadPixels.count > 0 || this.issues.overall === 'acceptable') {
      result = 'partial';
      resultReason = 'Display has minor imperfections';
    }
    // Touchscreen issues (if applicable)
    else if (this.issues.touchscreen.tested && this.issues.touchscreen.working === 'failed') {
      result = 'partial';
      resultReason = 'Display works but touchscreen not functioning';
    }
    
    return {
      component: 'display',
      result: result,
      resultReason: resultReason,
      details: {
        modelHasTouchscreen: this.modelProfile?.specifications?.display?.touchscreen || false,
        resolution: this.modelProfile?.specifications?.display?.resolution || 'Unknown',
        displayType: this.modelProfile?.specifications?.display?.type || 'LCD',
        issues: this.issues
      },
      notes: this.generateSmartNotes()
    };
  }

  generateSmartNotes() {
    const notes = [];
    
    if (this.displayBroken) {
      notes.push('Internal display not working');
      return notes.join('. ');
    }
    
    // Dead pixels
    if (this.issues.deadPixels.count > 0) {
      notes.push(`${this.issues.deadPixels.count} dead pixel(s) found`);
    }
    
    // Lines
    if (this.issues.lines.horizontal || this.issues.lines.vertical) {
      const lineType = [];
      if (this.issues.lines.horizontal) lineType.push('horizontal');
      if (this.issues.lines.vertical) lineType.push('vertical');
      notes.push(`Display has ${lineType.join(' and ')} lines`);
    }
    
    // Other issues
    if (this.issues.patches.present) {
      notes.push('Dark patches or bright spots present');
    }
    
    if (!this.issues.backlight.uniform) {
      notes.push('Backlight bleeding detected');
    }
    
    // Touchscreen status (only if model has it)
    if (this.issues.touchscreen.tested) {
      if (this.issues.touchscreen.working === 'perfect') {
        notes.push('Touchscreen working perfectly');
      } else if (this.issues.touchscreen.working === 'partial') {
        notes.push('Touchscreen partially responsive');
      } else if (this.issues.touchscreen.working === 'failed') {
        notes.push('Touchscreen not working');
      }
    }
    
    notes.push(`Overall display condition: ${this.issues.overall}`);
    
    if (this.issues.brightness) {
      notes.push(`Brightness: ${this.issues.brightness}`);
    }
    
    return notes.join('. ');
  }

  async onCleanup() {
    // Clear any full screen colors
    this.emit('clearFullScreen');
    this.onKeyPress = null;
  }
}

module.exports = DisplayTest;