const TestModule = require('../../core/framework/TestModule');
const si = require('systeminformation');
const { exec } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);

/**
 * System Information Detection Module
 * Captures comprehensive hardware details and detects components
 * This is the foundation module that should run first in IQC
 */
class SystemInfoDetection extends TestModule {
  constructor() {
    super({
      id: 'system-info-detection',
      name: 'System Information Detection',
      description: 'Capture hardware details and detect all components',
      category: 'hardware',
      estimatedTime: 2,
      requiresUserInteraction: false
    });
    
    this.systemInfo = {};
    this.testRAMSerials = []; // Will be loaded from config/database
  }

  async onInitialize() {
    // Load test RAM serials from storage if available
    try {
      const testRAMConfig = await this.storage?.getTestRAMSerials?.();
      if (testRAMConfig) {
        this.testRAMSerials = testRAMConfig;
      }
    } catch (error) {
      console.log('No test RAM serials configured');
    }
  }

  async onRunTest(config) {
    this.updateProgress(0, 'Starting system information detection...');
    
    const results = {
      system: {},
      cpu: {},
      memory: {},
      storage: {},
      network: {},
      graphics: {},
      bios: {},
      components: []
    };
    
    try {
      // 1. System Information (15%)
      this.updateProgress(5, 'Detecting system information...');
      results.system = await this.getSystemInfo();
      
      // 2. CPU Information (25%)
      this.updateProgress(15, 'Detecting processor...');
      results.cpu = await this.getCPUInfo();
      
      // 3. Memory Information (40%)
      this.updateProgress(25, 'Detecting RAM modules...');
      results.memory = await this.getMemoryInfo();
      
      // 4. Storage Information (55%)
      this.updateProgress(40, 'Detecting storage devices...');
      results.storage = await this.getStorageInfo();
      
      // 5. Network Information (70%)
      this.updateProgress(55, 'Detecting network interfaces...');
      results.network = await this.getNetworkInfo();
      
      // 6. Graphics Information (80%)
      this.updateProgress(70, 'Detecting graphics...');
      results.graphics = await this.getGraphicsInfo();
      
      // 7. BIOS Information (90%)
      this.updateProgress(80, 'Reading BIOS information...');
      results.bios = await this.getBIOSInfo();
      
      // 8. Component Summary (100%)
      this.updateProgress(90, 'Generating component summary...');
      results.components = this.generateComponentSummary(results);
      
      // Check for duplicate serials/MACs
      await this.checkForDuplicates(results);
      
      // Store in system info for other modules to access
      this.systemInfo = results;
      
      // Prepare detected model info
      const detectedModel = {
        manufacturer: results.system.manufacturer,
        model: results.system.model,
        fullModel: `${results.system.manufacturer} ${results.system.model}`.trim(),
        serial: results.system.serial,
        sku: results.system.sku
      };
      
      // Try to match model with fuzzy matching
      this.updateProgress(95, 'Matching model profile...');
      const ModelMatcher = require('../../core/models/ModelMatcher');
      const matcher = new ModelMatcher();
      
      // Get available model profiles (this would come from storage/database)
      const availableModels = await this.getAvailableModels();
      
      // Find matches
      const matches = matcher.findBestMatches(detectedModel.fullModel, availableModels, 80);
      
      // Handle model selection
      let finalModel = detectedModel.fullModel;
      
      if (matches.length === 0) {
        // No matches found, trigger manual selection
        finalModel = await this.requestManualModelSelection([], detectedModel.fullModel);
        if (!finalModel) {
          return {
            component: 'system_info',
            result: 'partial',
            resultReason: 'Model selection cancelled',
            details: results,
            detectedModel: detectedModel,
            notes: 'Manual model selection was cancelled'
          };
        }
      } else if (matches.length === 1 && matches[0].score >= 90) {
        // Single high-confidence match, use it automatically
        finalModel = matches[0].model;
      } else {
        // Multiple matches or low confidence, show selection UI
        finalModel = await this.requestManualModelSelection(matches, detectedModel.fullModel);
        if (!finalModel) {
          // User cancelled, use best match
          finalModel = matches[0].model;
        }
      }
      
      // Update detected model with final selection
      detectedModel.selectedModel = finalModel;
      detectedModel.isManuallySelected = finalModel !== detectedModel.fullModel;
      
      this.updateProgress(100, 'System detection completed');
      
      return {
        component: 'system_info',
        result: 'pass',
        details: results,
        detectedModel: detectedModel,
        notes: this.generateNotes(results)
      };
      
    } catch (error) {
      console.error('System detection error:', error);
      return {
        component: 'system_info',
        result: 'fail',
        error: error.message,
        details: results
      };
    }
  }

  async getSystemInfo() {
    const system = await si.system();
    const baseboard = await si.baseboard();
    const chassis = await si.chassis();
    
    return {
      manufacturer: system.manufacturer || 'Unknown',
      model: system.model || 'Unknown',
      version: system.version || '',
      serial: system.serial || 'Unknown',
      uuid: system.uuid || '',
      sku: system.sku || '',
      baseboard: {
        manufacturer: baseboard.manufacturer || '',
        model: baseboard.model || '',
        serial: baseboard.serial || '',
        version: baseboard.version || ''
      },
      chassis: {
        type: chassis.type || '',
        serial: chassis.serial || ''
      }
    };
  }

  async getCPUInfo() {
    const cpu = await si.cpu();
    const cpuCurrentSpeed = await si.cpuCurrentSpeed();
    
    // Detect processor tier (i3/i5/i7/i9)
    let tier = 'Unknown';
    if (cpu.brand) {
      if (cpu.brand.includes('i9')) tier = 'i9';
      else if (cpu.brand.includes('i7')) tier = 'i7';
      else if (cpu.brand.includes('i5')) tier = 'i5';
      else if (cpu.brand.includes('i3')) tier = 'i3';
      else if (cpu.brand.includes('Celeron')) tier = 'Celeron';
      else if (cpu.brand.includes('Pentium')) tier = 'Pentium';
      else if (cpu.brand.includes('AMD')) tier = 'AMD';
    }
    
    return {
      manufacturer: cpu.manufacturer || 'Unknown',
      brand: cpu.brand || 'Unknown',
      tier: tier,
      speed: cpu.speed || 0,
      speedMin: cpu.speedMin || 0,
      speedMax: cpu.speedMax || 0,
      cores: cpu.cores || 0,
      physicalCores: cpu.physicalCores || 0,
      processors: cpu.processors || 1,
      currentSpeed: cpuCurrentSpeed.avg || 0
    };
  }

  async getMemoryInfo() {
    const mem = await si.mem();
    const memLayout = await si.memLayout();
    
    const modules = [];
    let hasTestRAM = false;
    let totalCapacity = 0;
    
    for (const module of memLayout) {
      if (module.size > 0) {
        totalCapacity += module.size;
        
        // Check if this is test RAM
        const isTestRAM = this.testRAMSerials.includes(module.serialNum);
        if (isTestRAM) hasTestRAM = true;
        
        modules.push({
          bank: module.bank || 'Unknown',
          type: module.type || 'Unknown',
          size: module.size,
          sizeGB: Math.round(module.size / (1024 * 1024 * 1024)),
          speed: module.clockSpeed || 0,
          manufacturer: module.manufacturer || 'Unknown',
          partNum: module.partNum || '',
          serialNum: module.serialNum || 'Unknown',
          isTestRAM: isTestRAM,
          formFactor: module.formFactor || ''
        });
      }
    }
    
    return {
      total: mem.total,
      totalGB: Math.round(mem.total / (1024 * 1024 * 1024)),
      available: mem.available,
      used: mem.used,
      modules: modules,
      slotCount: memLayout.length,
      usedSlots: modules.length,
      hasTestRAM: hasTestRAM,
      totalCapacity: totalCapacity
    };
  }

  async getStorageInfo() {
    const diskLayout = await si.diskLayout();
    const blockDevices = await si.blockDevices();
    
    const drives = [];
    
    for (const disk of diskLayout) {
      // Skip removable drives
      if (disk.type === 'HD' || disk.type === 'SSD' || disk.type === 'NVMe') {
        const blockDevice = blockDevices.find(b => b.name === disk.device);
        
        drives.push({
          device: disk.device || 'Unknown',
          type: disk.type || 'Unknown',
          name: disk.name || 'Unknown',
          vendor: disk.vendor || 'Unknown',
          size: disk.size,
          sizeGB: Math.round(disk.size / (1024 * 1024 * 1024)),
          serial: disk.serialNum || 'Unknown',
          interfaceType: disk.interfaceType || 'Unknown',
          smartStatus: disk.smartStatus || 'Unknown',
          temperature: disk.temperature || null,
          model: blockDevice?.model || disk.name || '',
          physical: blockDevice?.physical || ''
        });
      }
    }
    
    return {
      drives: drives,
      count: drives.length,
      totalSize: drives.reduce((sum, d) => sum + d.size, 0),
      hasSSD: drives.some(d => d.type === 'SSD' || d.type === 'NVMe'),
      hasHDD: drives.some(d => d.type === 'HD')
    };
  }

  async getNetworkInfo() {
    const networkInterfaces = await si.networkInterfaces();
    const networkConnections = await si.networkConnections();
    
    const interfaces = [];
    let primaryMAC = '';
    
    for (const iface of networkInterfaces) {
      // Skip virtual interfaces
      if (!iface.virtual && iface.mac && iface.mac !== '00:00:00:00:00:00') {
        interfaces.push({
          name: iface.iface,
          mac: iface.mac,
          type: iface.type,
          speed: iface.speed,
          operstate: iface.operstate,
          dhcp: iface.dhcp,
          ipv4: iface.ip4,
          manufacturer: iface.manufacturer || 'Unknown'
        });
        
        // Set primary MAC (usually the first ethernet interface)
        if (!primaryMAC && (iface.type === 'wired' || iface.iface.includes('eth'))) {
          primaryMAC = iface.mac;
        }
      }
    }
    
    // If no wired MAC found, use the first available
    if (!primaryMAC && interfaces.length > 0) {
      primaryMAC = interfaces[0].mac;
    }
    
    return {
      interfaces: interfaces,
      primaryMAC: primaryMAC,
      activeConnections: networkConnections.length,
      hasEthernet: interfaces.some(i => i.type === 'wired'),
      hasWiFi: interfaces.some(i => i.type === 'wireless')
    };
  }

  async getGraphicsInfo() {
    const graphics = await si.graphics();
    
    const controllers = [];
    const displays = [];
    
    // Graphics controllers
    for (const controller of graphics.controllers) {
      controllers.push({
        vendor: controller.vendor || 'Unknown',
        model: controller.model || 'Unknown',
        bus: controller.bus || '',
        vram: controller.vram || 0,
        vramDynamic: controller.vramDynamic || false,
        subDeviceId: controller.subDeviceId || ''
      });
    }
    
    // Displays
    for (const display of graphics.displays) {
      if (display.connection && display.connection !== 'disconnected') {
        displays.push({
          vendor: display.vendor || '',
          model: display.model || '',
          main: display.main || false,
          builtin: display.builtin || false,
          connection: display.connection || '',
          sizeX: display.sizeX || 0,
          sizeY: display.sizeY || 0,
          pixelDepth: display.pixelDepth || 0,
          resolutionX: display.resolutionX || 0,
          resolutionY: display.resolutionY || 0,
          currentResolutionX: display.currentResolutionX || 0,
          currentResolutionY: display.currentResolutionY || 0
        });
      }
    }
    
    return {
      controllers: controllers,
      displays: displays,
      hasDiscreteGPU: controllers.length > 1 || controllers.some(c => 
        !c.model.toLowerCase().includes('intel') && 
        !c.model.toLowerCase().includes('amd radeon(tm) graphics')
      ),
      hasDisplay: displays.length > 0
    };
  }

  async getBIOSInfo() {
    const bios = await si.bios();
    
    return {
      vendor: bios.vendor || 'Unknown',
      version: bios.version || 'Unknown',
      releaseDate: bios.releaseDate || '',
      revision: bios.revision || '',
      serial: bios.serial || '',
      features: bios.features || []
    };
  }

  generateComponentSummary(results) {
    const components = [];
    
    // System
    components.push({
      type: 'System',
      description: `${results.system.manufacturer} ${results.system.model}`,
      serial: results.system.serial
    });
    
    // CPU
    components.push({
      type: 'Processor',
      description: `${results.cpu.brand} (${results.cpu.tier})`,
      details: `${results.cpu.cores} cores @ ${results.cpu.speed}GHz`
    });
    
    // Memory
    const memSummary = results.memory.modules.map(m => 
      `${m.sizeGB}GB ${m.type} @ ${m.speed}MHz${m.isTestRAM ? ' (TEST RAM)' : ''}`
    ).join(', ');
    components.push({
      type: 'Memory',
      description: `${results.memory.totalGB}GB Total (${results.memory.usedSlots}/${results.memory.slotCount} slots)`,
      details: memSummary
    });
    
    // Storage
    if (results.storage.drives.length > 0) {
      const storageSummary = results.storage.drives.map(d => 
        `${d.sizeGB}GB ${d.type} (${d.vendor})`
      ).join(', ');
      components.push({
        type: 'Storage',
        description: storageSummary,
        details: results.storage.drives.map(d => d.serial).join(', ')
      });
    } else {
      components.push({
        type: 'Storage',
        description: 'No drives detected',
        warning: true
      });
    }
    
    // Network
    components.push({
      type: 'Network',
      description: `MAC: ${results.network.primaryMAC}`,
      details: `${results.network.interfaces.length} interfaces`
    });
    
    // Graphics
    if (results.graphics.controllers.length > 0) {
      const gpuSummary = results.graphics.controllers.map(g => g.model).join(', ');
      components.push({
        type: 'Graphics',
        description: gpuSummary,
        hasDiscrete: results.graphics.hasDiscreteGPU
      });
    }
    
    return components;
  }

  async checkForDuplicates(results) {
    // In a real implementation, this would check against the database
    // For now, we'll emit an event if duplicate detection is needed
    
    if (results.system.serial && results.system.serial !== 'Unknown') {
      this.emit('checkDuplicate', {
        type: 'serial',
        value: results.system.serial,
        component: 'system'
      });
    }
    
    if (results.network.primaryMAC) {
      this.emit('checkDuplicate', {
        type: 'mac',
        value: results.network.primaryMAC,
        component: 'network'
      });
    }
    
    // Check for duplicate RAM/SSD serials
    for (const module of results.memory.modules) {
      if (module.serialNum && module.serialNum !== 'Unknown' && !module.isTestRAM) {
        this.emit('checkDuplicate', {
          type: 'ram_serial',
          value: module.serialNum,
          component: 'memory'
        });
      }
    }
    
    for (const drive of results.storage.drives) {
      if (drive.serial && drive.serial !== 'Unknown') {
        this.emit('checkDuplicate', {
          type: 'ssd_serial',
          value: drive.serial,
          component: 'storage'
        });
      }
    }
  }

  generateNotes(results) {
    const notes = [];
    
    // Processor info
    notes.push(`CPU: ${results.cpu.tier} - ${results.cpu.brand}`);
    
    // Memory status
    if (results.memory.hasTestRAM) {
      notes.push('Test RAM detected - original RAM may be missing');
    }
    notes.push(`RAM: ${results.memory.totalGB}GB in ${results.memory.usedSlots} slot(s)`);
    
    // Storage status
    if (results.storage.count === 0) {
      notes.push('WARNING: No storage drives detected');
    } else {
      notes.push(`Storage: ${results.storage.drives.map(d => `${d.sizeGB}GB ${d.type}`).join(', ')}`);
    }
    
    // Special features
    if (results.graphics.hasDiscreteGPU) {
      notes.push('Has discrete GPU');
    }
    
    // BIOS info
    notes.push(`BIOS: ${results.bios.vendor} v${results.bios.version}`);
    
    return notes.join('. ');
  }

  // Method to get system info for other modules
  getSystemInfoForModules() {
    return this.systemInfo;
  }
  
  // Get available model profiles from storage/database
  async getAvailableModels() {
    // TODO: In production, this would query the database for available model profiles
    // For now, return a sample list
    return [
      'HP EliteBook 840 G5',
      'HP EliteBook 840 G6',
      'HP EliteBook 840 G7',
      'HP EliteBook 850 G5',
      'Dell Latitude 5410',
      'Dell Latitude 5420',
      'Dell Latitude 7400',
      'Dell Latitude 7410',
      'Dell Latitude 7420',
      'Lenovo ThinkPad T480',
      'Lenovo ThinkPad T490',
      'Lenovo ThinkPad T14',
      'Lenovo ThinkPad X280',
      'Lenovo ThinkPad X390'
    ];
  }
  
  // Request manual model selection from user
  async requestManualModelSelection(matches, detectedModel) {
    return new Promise((resolve) => {
      this.emit('displayUI', {
        type: 'model-selection',
        matches: matches,
        detectedModel: detectedModel,
        callback: (selectedModel) => {
          resolve(selectedModel);
        }
      });
    });
  }

  async onCleanup() {
    // Nothing to clean up
  }
}

module.exports = SystemInfoDetection;