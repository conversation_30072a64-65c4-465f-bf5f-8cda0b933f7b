const TestModule = require('../../core/framework/TestModule');

/**
 * Physical Inspection Module
 * Documents physical condition of laptop parts (A, B, C, D)
 * No driver dependencies - purely visual inspection with user input
 */
class PhysicalInspection extends TestModule {
  constructor() {
    super({
      id: 'physical-inspection',
      name: 'Physical Inspection',
      description: 'Document physical condition of laptop parts',
      category: 'inspection',
      estimatedTime: 3,
      requiresUserInteraction: true
    });
    
    this.inspectionResults = {
      partA: null,
      partB: null,
      partC: null,
      partD: null,
      keyboard: {},
      trackpad: {},
      rubberFeet: null,
      stickers: null,
      hinges: null,
      ports: null,
      overallCondition: null,
      notes: ''
    };
  }

  async onInitialize() {
    // No initialization needed
  }

  async onRunTest(config) {
    this.debugMode = config.debugMode || false;
    this.updateProgress(0, 'Starting physical inspection...');
    
    // Show part nomenclature guide
    await this.showPartGuide();
    
    // In debug mode, just show UI and return
    if (this.debugMode) {
      this.updateProgress(50, 'Debug mode - showing UI only');
      return {
        component: 'physical_inspection',
        result: 'debug',
        details: { debugMode: true },
        notes: 'Debug mode - visual UI displayed'
      };
    }
    
    // Inspect each part
    await this.inspectPartA(); // 20%
    await this.inspectPartB(); // 40%
    await this.inspectPartC(); // 60%
    await this.inspectPartD(); // 80%
    await this.inspectAdditionalComponents(); // 90%
    
    // Get overall assessment
    await this.getOverallAssessment(); // 100%
    
    const result = this.calculateOverallResult();
    
    return {
      component: 'physical_inspection',
      result: result,
      details: this.inspectionResults,
      notes: this.generateInspectionReport()
    };
  }

  async showPartGuide() {
    // Display visual laptop diagram with parts clearly labeled
    this.emit('displayUI', {
      type: 'physical-inspection-guide',
      content: `
        <div style="padding: 20px; font-family: Arial, sans-serif; background: #f5f5f5; border-radius: 10px;">
          <h2 style="text-align: center; margin-bottom: 30px;">
            Laptop Part Identification Guide
            ${this.debugMode ? '<br><span style="color: #ff9800; font-size: 16px;">** DEBUG MODE - Visual UI Only **</span>' : ''}
          </h2>
          
          <!-- Visual laptop diagram -->
          <div style="position: relative; width: 800px; margin: 0 auto;">
            <!-- Open laptop view -->
            <div style="display: flex; justify-content: center; margin-bottom: 40px;">
              <!-- Screen section -->
              <div style="position: relative;">
                <!-- A-Part (Back of screen) -->
                <div style="width: 300px; height: 200px; background: #2196F3; border: 3px solid #1976D2; border-radius: 10px 10px 0 0; position: relative;">
                  <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center; color: white; font-weight: bold;">
                    A-PART<br>
                    <span style="font-size: 12px;">LCD Back Cover</span>
                  </div>
                </div>
                <!-- B-Part (Bezel) -->
                <div style="position: absolute; top: 10px; left: 10px; right: 10px; bottom: 10px; border: 15px solid #4CAF50; border-radius: 5px; pointer-events: none;">
                  <div style="position: absolute; top: -25px; left: 50%; transform: translateX(-50%); background: white; padding: 2px 10px; color: #4CAF50; font-weight: bold; font-size: 12px;">
                    B-PART (Bezel)
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Base section -->
            <div style="display: flex; justify-content: center;">
              <!-- C-Part (Keyboard area) -->
              <div style="width: 300px; height: 180px; background: #FF9800; border: 3px solid #F57C00; border-radius: 0 0 10px 10px; position: relative;">
                <div style="position: absolute; top: 20px; left: 50%; transform: translateX(-50%); text-align: center; color: white; font-weight: bold;">
                  C-PART<br>
                  <span style="font-size: 12px;">Keyboard/Palmrest</span>
                </div>
                <!-- Keyboard representation -->
                <div style="position: absolute; top: 60px; left: 20px; right: 20px; height: 60px; background: rgba(255,255,255,0.3); border-radius: 5px;"></div>
                <!-- Trackpad representation -->
                <div style="position: absolute; bottom: 20px; left: 50%; transform: translateX(-50%); width: 80px; height: 50px; background: rgba(255,255,255,0.3); border-radius: 5px;"></div>
              </div>
            </div>
            
            <!-- Bottom view -->
            <div style="margin-top: 40px; display: flex; justify-content: center;">
              <div style="width: 300px; height: 200px; background: #9C27B0; border: 3px solid #7B1FA2; border-radius: 10px; position: relative;">
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center; color: white; font-weight: bold;">
                  D-PART<br>
                  <span style="font-size: 12px;">Bottom Base</span>
                </div>
                <!-- Rubber feet indicators -->
                <div style="position: absolute; top: 20px; left: 20px; width: 20px; height: 20px; background: rgba(0,0,0,0.5); border-radius: 50%;"></div>
                <div style="position: absolute; top: 20px; right: 20px; width: 20px; height: 20px; background: rgba(0,0,0,0.5); border-radius: 50%;"></div>
                <div style="position: absolute; bottom: 20px; left: 20px; width: 20px; height: 20px; background: rgba(0,0,0,0.5); border-radius: 50%;"></div>
                <div style="position: absolute; bottom: 20px; right: 20px; width: 20px; height: 20px; background: rgba(0,0,0,0.5); border-radius: 50%;"></div>
              </div>
            </div>
          </div>
          
          <!-- Condition guide -->
          <div style="margin-top: 40px; padding: 20px; background: white; border-radius: 10px;">
            <h3>Condition Assessment Guide:</h3>
            <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; margin-top: 15px;">
              <div style="padding: 10px; border: 2px solid #4CAF50; border-radius: 5px; text-align: center;">
                <strong style="color: #4CAF50;">Perfect</strong><br>
                <small>No visible marks</small>
              </div>
              <div style="padding: 10px; border: 2px solid #FF9800; border-radius: 5px; text-align: center;">
                <strong style="color: #FF9800;">Paint Required</strong><br>
                <small>Minor scratches/wear</small>
              </div>
              <div style="padding: 10px; border: 2px solid #f44336; border-radius: 5px; text-align: center;">
                <strong style="color: #f44336;">Damaged</strong><br>
                <small>Dents/cracks</small>
              </div>
              <div style="padding: 10px; border: 2px solid #9E9E9E; border-radius: 5px; text-align: center;">
                <strong style="color: #9E9E9E;">Replace</strong><br>
                <small>Beyond repair</small>
              </div>
            </div>
          </div>
          
          <div style="text-align: center; margin-top: 30px;">
            <button onclick="window.startPhysicalInspection()" style="padding: 12px 40px; background: #2196F3; color: white; border: none; border-radius: 5px; font-size: 16px; cursor: pointer;">
              Start Inspection
            </button>
          </div>
        </div>
        
        <script>
          window.startPhysicalInspection = function() {
            window.electronAPI.modules.sendEvent('physical-inspection', 'ready', true);
          }
        </script>
      `
    });
    
    // Wait for user to be ready
    await new Promise((resolve) => {
      this.once('ready', resolve);
    });
  }

  async inspectPartA() {
    this.updateProgress(10, 'Inspecting A-Part (LCD Back Cover)...');
    
    const condition = await this.getUserInput(
      'A-Part (LCD Back Cover) Condition:',
      ['Perfect', 'Paint Required', 'Damaged', 'Needs Replacement']
    );
    
    this.inspectionResults.partA = condition;
    
    if (condition === 'Damaged' || condition === 'Paint Required') {
      const details = await this.getUserInput(
        'What type of damage?',
        ['Minor Scratches', 'Deep Scratches', 'Dents', 'Cracks', 'Discoloration', 'Sticker Residue']
      );
      this.inspectionResults.partADetails = details;
    }
    
    this.updateProgress(20, 'A-Part inspection complete');
  }

  async inspectPartB() {
    this.updateProgress(25, 'Inspecting B-Part (LCD Bezel)...');
    
    const condition = await this.getUserInput(
      'B-Part (LCD Bezel) Condition - Note: Cannot be painted',
      ['OK', 'Damaged', 'Needs Replacement']
    );
    
    this.inspectionResults.partB = condition;
    
    if (condition === 'Damaged') {
      const details = await this.getUserInput(
        'What type of damage?',
        ['Cracks', 'Broken Clips', 'Missing Pieces', 'Warped', 'Loose']
      );
      this.inspectionResults.partBDetails = details;
    }
    
    this.updateProgress(40, 'B-Part inspection complete');
  }

  async inspectPartC() {
    this.updateProgress(45, 'Inspecting C-Part (Keyboard/Palmrest Area)...');
    
    const condition = await this.getUserInput(
      'C-Part (Keyboard/Palmrest) Condition:',
      ['Perfect', 'Paint Required', 'Damaged', 'Needs Replacement']
    );
    
    this.inspectionResults.partC = condition;
    
    // Check keyboard specific issues
    const hasMissingKeys = await this.getUserInput(
      'Are there any missing keys?',
      ['No', 'Yes - 1 key', 'Yes - 2-3 keys', 'Yes - 4+ keys']
    );
    
    if (hasMissingKeys !== 'No') {
      const missingKeyDetails = await this.getUserInput(
        'Which keys are missing? (Enter key names)',
        null
      );
      this.inspectionResults.keyboard.missingKeys = missingKeyDetails;
      this.inspectionResults.keyboard.missingKeyCount = hasMissingKeys;
    }
    
    // Check for broken locks
    const hasBrokenLocks = await this.getUserInput(
      'Are there any broken keyboard locks/latches?',
      ['No', 'Yes - 1 lock', 'Yes - 2 locks', 'Yes - All locks']
    );
    
    this.inspectionResults.keyboard.brokenLocks = hasBrokenLocks;
    
    // Check trackpad
    const trackpadCondition = await this.getUserInput(
      'Trackpad physical condition:',
      ['Perfect', 'Scratched', 'Cracked', 'Loose/Wobbly', 'Buttons Damaged']
    );
    
    this.inspectionResults.trackpad.condition = trackpadCondition;
    
    this.updateProgress(60, 'C-Part inspection complete');
  }

  async inspectPartD() {
    this.updateProgress(65, 'Inspecting D-Part (Bottom Base)...');
    
    const condition = await this.getUserInput(
      'D-Part (Bottom Base) Condition:',
      ['Perfect', 'Paint Required', 'Damaged', 'Needs Replacement']
    );
    
    this.inspectionResults.partD = condition;
    
    // Check rubber feet
    const rubberFeetCondition = await this.getUserInput(
      'Rubber feet condition:',
      ['All OK', 'Some Missing', 'All Missing', 'Damaged/Worn']
    );
    
    this.inspectionResults.rubberFeet = rubberFeetCondition;
    
    // Check for missing screws
    const missingScrews = await this.getUserInput(
      'Are there any missing screws?',
      ['No', 'Yes - 1-2 screws', 'Yes - 3-5 screws', 'Yes - Many screws']
    );
    
    this.inspectionResults.missingScrews = missingScrews;
    
    this.updateProgress(80, 'D-Part inspection complete');
  }

  async inspectAdditionalComponents() {
    this.updateProgress(85, 'Inspecting additional components...');
    
    // Check hinges
    const hingeCondition = await this.getUserInput(
      'Screen hinge condition:',
      ['Tight and Good', 'Slightly Loose', 'Very Loose', 'Broken/Damaged']
    );
    
    this.inspectionResults.hinges = hingeCondition;
    
    // Check ports
    const portCondition = await this.getUserInput(
      'Physical condition of ports (USB, HDMI, etc):',
      ['All Good', 'Some Damaged', 'Some Blocked/Dirty', 'Major Damage']
    );
    
    this.inspectionResults.ports = portCondition;
    
    // Check for stickers
    const stickerCondition = await this.getUserInput(
      'Are there any stickers that need removal?',
      ['No Stickers', 'Easy to Remove', 'Difficult to Remove', 'Many Stickers']
    );
    
    this.inspectionResults.stickers = stickerCondition;
    
    // Any other visible damage
    const otherDamage = await this.getUserInput(
      'Any other visible damage or issues?',
      ['No', 'Yes - Minor', 'Yes - Major']
    );
    
    if (otherDamage !== 'No') {
      const notes = await this.getUserInput(
        'Please describe the additional damage:',
        null
      );
      this.inspectionResults.additionalNotes = notes;
    }
    
    this.updateProgress(90, 'Additional inspection complete');
  }

  async getOverallAssessment() {
    this.updateProgress(95, 'Getting overall assessment...');
    
    // Show summary of findings
    const summary = this.generateQuickSummary();
    
    await this.displayInstructions({
      title: 'Inspection Summary',
      content: `
        <div style="padding: 20px; font-family: Arial, sans-serif;">
          <h3>Parts Condition Summary:</h3>
          <div style="background: #f5f5f5; padding: 15px; border-radius: 8px; margin: 10px 0;">
            ${summary}
          </div>
        </div>
      `
    });
    
    const overallCondition = await this.getUserInput(
      'Overall laptop condition rating:',
      ['A - Excellent (minimal refurb needed)',
       'B - Good (standard refurb)',
       'C - Fair (extensive refurb)',
       'D - Poor (major parts replacement)']
    );
    
    this.inspectionResults.overallCondition = overallCondition;
    
    // Get any final notes
    const finalNotes = await this.getUserInput(
      'Any additional notes for refurbishment team? (Enter to skip)',
      null
    );
    
    if (finalNotes) {
      this.inspectionResults.notes = finalNotes;
    }
    
    this.updateProgress(100, 'Physical inspection completed');
  }

  generateQuickSummary() {
    const parts = ['partA', 'partB', 'partC', 'partD'];
    const summary = parts.map(part => {
      const condition = this.inspectionResults[part];
      const color = this.getConditionColor(condition);
      const label = part.replace('part', 'Part ');
      return `<div><strong>${label}:</strong> <span style="color: ${color}">${condition || 'Not inspected'}</span></div>`;
    }).join('');
    
    const issues = [];
    if (this.inspectionResults.keyboard.missingKeys) issues.push('Missing keys');
    if (this.inspectionResults.keyboard.brokenLocks !== 'No') issues.push('Broken keyboard locks');
    if (this.inspectionResults.rubberFeet !== 'All OK') issues.push('Rubber feet issues');
    if (this.inspectionResults.hinges !== 'Tight and Good') issues.push('Hinge issues');
    
    const issuesList = issues.length > 0 
      ? `<div style="margin-top: 10px;"><strong>Issues found:</strong> ${issues.join(', ')}</div>`
      : '';
    
    return summary + issuesList;
  }

  getConditionColor(condition) {
    if (!condition) return '#999';
    if (condition === 'Perfect' || condition === 'OK') return '#4CAF50';
    if (condition === 'Paint Required') return '#FF9800';
    if (condition === 'Damaged') return '#f44336';
    if (condition === 'Needs Replacement') return '#d32f2f';
    return '#666';
  }

  calculateOverallResult() {
    // Count serious issues
    let majorIssues = 0;
    let minorIssues = 0;
    
    // Check each part
    const parts = ['partA', 'partB', 'partC', 'partD'];
    for (const part of parts) {
      const condition = this.inspectionResults[part];
      if (condition === 'Needs Replacement') majorIssues++;
      else if (condition === 'Damaged') minorIssues++;
      else if (condition === 'Paint Required') minorIssues++;
    }
    
    // Check other issues
    if (this.inspectionResults.keyboard.brokenLocks !== 'No') majorIssues++;
    if (this.inspectionResults.hinges === 'Broken/Damaged') majorIssues++;
    if (this.inspectionResults.trackpad.condition === 'Cracked') majorIssues++;
    
    // Determine result
    if (majorIssues >= 2) return 'fail';
    if (majorIssues === 1 || minorIssues >= 3) return 'partial';
    return 'pass';
  }

  generateInspectionReport() {
    const report = [];
    
    // Part conditions
    report.push(`A-Part: ${this.inspectionResults.partA}`);
    report.push(`B-Part: ${this.inspectionResults.partB}`);
    report.push(`C-Part: ${this.inspectionResults.partC}`);
    report.push(`D-Part: ${this.inspectionResults.partD}`);
    
    // Keyboard issues
    if (this.inspectionResults.keyboard.missingKeys) {
      report.push(`Missing keys: ${this.inspectionResults.keyboard.missingKeys}`);
    }
    if (this.inspectionResults.keyboard.brokenLocks !== 'No') {
      report.push(`Keyboard locks: ${this.inspectionResults.keyboard.brokenLocks}`);
    }
    
    // Other components
    report.push(`Trackpad: ${this.inspectionResults.trackpad.condition}`);
    report.push(`Hinges: ${this.inspectionResults.hinges}`);
    report.push(`Rubber feet: ${this.inspectionResults.rubberFeet}`);
    
    // Overall rating
    report.push(`Overall: ${this.inspectionResults.overallCondition}`);
    
    return report.join('. ');
  }

  async onCleanup() {
    this.inspectionResults = {};
    this.emit('clearUI');
  }
}

module.exports = PhysicalInspection;