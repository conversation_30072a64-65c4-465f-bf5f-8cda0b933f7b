const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const OfflineStorage = require('./core/storage/OfflineStorage');
const PINAuth = require('./core/auth/PINAuth');
const SyncEngine = require('./core/sync/SyncEngine');
const ModuleManager = require('./core/framework/ModuleManager');
const config = require('./config');

// Global references
let mainWindow;
let offlineStorage;
let authManager;
let syncEngine;
let moduleManager;

/**
 * Create the main window
 */
function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1024,
    height: 768,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    },
    title: 'Laptop QC Agent v2.0',
    icon: path.join(__dirname, '../assets/icon.png')
  });

  mainWindow.loadFile('index.html');

  // Open DevTools in development
  if (process.argv.includes('--dev')) {
    mainWindow.webContents.openDevTools();
  }

  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

/**
 * Initialize core services
 */
async function initializeServices() {
  try {
    // Initialize offline storage
    offlineStorage = new OfflineStorage();
    await offlineStorage.initialize();
    console.log('Offline storage initialized');

    // Initialize authentication
    authManager = new PINAuth(offlineStorage);
    await authManager.initializeFromStorage();
    console.log('Authentication manager initialized');

    // Initialize sync engine
    syncEngine = new SyncEngine(offlineStorage);
    if (config.SUPABASE_URL && config.SUPABASE_SERVICE_KEY) {
      await syncEngine.initialize(config.SUPABASE_URL, config.SUPABASE_SERVICE_KEY);
      console.log('Sync engine initialized');
    } else {
      console.log('Sync engine not initialized - no Supabase credentials');
    }

    // Initialize module manager with storage
    moduleManager = new ModuleManager(offlineStorage);
    await moduleManager.loadModules();
    console.log('Module manager initialized');

    // Set up event handlers
    setupEventHandlers();

  } catch (error) {
    console.error('Failed to initialize services:', error);
    throw error;
  }
}

/**
 * Set up event handlers
 */
function setupEventHandlers() {
  // Auth events
  authManager.on('authenticated', (data) => {
    if (mainWindow) {
      mainWindow.webContents.send('auth-status', { authenticated: true, ...data });
    }
  });

  authManager.on('sessionEnded', (data) => {
    if (mainWindow) {
      mainWindow.webContents.send('auth-status', { authenticated: false, ...data });
    }
  });

  // Sync events
  syncEngine.on('syncStarted', () => {
    if (mainWindow) {
      mainWindow.webContents.send('sync-status', { syncing: true });
    }
  });

  syncEngine.on('syncCompleted', (data) => {
    if (mainWindow) {
      mainWindow.webContents.send('sync-status', { syncing: false, ...data });
    }
  });

  syncEngine.on('connectionChanged', (data) => {
    if (mainWindow) {
      mainWindow.webContents.send('connection-status', data);
    }
  });

  // Module events
  moduleManager.on('moduleProgress', (data) => {
    if (mainWindow) {
      mainWindow.webContents.send('module-progress', data);
    }
  });

  moduleManager.on('userInputRequired', (data) => {
    if (mainWindow) {
      mainWindow.webContents.send('user-input-required', data);
    }
  });

  moduleManager.on('displayInstructions', (data) => {
    if (mainWindow) {
      mainWindow.webContents.send('display-instructions', data);
    }
  });

  moduleManager.on('displayUI', (data) => {
    if (mainWindow) {
      mainWindow.webContents.send('display-ui', data);
    }
  });
  
  // Model profile events
  moduleManager.modelProfile.on('manualSelectionRequired', (data) => {
    if (mainWindow) {
      mainWindow.webContents.send('model-selection-required', data);
    }
  });
}

// App event handlers
app.whenReady().then(async () => {
  await initializeServices();
  createWindow();
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (mainWindow === null) {
    createWindow();
  }
});

app.on('before-quit', async (event) => {
  event.preventDefault();
  
  try {
    // Shutdown services
    if (syncEngine) {
      await syncEngine.shutdown();
    }
    
    if (authManager) {
      await authManager.endSession();
    }
    
    if (moduleManager) {
      await moduleManager.stopAllModules();
    }
    
    if (offlineStorage) {
      offlineStorage.close();
    }
  } catch (error) {
    console.error('Error during shutdown:', error);
  }
  
  app.exit();
});

// IPC Handlers

// Authentication
ipcMain.handle('auth:login', async (event, pin) => {
  return await authManager.authenticate(pin);
});

ipcMain.handle('auth:logout', async () => {
  await authManager.endSession();
  return { success: true };
});

ipcMain.handle('auth:get-session', async () => {
  return authManager.getSessionDisplay();
});

ipcMain.handle('auth:has-permission', async (event, action) => {
  return authManager.hasPermission(action);
});

// System info
ipcMain.handle('system:get-info', async () => {
  const si = require('systeminformation');
  const system = await si.system();
  const cpu = await si.cpu();
  const mem = await si.mem();
  const networkInterfaces = await si.networkInterfaces();
  
  return {
    manufacturer: system.manufacturer,
    model: system.model,
    serial: system.serial,
    cpu: `${cpu.manufacturer} ${cpu.brand}`,
    ram: Math.round(mem.total / (1024 * 1024 * 1024)) + ' GB',
    network: networkInterfaces[0]?.mac || 'Unknown'
  };
});

// Module management
ipcMain.handle('modules:get-available', async () => {
  return moduleManager.getAvailableModules();
});

ipcMain.handle('modules:get-by-category', async (event, category) => {
  return moduleManager.getModulesByCategory(category);
});

// Model profile management
ipcMain.handle('models:detect', async () => {
  return await moduleManager.modelProfile.detectModel();
});

ipcMain.handle('models:load-profile', async (event, model) => {
  return await moduleManager.modelProfile.loadProfile(model);
});

ipcMain.handle('models:get-current', async () => {
  return moduleManager.modelProfile.currentProfile;
});

ipcMain.handle('models:selection-response', async (event, selectedModel) => {
  // Handle manual model selection response
  if (moduleManager.modelProfile.manualSelectionCallback) {
    moduleManager.modelProfile.manualSelectionCallback(selectedModel);
  }
});

ipcMain.handle('modules:run', async (event, moduleId, config) => {
  try {
    authManager.requireAuth('run_tests');
    const session = authManager.getCurrentSession();
    
    // Create test session
    const sessionId = await offlineStorage.createTestSession({
      laptop_id: config.laptopId,
      test_type: moduleId,
      test_stage: config.stage || 'iqc',
      operator_id: session.userName,
      notes: ''  // Empty notes by default
    });
    
    // Run module
    const results = await moduleManager.runModule(moduleId, config, sessionId);
    
    // Save results
    await offlineStorage.saveTestResult({
      session_id: sessionId,
      module_id: moduleId,
      status: results.data.result || 'completed',
      data: results.data,
      errors: results.errors
    });
    
    // Update test session
    await offlineStorage.updateTestSession(sessionId, {
      end_time: new Date().toISOString(),
      result: results.data.result || 'completed',
      notes: results.data.notes || ''  // Include notes from test results if available
    });
    
    return { success: true, results };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('modules:stop', async (event, moduleId) => {
  await moduleManager.stopModule(moduleId);
  return { success: true };
});

ipcMain.handle('modules:user-input', async (event, moduleId, response) => {
  // Check if this is an event or regular user input
  if (response && response.event) {
    // Forward event to the running module instance
    const runningTests = moduleManager.runningTests;
    const runningTest = runningTests.get(moduleId);
    
    if (runningTest && runningTest.instance) {
      // Emit the event on the module instance
      runningTest.instance.emit(response.event, response.data);
      return { success: true };
    } else {
      console.error(`No running test found for module: ${moduleId}`);
      return { success: false, error: 'Module not running' };
    }
  } else {
    // Legacy callback handling
    const runningTest = moduleManager.getRunningTests().find(t => t.moduleId === moduleId);
    if (runningTest && runningTest.callback) {
      runningTest.callback(response);
    }
  }
});

// Storage operations
ipcMain.handle('storage:save-laptop', async (event, laptopData) => {
  return await offlineStorage.saveLaptop(laptopData);
});

ipcMain.handle('storage:get-laptop', async (event, identifier) => {
  return await offlineStorage.getLaptop(identifier);
});

ipcMain.handle('storage:save-hardware-test', async (event, resultData) => {
  authManager.requireAuth('run_tests');
  const session = authManager.getCurrentSession();
  
  return await offlineStorage.saveHardwareTestResult({
    ...resultData,
    tested_by: session.userName
  });
});

ipcMain.handle('storage:save-inspection', async (event, inspectionData) => {
  authManager.requireAuth('run_tests');
  const session = authManager.getCurrentSession();
  
  return await offlineStorage.savePhysicalInspection({
    ...inspectionData,
    inspected_by: session.userName
  });
});

// Sync operations
ipcMain.handle('sync:status', async () => {
  return syncEngine.getSyncStatus();
});

ipcMain.handle('sync:now', async () => {
  await syncEngine.syncNow();
  return { success: true };
});

// Workflow operations
ipcMain.handle('workflow:run', async (event, stage, laptopInfo, config) => {
  try {
    authManager.requireAuth('run_tests');
    const results = await moduleManager.runWorkflow(stage, laptopInfo, config);
    return { success: true, results };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

console.log('Laptop QC Agent v2.0 starting...');