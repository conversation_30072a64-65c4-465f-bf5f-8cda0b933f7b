// Agent v2.0 Configuration
// In production, these values would be embedded during build

module.exports = {
  // Supabase configuration
  SUPABASE_URL: process.env.SUPABASE_URL || 'https://primrdkulctsjvugdiyu.supabase.co',
  SUPABASE_SERVICE_KEY: process.env.SUPABASE_SERVICE_KEY || '', // Embedded during build
  
  // Network validation
  NETWORK_VALIDATION: process.env.NETWORK_VALIDATION || null, // e.g., '192.168.1' for subnet validation
  
  // Sync configuration
  SYNC_INTERVAL: 30000, // 30 seconds
  SYNC_BATCH_SIZE: 50,
  
  // Session configuration
  SESSION_TIMEOUT: 30 * 60 * 1000, // 30 minutes
  
  // Storage configuration
  DATA_RETENTION_DAYS: 7, // Keep synced data for 7 days
  
  // Module configuration
  MODULE_TIMEOUT: 10 * 60 * 1000, // 10 minutes max per module
  
  // Update configuration
  UPDATE_URL: process.env.UPDATE_URL || 'https://updates.laptopqc.com',
  UPDATE_CHANNEL: process.env.UPDATE_CHANNEL || 'stable',
  
  // Debug configuration
  DEBUG_MODE: process.argv.includes('--dev'),
  
  // Bridge service
  BRIDGE_SERVICE_URL: process.env.BRIDGE_SERVICE_URL || 'http://localhost:5200'
};