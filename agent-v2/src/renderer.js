// Renderer process script for Agent v2.0

let currentTest = null;
let systemInfo = null;

// Initialize on load
window.addEventListener('DOMContentLoaded', async () => {
  setupPINInput();
  setupEventListeners();
  
  // Check if already authenticated
  const session = await window.electronAPI.auth.getSession();
  if (session.isAuthenticated) {
    showMainScreen(session);
  }
});

// Setup PIN input behavior
function setupPINInput() {
  const inputs = document.querySelectorAll('.pin-digit');
  
  inputs.forEach((input, index) => {
    input.addEventListener('input', (e) => {
      if (e.target.value.length === 1 && index < inputs.length - 1) {
        inputs[index + 1].focus();
      }
    });
    
    input.addEventListener('keydown', (e) => {
      if (e.key === 'Backspace' && e.target.value === '' && index > 0) {
        inputs[index - 1].focus();
      }
      
      if (e.key === 'Enter') {
        handleLogin();
      }
    });
  });
}

// Setup event listeners
function setupEventListeners() {
  // Auth status changes
  window.electronAPI.auth.onStatusChange((data) => {
    if (data.authenticated) {
      showMainScreen(data);
    } else {
      showLoginScreen();
    }
  });
  
  // Connection status
  window.electronAPI.sync.onConnectionChange((data) => {
    const indicator = document.getElementById('connection-status');
    const text = document.getElementById('connection-text');
    
    if (data.online) {
      indicator.className = 'status-indicator online';
      text.textContent = 'Online';
    } else {
      indicator.className = 'status-indicator offline';
      text.textContent = 'Offline';
    }
  });
  
  // Sync status
  window.electronAPI.sync.onStatusChange((data) => {
    const indicator = document.getElementById('sync-status');
    const text = document.getElementById('sync-text');
    
    if (data.syncing) {
      indicator.className = 'status-indicator syncing';
      text.textContent = 'Syncing...';
    } else {
      indicator.className = 'status-indicator';
      text.textContent = data.itemsSynced ? `Synced ${data.itemsSynced} items` : 'Not syncing';
    }
  });
  
  // Module events
  window.electronAPI.modules.onProgress((data) => {
    updateTestProgress(data);
  });
  
  window.electronAPI.modules.onUserInputRequired((data) => {
    showUserInputPrompt(data);
  });
  
  window.electronAPI.modules.onInstructions((data) => {
    showTestInstructions(data);
  });
  
  window.electronAPI.modules.onDisplayUI((data) => {
    showCustomUI(data);
  });
  
  // Model selection events
  window.electronAPI.models.onSelectionRequired((data) => {
    showModelSelection(data.matches || [], (selectedModel) => {
      // Send response back to main process
      window.electronAPI.models.sendSelectionResponse(selectedModel);
    });
  });
}

// Handle login
async function handleLogin() {
  const inputs = document.querySelectorAll('.pin-digit');
  const pin = Array.from(inputs).map(i => i.value).join('');
  
  if (pin.length < 4) {
    alert('Please enter a 4-6 digit PIN');
    return;
  }
  
  const result = await window.electronAPI.auth.login(pin);
  
  if (!result.success) {
    alert(result.error || 'Invalid PIN');
    inputs.forEach(i => i.value = '');
    inputs[0].focus();
  }
}

// Handle logout
async function handleLogout() {
  if (confirm('Are you sure you want to logout?')) {
    await window.electronAPI.auth.logout();
  }
}

// Show login screen
function showLoginScreen() {
  document.getElementById('login-screen').style.display = 'block';
  document.getElementById('main-screen').classList.remove('active');
  document.getElementById('user-info').style.display = 'none';
  
  // Clear PIN inputs
  document.querySelectorAll('.pin-digit').forEach(i => i.value = '');
  document.querySelector('.pin-digit').focus();
}

// Show main screen
async function showMainScreen(session) {
  document.getElementById('login-screen').style.display = 'none';
  document.getElementById('main-screen').classList.add('active');
  document.getElementById('user-info').style.display = 'block';
  document.getElementById('user-name').textContent = session.userName;
  
  // Load system info
  await loadSystemInfo();
  
  // Small delay to ensure modules are loaded
  setTimeout(async () => {
    // Load available tests
    await loadAvailableTests();
  }, 500);
  
  // Update session timer
  updateSessionTimer();
}

// Load system information
async function loadSystemInfo() {
  systemInfo = await window.electronAPI.system.getInfo();
  
  document.getElementById('info-model').textContent = systemInfo.model || 'Unknown';
  document.getElementById('laptop-model-header').textContent = systemInfo.model || 'Unknown';
  document.getElementById('info-serial').textContent = systemInfo.serial || 'Unknown';
  document.getElementById('info-cpu').textContent = systemInfo.cpu || 'Unknown';
  document.getElementById('info-ram').textContent = systemInfo.ram || 'Unknown';
  
  // Check if laptop exists in database
  const existingLaptop = await window.electronAPI.storage.getLaptop(systemInfo.serial);
  
  if (existingLaptop) {
    // Update existing laptop
    systemInfo.laptopId = existingLaptop.id;
    systemInfo.shortId = existingLaptop.short_id;
    document.getElementById('laptop-uid').textContent = existingLaptop.short_id;
  } else {
    // Create new laptop record
    const result = await window.electronAPI.storage.saveLaptop({
      serial_number: systemInfo.serial,
      model: systemInfo.model,
      mac_address: systemInfo.network,
      ip_address: window.location.hostname || 'localhost',
      bay_number: null,  // Will be assigned later via bay assignment
      processor_model: systemInfo.cpu || null,
      ram_serial: null,
      ssd_serial: null
    });
    
    systemInfo.laptopId = result.id;
    systemInfo.shortId = result.shortId;
    document.getElementById('laptop-uid').textContent = result.shortId;
  }
  
  // Detect and load model profile ONCE during initialization
  console.log('Detecting model profile...');
  try {
    const detectedModel = await window.electronAPI.models.detect();
    console.log('Model detected:', detectedModel);
    
    // Load or create profile for this model
    const modelProfile = await window.electronAPI.models.loadProfile(detectedModel);
    console.log('Model profile loaded:', modelProfile);
    
    // Store for later use
    systemInfo.modelProfile = modelProfile;
  } catch (error) {
    console.error('Model detection failed:', error);
    // Continue without model profile - tests will use defaults
  }
}

// Test state management
const testStates = new Map(); // moduleId -> {status: 'not-started'|'in-progress'|'passed'|'failed'|'skipped', result: any}
let availableModules = [];
let currentTestModule = null;
let isFlexibleMode = true; // New flexible test mode

// Load available test modules
async function loadAvailableTests() {
  console.log('Loading available tests...');
  const modules = await window.electronAPI.modules.getAvailable();
  console.log('Received modules:', modules);
  
  if (!modules || modules.length === 0) {
    console.warn('No modules received from backend');
    return;
  }
  
  availableModules = modules;
  
  // Initialize test states
  modules.forEach(module => {
    testStates.set(module.id, { status: 'not-started', result: null });
  });
  
  if (isFlexibleMode) {
    // Create flexible test UI
    createFlexibleTestUI(modules);
  } else {
    // Legacy grid view
    createLegacyTestGrid(modules);
  }
  
  console.log(`Loaded ${modules.length} test modules`);
}

// Create flexible test UI with status buttons
function createFlexibleTestUI(modules) {
  const statusBar = document.getElementById('test-status-bar');
  if (!statusBar) return;
  
  statusBar.innerHTML = '';
  
  modules.forEach(module => {
    const button = document.createElement('button');
    button.className = 'test-status-btn not-started';
    button.id = `test-btn-${module.id}`;
    button.onclick = () => runFlexibleTest(module);
    
    const statusIcons = {
      'not-started': '○',
      'in-progress': '◐',
      'passed': '✓',
      'failed': '✗',
      'skipped': '⤏'
    };
    
    button.innerHTML = `
      <span class="status-icon">${statusIcons['not-started']}</span>
      <span>${module.name}</span>
    `;
    
    statusBar.appendChild(button);
  });
  
  // Show the current test area
  document.getElementById('current-test-area').style.display = 'block';
}

// Create legacy test grid
function createLegacyTestGrid(modules) {
  const testGrid = document.getElementById('test-grid');
  if (!testGrid) return;
  
  testGrid.innerHTML = '';
  testGrid.style.display = 'grid';
  
  modules.forEach(module => {
    const card = document.createElement('div');
    card.className = 'test-card';
    card.onclick = () => runTest(module.id);
    
    card.innerHTML = `
      <h3>${module.name}</h3>
      <p>${module.description}</p>
      <p style="margin-top: 10px; font-size: 12px; color: #999;">
        Est. time: ${module.estimatedTime} min
      </p>
    `;
    
    testGrid.appendChild(card);
  });
}

// Run a specific test
async function runTest(moduleId) {
  const hasPermission = await window.electronAPI.auth.hasPermission('run_tests');
  if (!hasPermission) {
    alert('You do not have permission to run tests');
    return;
  }
  
  currentTest = moduleId;
  
  // Show test modal
  document.getElementById('test-modal').classList.add('active');
  document.getElementById('test-title').textContent = 'Starting Test...';
  document.getElementById('progress-fill').style.width = '0%';
  document.getElementById('progress-text').textContent = '0% Complete';
  
  // Run the test
  const result = await window.electronAPI.modules.run(moduleId, {
    laptopId: systemInfo.laptopId,
    laptopModel: systemInfo.model,
    laptopSerial: systemInfo.serial,
    laptopShortId: systemInfo.shortId
  });
  
  if (result.success) {
    alert('Test completed successfully!');
  } else {
    alert('Test failed: ' + result.error);
  }
  
  // Hide modal
  document.getElementById('test-modal').classList.remove('active');
  currentTest = null;
}

// Run complete IQC workflow (legacy function for compatibility)
async function runIQCWorkflow() {
  // In flexible mode, just run all tests
  if (isFlexibleMode) {
    runAllTests();
    return;
  }
  
  const hasPermission = await window.electronAPI.auth.hasPermission('run_tests');
  if (!hasPermission) {
    alert('You do not have permission to run tests');
    return;
  }
  
  if (!confirm('This will run the complete IQC test suite. Continue?')) {
    return;
  }
  
  // Show test modal
  document.getElementById('test-modal').classList.add('active');
  document.getElementById('test-title').textContent = 'Running IQC Workflow';
  
  const result = await window.electronAPI.workflow.run('iqc', systemInfo, {});
  
  if (result.success) {
    alert('IQC workflow completed!');
  } else {
    alert('Workflow failed: ' + result.error);
  }
  
  // Hide modal
  document.getElementById('test-modal').classList.remove('active');
}

// Update test progress
function updateTestProgress(data) {
  if (data.moduleId !== currentTest) return;
  
  document.getElementById('progress-fill').style.width = data.progress + '%';
  document.getElementById('progress-text').textContent = data.message || `${data.progress}% Complete`;
}

// Show test instructions
function showTestInstructions(data) {
  const content = document.getElementById('test-content');
  
  content.innerHTML = `
    <h3>${data.instructions.title}</h3>
    <ol style="margin: 20px 0;">
      ${data.instructions.steps.map(step => `<li>${step}</li>`).join('')}
    </ol>
  `;
  
  // Show keyboard layout if available
  if (data.instructions.layout) {
    content.innerHTML += createKeyboardVisual(data.instructions.layout);
  }
}

// Create visual keyboard
function createKeyboardVisual(layout) {
  let html = '<div class="keyboard-visual">';
  
  layout.rows.forEach(row => {
    html += '<div class="keyboard-row">';
    row.forEach(key => {
      const widthClass = key.width > 1 ? `wide-${key.width}`.replace('.', '-') : '';
      html += `<div class="key ${widthClass}" data-code="${key.code}">${key.label}</div>`;
    });
    html += '</div>';
  });
  
  html += '</div>';
  return html;
}

// Show user input prompt
function showUserInputPrompt(data) {
  if (data.options && data.options.length > 0) {
    // Multiple choice
    const selected = prompt(data.prompt + '\n\nOptions: ' + data.options.join(', '));
    data.callback(selected);
  } else {
    // Free text
    const input = prompt(data.prompt);
    data.callback(input);
  }
}

// Show custom UI from test modules
function showCustomUI(data) {
  console.log('showCustomUI called with:', data.type, data.moduleId);
  
  // Handle model selection separately (doesn't require currentTest check)
  if (data.type === 'model-selection') {
    showModelSelection(data.matches || [], (selectedModel) => {
      if (data.callback) {
        data.callback(selectedModel);
      }
    });
    return;
  }
  
  // Determine where to display the UI based on mode
  let contentDiv;
  
  if (isFlexibleMode) {
    // In flexible mode, use the test display content area
    contentDiv = document.getElementById('test-display-content');
  } else {
    // In legacy mode, use the modal
    const testModal = document.getElementById('test-modal');
    if (testModal && !testModal.classList.contains('active')) {
      testModal.classList.add('active');
    }
    contentDiv = document.getElementById('test-content');
  }
  
  if (!contentDiv) {
    console.error('Content div not found!');
    return;
  }
  
  // Clear previous content
  contentDiv.innerHTML = '';
  
  if (data.type === 'color-test') {
    // For display test color UI
    contentDiv.innerHTML = data.content;
  } else if (data.type === 'usb-test') {
    // For USB test port layout
    contentDiv.innerHTML = data.content;
  } else if (data.type === 'keyboard-test') {
    // For keyboard visual layout
    contentDiv.innerHTML = data.content;
    // Add keyboard event listeners if needed
    if (data.setupListeners) {
      setupKeyboardListeners();
    }
  } else if (data.type === 'physical-inspection') {
    // For physical inspection visual
    contentDiv.innerHTML = data.content;
  } else {
    // Generic HTML content
    contentDiv.innerHTML = data.content || '';
  }
  
  console.log('UI content displayed successfully');
}

// Stop current test
async function stopCurrentTest() {
  if (currentTest && confirm('Stop the current test?')) {
    await window.electronAPI.modules.stop(currentTest);
    document.getElementById('test-modal').classList.remove('active');
    currentTest = null;
  }
}

// Update session timer
function updateSessionTimer() {
  setInterval(async () => {
    const session = await window.electronAPI.auth.getSession();
    if (session.isAuthenticated) {
      document.getElementById('session-timer').textContent = `(${session.timeRemaining})`;
    }
  }, 1000);
}

// Run flexible test
async function runFlexibleTest(module) {
  const hasPermission = await window.electronAPI.auth.hasPermission('run_tests');
  if (!hasPermission) {
    alert('You do not have permission to run tests');
    return;
  }
  
  // Update state
  currentTestModule = module;
  updateTestStatus(module.id, 'in-progress');
  
  // Update UI
  document.getElementById('current-test-title').textContent = `Current Test: ${module.name}`;
  const displayContent = document.getElementById('test-display-content');
  displayContent.innerHTML = '<p>Loading test...</p>';
  
  // Run the test module to get visual UI
  try {
    const isDebugMode = navigator.platform.includes('Mac');
    if (isDebugMode) {
      console.log('MacOS detected - running in debug mode with visual UI');
    }
    
    const result = await window.electronAPI.modules.run(module.id, {
      laptopId: systemInfo.laptopId,
      laptopModel: systemInfo.model,
      laptopSerial: systemInfo.serial,
      laptopShortId: systemInfo.shortId,
      modelProfile: systemInfo.modelProfile,  // Pass the already-loaded model profile
      debugMode: isDebugMode  // Pass debug flag for UI-only mode
    });
    
    if (result.success) {
      // On macOS, don't auto-update status - let manual controls handle it
      if (!isDebugMode) {
        updateTestStatus(module.id, 'passed', result);
      }
    } else {
      if (!isDebugMode) {
        updateTestStatus(module.id, 'failed', result);
      }
    }
  } catch (error) {
    console.error('Test error:', error);
    if (!navigator.platform.includes('Mac')) {
      updateTestStatus(module.id, 'failed', { error: error.message });
    }
  }
}

// Update test status
function updateTestStatus(moduleId, status, result = null) {
  const state = testStates.get(moduleId);
  if (state) {
    state.status = status;
    state.result = result;
  }
  
  // Update button appearance
  const button = document.getElementById(`test-btn-${moduleId}`);
  if (button) {
    button.className = `test-status-btn ${status}`;
    
    const statusIcons = {
      'not-started': '○',
      'in-progress': '◐',
      'passed': '✓',
      'failed': '✗',
      'skipped': '⤏'
    };
    
    const icon = button.querySelector('.status-icon');
    if (icon) {
      icon.textContent = statusIcons[status] || '?';
    }
  }
}

// Test control functions
function skipCurrentTest() {
  if (currentTestModule) {
    updateTestStatus(currentTestModule.id, 'skipped');
    nextTest();
  }
}

function markTestPass() {
  if (currentTestModule) {
    updateTestStatus(currentTestModule.id, 'passed', { result: 'pass', manual: true });
    alert(`${currentTestModule.name} marked as passed`);
  }
}

function markTestFail() {
  if (currentTestModule) {
    updateTestStatus(currentTestModule.id, 'failed', { result: 'fail', manual: true });
    alert(`${currentTestModule.name} marked as failed`);
  }
}

function nextTest() {
  // Find next not-started test
  const nextModule = availableModules.find(m => {
    const state = testStates.get(m.id);
    return state && state.status === 'not-started';
  });
  
  if (nextModule) {
    runFlexibleTest(nextModule);
  } else {
    alert('All tests completed!');
    document.getElementById('current-test-title').textContent = 'All Tests Completed';
    document.getElementById('test-display-content').innerHTML = generateTestSummary();
  }
}

function runAllTests() {
  // Find first not-started test
  const firstModule = availableModules.find(m => {
    const state = testStates.get(m.id);
    return state && state.status === 'not-started';
  });
  
  if (firstModule) {
    runFlexibleTest(firstModule);
  } else {
    alert('All tests have already been run!');
  }
}

function toggleTestView() {
  isFlexibleMode = !isFlexibleMode;
  loadAvailableTests();
}

function generateTestSummary() {
  let summary = '<h4>Test Summary</h4><ul>';
  
  availableModules.forEach(module => {
    const state = testStates.get(module.id);
    if (state) {
      const statusEmoji = {
        'passed': '✅',
        'failed': '❌',
        'skipped': '⤏',
        'not-started': '⚪'
      };
      summary += `<li>${statusEmoji[state.status] || '?'} ${module.name}: ${state.status}</li>`;
    }
  });
  
  summary += '</ul>';
  return summary;
}

// Manual Model Selection Functions
let selectedModelIndex = -1;
let modelSelectionCallback = null;

function showModelSelection(matches, callback) {
  selectedModelIndex = -1;
  modelSelectionCallback = callback;
  
  const modal = document.getElementById('model-selection-modal');
  const optionsDiv = document.getElementById('model-options');
  const reasonText = document.getElementById('model-selection-reason');
  
  // Clear previous options
  optionsDiv.innerHTML = '';
  
  // Set reason text
  if (matches.length === 0) {
    reasonText.textContent = 'No models matched automatically. Please enter the model manually:';
  } else {
    reasonText.textContent = `Found ${matches.length} possible matches. Please select the correct one:`;
  }
  
  // Add model options
  matches.forEach((match, index) => {
    const option = document.createElement('button');
    option.className = 'model-option';
    option.onclick = () => selectModelOption(index);
    
    const confidenceClass = match.score >= 90 ? 'confidence-high' : 
                           match.score >= 80 ? 'confidence-medium' : 
                           'confidence-low';
    
    option.innerHTML = `
      <div class="model-option-title">${match.model}</div>
      <div class="model-option-confidence ${confidenceClass}">
        Confidence: ${match.score}% - ${match.normalized || ''}
      </div>
    `;
    
    optionsDiv.appendChild(option);
  });
  
  // Show modal
  modal.classList.add('active');
  
  // Focus manual input if no matches
  if (matches.length === 0) {
    document.getElementById('manual-model-input').focus();
  }
}

function selectModelOption(index) {
  selectedModelIndex = index;
  
  // Update UI to show selection
  const options = document.querySelectorAll('.model-option');
  options.forEach((opt, i) => {
    opt.classList.toggle('selected', i === index);
  });
  
  // Clear manual input when option is selected
  document.getElementById('manual-model-input').value = '';
}

function cancelModelSelection() {
  document.getElementById('model-selection-modal').classList.remove('active');
  selectedModelIndex = -1;
  
  if (modelSelectionCallback) {
    modelSelectionCallback(null);
    modelSelectionCallback = null;
  }
}

function confirmModelSelection() {
  const modal = document.getElementById('model-selection-modal');
  const manualInput = document.getElementById('manual-model-input').value.trim();
  
  let selectedModel = null;
  
  if (manualInput) {
    // Manual input takes precedence
    selectedModel = manualInput;
  } else if (selectedModelIndex >= 0) {
    // Get selected option
    const options = document.querySelectorAll('.model-option');
    if (options[selectedModelIndex]) {
      const titleElement = options[selectedModelIndex].querySelector('.model-option-title');
      selectedModel = titleElement ? titleElement.textContent : null;
    }
  }
  
  if (!selectedModel) {
    alert('Please select a model or enter one manually');
    return;
  }
  
  // Hide modal
  modal.classList.remove('active');
  
  // Call callback with selected model
  if (modelSelectionCallback) {
    modelSelectionCallback(selectedModel);
    modelSelectionCallback = null;
  }
  
  // Reset
  selectedModelIndex = -1;
  document.getElementById('manual-model-input').value = '';
}

// Make functions globally available
window.selectModelOption = selectModelOption;
window.cancelModelSelection = cancelModelSelection;
window.confirmModelSelection = confirmModelSelection;