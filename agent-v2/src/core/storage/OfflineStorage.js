const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs').promises;
const { v4: uuidv4 } = require('uuid');

/**
 * Offline storage manager using SQLite
 */
class OfflineStorage {
  constructor(dbPath = null) {
    this.dbPath = dbPath || path.join(process.cwd(), 'data', 'offline.db');
    this.db = null;
  }

  /**
   * Initialize the database
   */
  async initialize() {
    try {
      // Ensure data directory exists
      const dataDir = path.dirname(this.dbPath);
      await fs.mkdir(dataDir, { recursive: true });
      
      // Open database
      this.db = new Database(this.dbPath);
      
      // Create tables
      await this.createTables();
      
      // Set up indexes
      await this.createIndexes();
      
      console.log('Offline storage initialized at:', this.dbPath);
    } catch (error) {
      console.error('Failed to initialize offline storage:', error);
      throw error;
    }
  }

  /**
   * Create database tables
   */
  async createTables() {
    // System info and laptop data
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS laptops (
        id TEXT PRIMARY KEY,
        short_id TEXT UNIQUE,
        serial_number TEXT,
        mac_address TEXT,
        model TEXT,
        bay_number INTEGER,
        processor_model TEXT,
        ram_serial TEXT,
        ssd_serial TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        synced_at DATETIME,
        sync_status TEXT DEFAULT 'pending'
      )
    `);

    // Test sessions
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS test_sessions (
        id TEXT PRIMARY KEY,
        laptop_id TEXT NOT NULL,
        test_type TEXT NOT NULL,
        test_stage TEXT,
        start_time DATETIME DEFAULT CURRENT_TIMESTAMP,
        end_time DATETIME,
        result TEXT,
        operator_id TEXT,
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        synced_at DATETIME,
        sync_status TEXT DEFAULT 'pending',
        FOREIGN KEY (laptop_id) REFERENCES laptops(id)
      )
    `);

    // Test results
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS test_results (
        id TEXT PRIMARY KEY,
        session_id TEXT NOT NULL,
        module_id TEXT NOT NULL,
        status TEXT NOT NULL,
        data TEXT,
        errors TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        synced_at DATETIME,
        sync_status TEXT DEFAULT 'pending',
        FOREIGN KEY (session_id) REFERENCES test_sessions(id)
      )
    `);

    // Hardware test results
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS hardware_test_results (
        id TEXT PRIMARY KEY,
        laptop_id TEXT NOT NULL,
        session_id TEXT,
        test_module TEXT NOT NULL,
        component TEXT NOT NULL,
        result TEXT NOT NULL,
        details TEXT,
        tested_by TEXT,
        tested_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        synced_at DATETIME,
        sync_status TEXT DEFAULT 'pending',
        FOREIGN KEY (laptop_id) REFERENCES laptops(id),
        FOREIGN KEY (session_id) REFERENCES test_sessions(id)
      )
    `);

    // Physical inspections
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS physical_inspections (
        id TEXT PRIMARY KEY,
        laptop_id TEXT NOT NULL,
        session_id TEXT,
        part_a_condition TEXT,
        part_b_condition TEXT,
        part_c_condition TEXT,
        part_d_condition TEXT,
        keyboard_issues TEXT,
        notes TEXT,
        inspected_by TEXT,
        inspected_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        synced_at DATETIME,
        sync_status TEXT DEFAULT 'pending',
        FOREIGN KEY (laptop_id) REFERENCES laptops(id),
        FOREIGN KEY (session_id) REFERENCES test_sessions(id)
      )
    `);

    // User sessions
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS user_sessions (
        id TEXT PRIMARY KEY,
        user_pin TEXT NOT NULL,
        user_name TEXT,
        login_time DATETIME DEFAULT CURRENT_TIMESTAMP,
        logout_time DATETIME,
        is_active INTEGER DEFAULT 1
      )
    `);

    // Sync queue
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS sync_queue (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        table_name TEXT NOT NULL,
        record_id TEXT NOT NULL,
        operation TEXT NOT NULL,
        data TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        attempts INTEGER DEFAULT 0,
        last_attempt DATETIME,
        error TEXT
      )
    `);
    
    // Model profiles
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS model_profiles (
        id TEXT PRIMARY KEY,
        manufacturer TEXT NOT NULL,
        model TEXT NOT NULL,
        full_model TEXT NOT NULL,
        specifications TEXT NOT NULL,
        test_config TEXT NOT NULL,
        research_status TEXT DEFAULT 'pending',
        last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        synced_at DATETIME,
        sync_status TEXT DEFAULT 'pending'
      )
    `);
  }

  /**
   * Create database indexes
   */
  async createIndexes() {
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_laptops_sync ON laptops(sync_status)',
      'CREATE INDEX IF NOT EXISTS idx_laptops_serial ON laptops(serial_number)',
      'CREATE INDEX IF NOT EXISTS idx_test_sessions_laptop ON test_sessions(laptop_id)',
      'CREATE INDEX IF NOT EXISTS idx_test_sessions_sync ON test_sessions(sync_status)',
      'CREATE INDEX IF NOT EXISTS idx_test_results_session ON test_results(session_id)',
      'CREATE INDEX IF NOT EXISTS idx_hardware_tests_laptop ON hardware_test_results(laptop_id)',
      'CREATE INDEX IF NOT EXISTS idx_sync_queue_created ON sync_queue(created_at)'
    ];

    for (const index of indexes) {
      this.db.exec(index);
    }
  }

  /**
   * Generate short ID for laptop (A001, A002, etc.)
   */
  async generateShortId() {
    // Get the highest existing ID
    const stmt = this.db.prepare(`
      SELECT short_id FROM laptops 
      WHERE short_id LIKE 'A%' 
      ORDER BY short_id DESC 
      LIMIT 1
    `);
    
    const result = stmt.get();
    
    let nextNumber = 1;
    if (result && result.short_id) {
      const lastNumber = parseInt(result.short_id.substring(1));
      if (!isNaN(lastNumber)) {
        nextNumber = lastNumber + 1;
      }
    }
    
    return `A${nextNumber.toString().padStart(3, '0')}`;
  }

  /**
   * Save or update laptop info
   */
  async saveLaptop(laptopData) {
    const id = laptopData.id || uuidv4();
    
    // Generate short_id if not provided
    let shortId = laptopData.short_id;
    if (!shortId) {
      shortId = await this.generateShortId();
    }
    
    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO laptops (
        id, short_id, serial_number, mac_address, model, 
        bay_number, processor_model, ram_serial, ssd_serial,
        updated_at, sync_status
      ) VALUES (
        @id, @short_id, @serial_number, @mac_address, @model,
        @bay_number, @processor_model, @ram_serial, @ssd_serial,
        CURRENT_TIMESTAMP, 'pending'
      )
    `);

    const laptopRecord = { 
      id, 
      short_id: shortId,
      ...laptopData 
    };
    
    stmt.run(laptopRecord);
    
    // Add to sync queue
    await this.addToSyncQueue('laptops', id, 'upsert', laptopRecord);
    
    return { id, shortId };
  }

  /**
   * Get laptop by ID, short_id, or serial number
   */
  async getLaptop(identifier) {
    const stmt = this.db.prepare(`
      SELECT * FROM laptops 
      WHERE id = @identifier 
         OR short_id = @identifier 
         OR serial_number = @identifier
    `);
    
    return stmt.get({ identifier });
  }

  /**
   * Create test session
   */
  async createTestSession(sessionData) {
    const id = sessionData.id || uuidv4();
    const stmt = this.db.prepare(`
      INSERT INTO test_sessions (
        id, laptop_id, test_type, test_stage, 
        operator_id, notes, sync_status
      ) VALUES (
        @id, @laptop_id, @test_type, @test_stage,
        @operator_id, @notes, 'pending'
      )
    `);

    stmt.run({ id, ...sessionData });
    
    // Add to sync queue
    await this.addToSyncQueue('test_sessions', id, 'insert', { id, ...sessionData });
    
    return id;
  }

  /**
   * Update test session
   */
  async updateTestSession(sessionId, updates) {
    const stmt = this.db.prepare(`
      UPDATE test_sessions 
      SET end_time = @end_time, result = @result, 
          notes = @notes, sync_status = 'pending'
      WHERE id = @id
    `);

    stmt.run({ id: sessionId, ...updates });
    
    // Add to sync queue
    await this.addToSyncQueue('test_sessions', sessionId, 'update', updates);
  }

  /**
   * Save test result
   */
  async saveTestResult(resultData) {
    const id = resultData.id || uuidv4();
    const stmt = this.db.prepare(`
      INSERT INTO test_results (
        id, session_id, module_id, status, data, errors, sync_status
      ) VALUES (
        @id, @session_id, @module_id, @status, 
        @data, @errors, 'pending'
      )
    `);

    stmt.run({
      id,
      ...resultData,
      data: JSON.stringify(resultData.data || {}),
      errors: JSON.stringify(resultData.errors || [])
    });
    
    // Add to sync queue
    await this.addToSyncQueue('test_results', id, 'insert', resultData);
    
    return id;
  }

  /**
   * Save hardware test result
   */
  async saveHardwareTestResult(resultData) {
    const id = resultData.id || uuidv4();
    const stmt = this.db.prepare(`
      INSERT INTO hardware_test_results (
        id, laptop_id, session_id, test_module, component,
        result, details, tested_by, sync_status
      ) VALUES (
        @id, @laptop_id, @session_id, @test_module, @component,
        @result, @details, @tested_by, 'pending'
      )
    `);

    stmt.run({
      id,
      ...resultData,
      details: JSON.stringify(resultData.details || {})
    });
    
    // Add to sync queue
    await this.addToSyncQueue('hardware_test_results', id, 'insert', resultData);
    
    return id;
  }

  /**
   * Save physical inspection
   */
  async savePhysicalInspection(inspectionData) {
    const id = inspectionData.id || uuidv4();
    const stmt = this.db.prepare(`
      INSERT INTO physical_inspections (
        id, laptop_id, session_id, part_a_condition, part_b_condition,
        part_c_condition, part_d_condition, keyboard_issues, 
        notes, inspected_by, sync_status
      ) VALUES (
        @id, @laptop_id, @session_id, @part_a_condition, @part_b_condition,
        @part_c_condition, @part_d_condition, @keyboard_issues,
        @notes, @inspected_by, 'pending'
      )
    `);

    stmt.run({
      id,
      ...inspectionData,
      keyboard_issues: JSON.stringify(inspectionData.keyboard_issues || {}),
      notes: inspectionData.notes || ''  // Provide default empty string if notes is missing
    });
    
    // Add to sync queue
    await this.addToSyncQueue('physical_inspections', id, 'insert', inspectionData);
    
    return id;
  }

  /**
   * Create user session
   */
  async createUserSession(pin, userName = null) {
    const id = uuidv4();
    const stmt = this.db.prepare(`
      INSERT INTO user_sessions (id, user_pin, user_name)
      VALUES (@id, @pin, @userName)
    `);

    stmt.run({ id, pin, userName });
    return id;
  }

  /**
   * Get active user session
   */
  async getActiveUserSession() {
    const stmt = this.db.prepare(`
      SELECT * FROM user_sessions 
      WHERE is_active = 1 
      ORDER BY login_time DESC 
      LIMIT 1
    `);
    
    return stmt.get();
  }

  /**
   * End user session
   */
  async endUserSession(sessionId) {
    const stmt = this.db.prepare(`
      UPDATE user_sessions 
      SET logout_time = CURRENT_TIMESTAMP, is_active = 0
      WHERE id = @sessionId
    `);

    stmt.run({ sessionId });
  }

  /**
   * Add item to sync queue
   */
  async addToSyncQueue(tableName, recordId, operation, data) {
    const stmt = this.db.prepare(`
      INSERT INTO sync_queue (table_name, record_id, operation, data)
      VALUES (@tableName, @recordId, @operation, @data)
    `);

    stmt.run({
      tableName,
      recordId,
      operation,
      data: JSON.stringify(data)
    });
  }

  /**
   * Get pending sync items
   */
  async getPendingSyncItems(limit = 100) {
    const stmt = this.db.prepare(`
      SELECT * FROM sync_queue 
      WHERE attempts < 3 
      ORDER BY created_at ASC 
      LIMIT @limit
    `);
    
    return stmt.all({ limit });
  }

  /**
   * Mark sync item as completed
   */
  async markSyncCompleted(queueId, tableName, recordId) {
    // Remove from queue
    const deleteStmt = this.db.prepare('DELETE FROM sync_queue WHERE id = @queueId');
    deleteStmt.run({ queueId });
    
    // Update sync status
    const updateStmt = this.db.prepare(`
      UPDATE ${tableName} 
      SET sync_status = 'synced', synced_at = CURRENT_TIMESTAMP 
      WHERE id = @recordId
    `);
    updateStmt.run({ recordId });
  }

  /**
   * Mark sync item as failed
   */
  async markSyncFailed(queueId, error) {
    const stmt = this.db.prepare(`
      UPDATE sync_queue 
      SET attempts = attempts + 1, 
          last_attempt = CURRENT_TIMESTAMP,
          error = @error
      WHERE id = @queueId
    `);
    
    stmt.run({ queueId, error });
  }

  /**
   * Get unsynced count
   */
  async getUnsyncedCount() {
    const stmt = this.db.prepare(`
      SELECT COUNT(*) as count FROM sync_queue WHERE attempts < 3
    `);
    
    return stmt.get().count;
  }
  
  /**
   * Save or update model profile
   */
  async saveModelProfile(profile) {
    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO model_profiles (
        id, manufacturer, model, full_model,
        specifications, test_config, research_status,
        last_updated, updated_at, sync_status
      ) VALUES (
        @id, @manufacturer, @model, @full_model,
        @specifications, @test_config, @research_status,
        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'pending'
      )
    `);
    
    stmt.run({
      id: profile.id,
      manufacturer: profile.manufacturer,
      model: profile.model,
      full_model: profile.fullModel,
      specifications: JSON.stringify(profile.specifications),
      test_config: JSON.stringify(profile.test_config),
      research_status: profile.research_status || 'pending'
    });
    
    // Add to sync queue
    await this.addToSyncQueue('model_profiles', profile.id, 'upsert', profile);
    
    return profile.id;
  }
  
  /**
   * Get model profile by name
   */
  async getModelProfile(fullModel) {
    const stmt = this.db.prepare(`
      SELECT * FROM model_profiles 
      WHERE full_model = @fullModel
      ORDER BY last_updated DESC
      LIMIT 1
    `);
    
    const result = stmt.get({ fullModel });
    
    if (result) {
      return {
        ...result,
        specifications: JSON.parse(result.specifications),
        test_config: JSON.parse(result.test_config)
      };
    }
    
    return null;
  }

  /**
   * Get all model profiles
   */
  async getAllModelProfiles() {
    const stmt = this.db.prepare(`
      SELECT * FROM model_profiles 
      ORDER BY manufacturer, model
    `);
    
    const results = stmt.all();
    
    return results.map(result => ({
      ...result,
      specifications: JSON.parse(result.specifications),
      test_config: JSON.parse(result.test_config)
    }));
  }

  /**
   * Clean up old synced data
   */
  async cleanupSyncedData(daysToKeep = 7) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
    
    const tables = ['laptops', 'test_sessions', 'test_results', 'hardware_test_results', 'physical_inspections'];
    
    for (const table of tables) {
      const stmt = this.db.prepare(`
        DELETE FROM ${table} 
        WHERE sync_status = 'synced' 
        AND synced_at < @cutoffDate
      `);
      
      stmt.run({ cutoffDate: cutoffDate.toISOString() });
    }
  }

  /**
   * Close database connection
   */
  close() {
    if (this.db) {
      this.db.close();
      this.db = null;
    }
  }
}

module.exports = OfflineStorage;