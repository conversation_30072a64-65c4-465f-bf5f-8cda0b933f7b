const EventEmitter = require('events');
const { createClient } = require('@supabase/supabase-js');
const cron = require('node-cron');

/**
 * Background sync engine for offline data
 */
class SyncEngine extends EventEmitter {
  constructor(offlineStorage, config = {}) {
    super();
    this.storage = offlineStorage;
    this.config = {
      syncInterval: 30000, // 30 seconds
      batchSize: 50,
      maxRetries: 3,
      ...config
    };
    
    this.supabase = null;
    this.isOnline = false;
    this.isSyncing = false;
    this.syncTimer = null;
    this.syncStats = {
      lastSync: null,
      itemsSynced: 0,
      itemsFailed: 0,
      totalPending: 0
    };
  }

  /**
   * Initialize sync engine
   */
  async initialize(supabaseUrl, supabaseKey) {
    try {
      // Initialize Supabase client
      this.supabase = createClient(supabaseUrl, supabaseKey);
      
      // Check connection
      await this.checkConnection();
      
      // Start sync timer
      this.startSyncTimer();
      
      // Schedule periodic cleanup
      cron.schedule('0 0 * * *', () => {
        this.cleanupOldData();
      });
      
      this.emit('initialized');
    } catch (error) {
      console.error('Failed to initialize sync engine:', error);
      this.emit('error', { phase: 'initialization', error: error.message });
    }
  }

  /**
   * Check connection to Supabase
   */
  async checkConnection() {
    try {
      const { data, error } = await this.supabase
        .from('laptops')
        .select('count')
        .limit(1);
      
      if (error) throw error;
      
      this.isOnline = true;
      this.emit('connectionChanged', { online: true });
      return true;
    } catch (error) {
      this.isOnline = false;
      this.emit('connectionChanged', { online: false });
      return false;
    }
  }

  /**
   * Start sync timer
   */
  startSyncTimer() {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
    }
    
    // Initial sync
    this.performSync();
    
    // Schedule periodic syncs
    this.syncTimer = setInterval(() => {
      this.performSync();
    }, this.config.syncInterval);
  }

  /**
   * Stop sync timer
   */
  stopSyncTimer() {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
      this.syncTimer = null;
    }
  }

  /**
   * Perform sync operation
   */
  async performSync() {
    if (this.isSyncing) {
      console.log('Sync already in progress, skipping...');
      return;
    }
    
    // Check connection first
    if (!await this.checkConnection()) {
      console.log('Offline, skipping sync...');
      return;
    }
    
    this.isSyncing = true;
    this.emit('syncStarted');
    
    try {
      // Get pending items
      const pendingItems = await this.storage.getPendingSyncItems(this.config.batchSize);
      this.syncStats.totalPending = await this.storage.getUnsyncedCount();
      
      if (pendingItems.length === 0) {
        this.emit('syncCompleted', {
          itemsSynced: 0,
          message: 'No pending items to sync'
        });
        return;
      }
      
      // Process each item
      let syncedCount = 0;
      let failedCount = 0;
      
      for (const item of pendingItems) {
        try {
          await this.syncItem(item);
          syncedCount++;
        } catch (error) {
          console.error(`Failed to sync item ${item.id}:`, error);
          await this.storage.markSyncFailed(item.id, error.message);
          failedCount++;
        }
      }
      
      // Update stats
      this.syncStats.lastSync = new Date();
      this.syncStats.itemsSynced += syncedCount;
      this.syncStats.itemsFailed += failedCount;
      
      this.emit('syncCompleted', {
        itemsSynced: syncedCount,
        itemsFailed: failedCount,
        totalPending: await this.storage.getUnsyncedCount()
      });
      
    } catch (error) {
      console.error('Sync error:', error);
      this.emit('syncError', { error: error.message });
    } finally {
      this.isSyncing = false;
    }
  }

  /**
   * Sync individual item
   */
  async syncItem(item) {
    const data = JSON.parse(item.data);
    
    switch (item.table_name) {
      case 'laptops':
        await this.syncLaptop(item, data);
        break;
        
      case 'test_sessions':
        await this.syncTestSession(item, data);
        break;
        
      case 'test_results':
        await this.syncTestResult(item, data);
        break;
        
      case 'hardware_test_results':
        await this.syncHardwareTestResult(item, data);
        break;
        
      case 'physical_inspections':
        await this.syncPhysicalInspection(item, data);
        break;
        
      default:
        throw new Error(`Unknown table: ${item.table_name}`);
    }
    
    // Mark as synced
    await this.storage.markSyncCompleted(item.id, item.table_name, item.record_id);
  }

  /**
   * Sync laptop data
   */
  async syncLaptop(item, data) {
    if (item.operation === 'upsert') {
      const { error } = await this.supabase
        .from('laptops')
        .upsert({
          id: data.id,
          short_id: data.short_id,
          serial_number: data.serial_number,
          mac_address: data.mac_address,
          model: data.model,
          bay_number: data.bay_number,
          processor_model: data.processor_model,
          created_at: data.created_at,
          updated_at: data.updated_at
        });
      
      if (error) throw error;
    }
  }

  /**
   * Sync test session
   */
  async syncTestSession(item, data) {
    if (item.operation === 'insert') {
      const { error } = await this.supabase
        .from('test_sessions')
        .insert({
          id: data.id,
          laptop_id: data.laptop_id,
          test_type: data.test_type,
          start_time: data.start_time,
          end_time: data.end_time,
          result: data.result,
          operator_id: data.operator_id,
          notes: data.notes
        });
      
      if (error) throw error;
    } else if (item.operation === 'update') {
      const { error } = await this.supabase
        .from('test_sessions')
        .update({
          end_time: data.end_time,
          result: data.result,
          notes: data.notes
        })
        .eq('id', item.record_id);
      
      if (error) throw error;
    }
  }

  /**
   * Sync test result
   */
  async syncTestResult(item, data) {
    const { error } = await this.supabase
      .from('test_results')
      .insert({
        id: data.id,
        session_id: data.session_id,
        module_id: data.module_id,
        status: data.status,
        data: data.data,
        errors: data.errors,
        created_at: data.created_at
      });
    
    if (error) throw error;
  }

  /**
   * Sync hardware test result
   */
  async syncHardwareTestResult(item, data) {
    const { error } = await this.supabase
      .from('hardware_test_results')
      .insert({
        id: data.id,
        laptop_id: data.laptop_id,
        session_id: data.session_id,
        test_module: data.test_module,
        component: data.component,
        result: data.result,
        details: data.details,
        tested_by: data.tested_by,
        tested_at: data.tested_at
      });
    
    if (error) throw error;
  }

  /**
   * Sync physical inspection
   */
  async syncPhysicalInspection(item, data) {
    const { error } = await this.supabase
      .from('physical_inspections')
      .insert({
        id: data.id,
        laptop_id: data.laptop_id,
        session_id: data.session_id,
        part_a_condition: data.part_a_condition,
        part_b_condition: data.part_b_condition,
        part_c_condition: data.part_c_condition,
        part_d_condition: data.part_d_condition,
        keyboard_issues: data.keyboard_issues,
        notes: data.notes,
        inspected_by: data.inspected_by,
        inspected_at: data.inspected_at
      });
    
    if (error) throw error;
  }

  /**
   * Force sync now
   */
  async syncNow() {
    return await this.performSync();
  }

  /**
   * Get sync status
   */
  getSyncStatus() {
    return {
      isOnline: this.isOnline,
      isSyncing: this.isSyncing,
      stats: this.syncStats
    };
  }

  /**
   * Clean up old synced data
   */
  async cleanupOldData() {
    try {
      await this.storage.cleanupSyncedData(7); // Keep 7 days
      this.emit('cleanupCompleted');
    } catch (error) {
      console.error('Cleanup error:', error);
      this.emit('cleanupError', { error: error.message });
    }
  }

  /**
   * Handle conflict resolution
   */
  async resolveConflict(localData, remoteData) {
    // Simple last-write-wins strategy
    // In production, might want more sophisticated conflict resolution
    const localTime = new Date(localData.updated_at || localData.created_at);
    const remoteTime = new Date(remoteData.updated_at || remoteData.created_at);
    
    return localTime > remoteTime ? localData : remoteData;
  }

  /**
   * Shutdown sync engine
   */
  async shutdown() {
    this.stopSyncTimer();
    
    // Final sync attempt
    if (this.isOnline && !this.isSyncing) {
      await this.performSync();
    }
    
    this.emit('shutdown');
  }
}

module.exports = SyncEngine;