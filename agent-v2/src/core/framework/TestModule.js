const EventEmitter = require('events');

/**
 * Base class for all test modules
 * Provides common interface and lifecycle methods
 */
class TestModule extends EventEmitter {
  constructor(config) {
    super();
    this.id = config.id;
    this.name = config.name;
    this.version = config.version || '1.0.0';
    this.requiredHardware = config.requiredHardware || [];
    this.description = config.description || '';
    this.category = config.category || 'general';
    this.estimatedTime = config.estimatedTime || 0; // in minutes
    
    this.isInitialized = false;
    this.isRunning = false;
    this.results = null;
    this.errors = [];
  }

  /**
   * Initialize the module - load resources, check dependencies
   */
  async initialize() {
    if (this.isInitialized) return true;
    
    try {
      // Check hardware requirements
      const hardwareCheck = await this.checkHardwareRequirements();
      if (!hardwareCheck.success) {
        throw new Error(`Hardware requirements not met: ${hardwareCheck.missing.join(', ')}`);
      }
      
      // Module-specific initialization
      await this.onInitialize();
      
      this.isInitialized = true;
      this.emit('initialized', { module: this.name });
      return true;
    } catch (error) {
      this.errors.push({
        phase: 'initialization',
        error: error.message,
        timestamp: new Date()
      });
      this.emit('error', { module: this.name, error: error.message });
      throw error;
    }
  }

  /**
   * Run the test module
   */
  async runTest(config = {}) {
    if (!this.isInitialized) {
      await this.initialize();
    }
    
    if (this.isRunning) {
      throw new Error('Test is already running');
    }
    
    this.isRunning = true;
    this.results = null;
    this.errors = [];
    
    try {
      this.emit('testStarted', { 
        module: this.name, 
        config,
        estimatedTime: this.estimatedTime 
      });
      
      // Run the actual test implementation
      const results = await this.onRunTest(config);
      
      // Validate result format
      const validatedResults = this.validateResults(results);
      
      this.results = {
        module: this.name,
        version: this.version,
        timestamp: new Date(),
        config,
        data: validatedResults,
        errors: this.errors
      };
      
      this.emit('testCompleted', this.results);
      return this.results;
    } catch (error) {
      this.errors.push({
        phase: 'execution',
        error: error.message,
        timestamp: new Date()
      });
      
      this.results = {
        module: this.name,
        version: this.version,
        timestamp: new Date(),
        config,
        error: error.message,
        errors: this.errors
      };
      
      this.emit('testFailed', { module: this.name, error: error.message });
      throw error;
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Get the current test results
   */
  getResults() {
    return this.results;
  }

  /**
   * Stop the running test
   */
  async stopTest() {
    if (!this.isRunning) return;
    
    try {
      await this.onStopTest();
      this.isRunning = false;
      this.emit('testStopped', { module: this.name });
    } catch (error) {
      this.emit('error', { module: this.name, error: error.message });
      throw error;
    }
  }

  /**
   * Clean up resources
   */
  async cleanup() {
    if (this.isRunning) {
      await this.stopTest();
    }
    
    try {
      await this.onCleanup();
      this.isInitialized = false;
      this.emit('cleanup', { module: this.name });
    } catch (error) {
      this.emit('error', { module: this.name, error: error.message });
    }
  }

  /**
   * Check if required hardware is available
   */
  async checkHardwareRequirements() {
    // Default implementation - override in specific modules
    return { success: true, missing: [] };
  }

  /**
   * Emit progress updates
   */
  updateProgress(progress, message = '') {
    this.emit('progress', {
      module: this.name,
      progress: Math.min(100, Math.max(0, progress)),
      message
    });
  }

  /**
   * Add a warning (non-fatal issue)
   */
  addWarning(warning) {
    this.errors.push({
      phase: 'execution',
      type: 'warning',
      message: warning,
      timestamp: new Date()
    });
  }

  // Abstract methods to be implemented by subclasses
  async onInitialize() {
    // Override in subclass
  }

  async onRunTest(config) {
    // Override in subclass
    throw new Error('onRunTest must be implemented by subclass');
  }

  async onStopTest() {
    // Override in subclass
  }

  async onCleanup() {
    // Override in subclass
  }

  // Validate test results include required fields
  validateResults(results) {
    const validResults = ['pass', 'fail', 'partial', 'not-tested', 'driver-issue', 'skipped', 'bios-disabled'];
    
    if (!results.result || !validResults.includes(results.result)) {
      console.warn(`Invalid result '${results.result}' for module ${this.name}. Defaulting to 'not-tested'`);
      results.result = 'not-tested';
    }
    
    // Ensure we have a reason for non-pass results
    if (results.result !== 'pass' && !results.resultReason) {
      results.resultReason = this.getDefaultReason(results.result);
    }
    
    return results;
  }
  
  getDefaultReason(result) {
    const reasons = {
      'fail': 'Hardware failure detected',
      'partial': 'Some functionality not working',
      'not-tested': 'Test could not be completed',
      'driver-issue': 'Required drivers not available',
      'skipped': 'Test not applicable to this model',
      'bios-disabled': 'Feature disabled in BIOS settings'
    };
    return reasons[result] || 'Unknown issue';
  }

  // Utility method for visual test modules
  async waitForUserInput(prompt, options = []) {
    return new Promise((resolve) => {
      this.emit('userInputRequired', {
        module: this.name,
        prompt,
        options,
        callback: resolve
      });
    });
  }
  
  // Utility method for getting user input
  async getUserInput(prompt, options = null) {
    return this.waitForUserInput(prompt, options);
  }

  // Utility method for instruction display
  displayInstructions(instructions) {
    this.emit('displayInstructions', {
      module: this.name,
      instructions
    });
  }
}

module.exports = TestModule;