const EventEmitter = require('events');
const path = require('path');
const fs = require('fs').promises;
const ModelProfile = require('../models/ModelProfile');

/**
 * Manages test modules lifecycle and execution
 */
class ModuleManager extends EventEmitter {
  constructor(storage) {
    super();
    this.modules = new Map();
    this.runningTests = new Map();
    this.moduleDirectory = path.join(__dirname, '../../modules');
    this.storage = storage;
    this.modelProfile = new ModelProfile(storage);
  }

  /**
   * Load all available modules
   */
  async loadModules() {
    try {
      const moduleCategories = await fs.readdir(this.moduleDirectory);
      
      for (const category of moduleCategories) {
        const categoryPath = path.join(this.moduleDirectory, category);
        const stat = await fs.stat(categoryPath);
        
        if (stat.isDirectory()) {
          await this.loadModulesFromCategory(category, categoryPath);
        }
      }
      
      console.log(`Loaded ${this.modules.size} test modules`);
      this.emit('modulesLoaded', Array.from(this.modules.keys()));
    } catch (error) {
      console.error('Error loading modules:', error);
      this.emit('error', { phase: 'loading', error: error.message });
    }
  }

  /**
   * Load modules from a specific category directory
   */
  async loadModulesFromCategory(category, categoryPath) {
    try {
      const files = await fs.readdir(categoryPath);
      
      for (const file of files) {
        if (file.endsWith('.js') && !file.startsWith('index')) {
          const modulePath = path.join(categoryPath, file);
          await this.loadModule(modulePath, category);
        }
      }
    } catch (error) {
      console.error(`Error loading modules from ${category}:`, error);
    }
  }

  /**
   * Load a single module
   */
  async loadModule(modulePath, category) {
    try {
      const ModuleClass = require(modulePath);
      
      // Check if it's a valid module class
      if (typeof ModuleClass !== 'function') {
        console.warn(`${modulePath} does not export a valid module class`);
        return;
      }
      
      // Create instance to get metadata
      const tempInstance = new ModuleClass();
      const moduleId = `${category}/${tempInstance.id}`;
      
      this.modules.set(moduleId, {
        id: moduleId,
        name: tempInstance.name,
        category,
        version: tempInstance.version,
        description: tempInstance.description,
        estimatedTime: tempInstance.estimatedTime,
        requiredHardware: tempInstance.requiredHardware,
        path: modulePath,
        Class: ModuleClass
      });
      
      // Clean up temp instance
      if (tempInstance.cleanup) {
        await tempInstance.cleanup();
      }
      
      console.log(`Loaded module: ${moduleId}`);
    } catch (error) {
      console.error(`Error loading module ${modulePath}:`, error);
    }
  }

  /**
   * Get all available modules
   */
  getAvailableModules() {
    const modules = Array.from(this.modules.values()).map(m => ({
      id: m.id,
      name: m.name,
      category: m.category,
      version: m.version,
      description: m.description,
      estimatedTime: m.estimatedTime,
      requiredHardware: m.requiredHardware
    }));
    console.log(`getAvailableModules called, returning ${modules.length} modules`);
    return modules;
  }

  /**
   * Get modules by category
   */
  getModulesByCategory(category) {
    return this.getAvailableModules().filter(m => m.category === category);
  }

  /**
   * Get module info
   */
  getModuleInfo(moduleId) {
    return this.modules.get(moduleId);
  }

  /**
   * Create and initialize a module instance
   */
  async createModuleInstance(moduleId) {
    const moduleInfo = this.modules.get(moduleId);
    if (!moduleInfo) {
      throw new Error(`Module ${moduleId} not found`);
    }
    
    const instance = new moduleInfo.Class();
    
    // Forward module events
    instance.on('progress', (data) => {
      this.emit('moduleProgress', { moduleId, ...data });
    });
    
    instance.on('userInputRequired', (data) => {
      this.emit('userInputRequired', { moduleId, ...data });
    });
    
    instance.on('displayInstructions', (data) => {
      this.emit('displayInstructions', { moduleId, ...data });
    });
    
    instance.on('displayUI', (data) => {
      this.emit('displayUI', { moduleId, ...data });
    });
    
    instance.on('error', (data) => {
      this.emit('moduleError', { moduleId, ...data });
    });
    
    await instance.initialize();
    return instance;
  }

  /**
   * Run a test module
   */
  async runModule(moduleId, config = {}, testSessionId = null) {
    console.log(`Starting module: ${moduleId}`);
    if (this.runningTests.has(moduleId)) {
      throw new Error(`Module ${moduleId} is already running`);
    }
    
    try {
      // Check if model profile was passed in config
      if (config.modelProfile) {
        // Set it as current profile if not already set
        if (!this.modelProfile.currentProfile) {
          this.modelProfile.currentProfile = config.modelProfile;
        }
      }
      
      // Get model-specific test configuration if profile exists
      let modelConfig = {};
      if (this.modelProfile.currentProfile) {
        modelConfig = this.modelProfile.getTestConfig(moduleId);
      }
      
      // Merge configurations
      const finalConfig = {
        ...modelConfig,
        ...config,
        modelProfile: this.modelProfile.currentProfile
      };
      
      const instance = await this.createModuleInstance(moduleId);
      
      this.runningTests.set(moduleId, {
        instance,
        startTime: new Date(),
        sessionId: testSessionId,
        config: finalConfig
      });
      
      this.emit('moduleStarted', {
        moduleId,
        sessionId: testSessionId,
        config: finalConfig
      });
      
      console.log(`Running test for module: ${moduleId}`);
      const results = await instance.runTest(finalConfig);
      console.log(`Module ${moduleId} completed with result:`, results?.result || 'unknown');
      
      this.emit('moduleCompleted', {
        moduleId,
        sessionId: testSessionId,
        results
      });
      
      return results;
    } catch (error) {
      this.emit('moduleFailed', {
        moduleId,
        sessionId: testSessionId,
        error: error.message
      });
      throw error;
    } finally {
      // Clean up
      const running = this.runningTests.get(moduleId);
      if (running && running.instance) {
        await running.instance.cleanup();
      }
      this.runningTests.delete(moduleId);
    }
  }

  /**
   * Stop a running module
   */
  async stopModule(moduleId) {
    const running = this.runningTests.get(moduleId);
    if (!running) {
      return;
    }
    
    try {
      await running.instance.stopTest();
      this.emit('moduleStopped', {
        moduleId,
        sessionId: running.sessionId
      });
    } catch (error) {
      console.error(`Error stopping module ${moduleId}:`, error);
    } finally {
      if (running.instance) {
        await running.instance.cleanup();
      }
      this.runningTests.delete(moduleId);
    }
  }

  /**
   * Stop all running modules
   */
  async stopAllModules() {
    const runningModuleIds = Array.from(this.runningTests.keys());
    
    for (const moduleId of runningModuleIds) {
      await this.stopModule(moduleId);
    }
  }

  /**
   * Get running tests
   */
  getRunningTests() {
    return Array.from(this.runningTests.entries()).map(([moduleId, info]) => ({
      moduleId,
      startTime: info.startTime,
      sessionId: info.sessionId,
      config: info.config
    }));
  }

  /**
   * Run multiple modules in sequence
   */
  async runModuleSequence(moduleIds, globalConfig = {}, testSessionId = null) {
    const results = [];
    let systemInfo = null;
    let detectedModel = null;
    
    for (const moduleId of moduleIds) {
      try {
        // Enhance config with system info if available
        const moduleConfig = {
          ...globalConfig,
          systemInfo: systemInfo,
          detectedModel: detectedModel
        };
        
        const result = await this.runModule(moduleId, moduleConfig, testSessionId);
        
        // If this was SystemInfoDetection, capture its results
        if (moduleId === 'hardware/system-info-detection') {
          systemInfo = result.details || {};
          detectedModel = result.detectedModel || null;
          
          // Update model profile with detected info
          if (detectedModel) {
            this.modelProfile.currentModel = detectedModel;
            await this.modelProfile.loadProfile();
          }
        }
        
        results.push({
          moduleId,
          success: true,
          results: result
        });
      } catch (error) {
        results.push({
          moduleId,
          success: false,
          error: error.message
        });
        
        // Optionally continue or stop on error
        if (globalConfig.stopOnError) {
          break;
        }
      }
    }
    
    return results;
  }

  /**
   * Run test workflow based on stage
   */
  async runWorkflow(stage, laptopInfo, config = {}) {
    console.log(`Starting workflow for stage: ${stage}`);
    const workflows = {
      'iqc': [
        'hardware/system-info-detection',
        'hardware/display-test',
        'hardware/keyboard-test',
        'hardware/usb-test',
        'hardware/audio-test',
        'inspection/physical-inspection'
      ],
      'mid_qc': [
        'hardware/display-test',
        'hardware/keyboard-test',
        'inspection/physical-inspection'
      ],
      'final_qc': [
        'hardware/system-info-detection',
        // 'battery/health-check',  // TODO: Create this module
        // 'battery/test',         // TODO: Create this module
        'hardware/display-test',
        'hardware/keyboard-test',
        'hardware/usb-test',
        'hardware/audio-test',
        // 'hardware/stress-test', // TODO: Create this module
        'inspection/physical-inspection'
      ]
    };
    
    const moduleIds = workflows[stage] || [];
    console.log(`Running ${moduleIds.length} modules:`, moduleIds);
    const workflowConfig = {
      ...config,
      stage,
      laptopInfo
    };
    
    return await this.runModuleSequence(moduleIds, workflowConfig);
  }
}

module.exports = ModuleManager;