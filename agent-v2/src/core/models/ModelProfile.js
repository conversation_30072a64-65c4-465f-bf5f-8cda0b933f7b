const si = require('systeminformation');
const ModelMatcher = require('./ModelMatcher');
const { EventEmitter } = require('events');

/**
 * Model Profile Manager with Smart Matching
 * Handles model detection with fuzzy matching and model-specific test configurations
 */
class ModelProfile extends EventEmitter {
  constructor(storage) {
    super();
    this.storage = storage;
    this.currentModel = null;
    this.currentProfile = null;
    this.modelMatcher = new ModelMatcher();
  }

  /**
   * Detect laptop model from system information
   */
  async detectModel() {
    try {
      const system = await si.system();
      
      // Extract manufacturer and model
      const manufacturer = system.manufacturer || '';
      const model = system.model || '';
      const version = system.version || '';
      
      // Clean up model string (remove extra spaces, special chars)
      const cleanModel = `${manufacturer} ${model} ${version}`
        .replace(/\s+/g, ' ')
        .replace(/[^\w\s-]/g, '')
        .trim();
      
      this.currentModel = {
        manufacturer: manufacturer,
        model: model,
        fullModel: cleanModel,
        version: version
      };
      
      console.log('Detected model:', cleanModel);
      return this.currentModel;
    } catch (error) {
      console.error('Failed to detect model:', error);
      return null;
    }
  }

  /**
   * Load model profile with fuzzy matching support
   */
  async loadProfile(model = null) {
    if (!model && !this.currentModel) {
      await this.detectModel();
    }
    
    const modelToLoad = model || this.currentModel;
    if (!modelToLoad) {
      throw new Error('No model detected or provided');
    }
    
    // Try exact match first
    let profile = await this.storage.getModelProfile(modelToLoad.fullModel);
    
    if (!profile) {
      // Try fuzzy matching against existing profiles
      profile = await this.findBestMatchingProfile(modelToLoad);
      
      if (!profile) {
        // No match found - check if user wants to select manually
        const manualSelection = await this.promptForManualSelection(modelToLoad);
        
        if (manualSelection) {
          profile = manualSelection;
        } else {
          // Create default profile and mark for research
          profile = await this.createDefaultProfile(modelToLoad);
          
          // Queue for LLM research
          await this.queueForResearch(modelToLoad);
        }
      }
    }
    
    this.currentProfile = profile;
    return profile;
  }

  /**
   * Find best matching profile using fuzzy matching
   */
  async findBestMatchingProfile(model) {
    // Get all existing profiles
    const allProfiles = await this.storage.getAllModelProfiles();
    if (!allProfiles || allProfiles.length === 0) {
      return null;
    }
    
    // Find matches with 80% threshold
    const matches = this.modelMatcher.findBestMatches(
      model.fullModel,
      allProfiles,
      80
    );
    
    if (matches.length > 0 && matches[0].matchType !== 'weak') {
      console.log(`Found matching profile: ${matches[0].model.fullModel} (${matches[0].score}% match)`);
      return matches[0].model;
    }
    
    return null;
  }

  /**
   * Prompt user for manual model selection
   */
  async promptForManualSelection(detectedModel) {
    // Get all existing profiles for fuzzy matching
    const allProfiles = await this.storage.getAllModelProfiles();
    
    // Find potential matches
    const matches = this.modelMatcher.findBestMatches(
      detectedModel.fullModel,
      allProfiles,
      60  // Lower threshold for manual selection
    );
    
    // Emit event for UI to handle
    this.emit('manualSelectionRequired', {
      detectedModel,
      matches,
      callback: (selectedModel) => {
        this.manualSelectionCallback = selectedModel;
      }
    });
    
    // Wait for user selection
    return new Promise((resolve) => {
      const checkInterval = setInterval(() => {
        if (this.manualSelectionCallback !== undefined) {
          clearInterval(checkInterval);
          const result = this.manualSelectionCallback;
          this.manualSelectionCallback = undefined;
          resolve(result);
        }
      }, 100);
      
      // Timeout after 60 seconds
      setTimeout(() => {
        clearInterval(checkInterval);
        if (this.manualSelectionCallback === undefined) {
          console.log('Manual selection timed out');
          resolve(null);
        }
      }, 60000);
    });
  }

  /**
   * Create default profile with standard assumptions
   */
  async createDefaultProfile(model) {
    const profile = {
      id: `${model.manufacturer}-${model.model}`.toLowerCase().replace(/\s+/g, '-'),
      manufacturer: model.manufacturer,
      model: model.model,
      fullModel: model.fullModel,
      specifications: {
        // Default assumptions - will be updated after research
        usb_ports: {
          total: 3,  // Most laptops have 2-4 USB ports
          usb2: 1,
          usb3: 2,
          usbc: 0,
          locations: {
            left: 2,
            right: 1,
            back: 0
          }
        },
        display: {
          type: 'LCD',
          touchscreen: false,
          resolution: '1920x1080',  // Most common
          size: '15.6'
        },
        keyboard: {
          layout: 'US',
          numpad: model.fullModel.includes('15') || model.fullModel.includes('17'),
          backlight: false,
          total_keys: 85  // Standard without numpad
        },
        trackpad: {
          type: 'standard',
          buttons: 2,
          has_trackpoint: model.manufacturer.toLowerCase().includes('dell') || 
                          model.manufacturer.toLowerCase().includes('lenovo')
        },
        camera: {
          type: 'standard',  // or 'IR' for Windows Hello
          resolution: '720p'
        },
        audio: {
          speakers: 2,
          microphone: true,
          audio_jack: true
        },
        power: {
          dc_jack: true,
          usbc_charging: false,
          wattage: 65  // Common for most laptops
        },
        network: {
          ethernet: true,
          wifi: true,
          bluetooth: true
        },
        battery: {
          removable: false,
          cells: 4
        }
      },
      test_config: {
        // Test-specific configurations
        keyboard_test: {
          layout_map: 'standard_us',
          special_keys: ['Fn', 'Windows', 'Alt', 'Ctrl'],
          skip_numpad: !model.fullModel.includes('15') && !model.fullModel.includes('17')
        },
        usb_test: {
          expected_ports: 3,
          test_sequence: ['left-1', 'left-2', 'right-1'],
          require_usb3_speed: true
        },
        display_test: {
          test_patterns: ['white', 'black', 'red', 'green', 'blue'],
          check_brightness_levels: true,
          touchscreen_test: false
        },
        physical_inspection: {
          check_parts: ['A', 'B', 'C', 'D'],
          check_ports: true,
          check_hinges: true
        }
      },
      research_status: 'pending',  // pending, researched, verified
      last_updated: new Date().toISOString()
    };
    
    // Save to database
    await this.storage.saveModelProfile(profile);
    
    return profile;
  }

  /**
   * Queue model for LLM research
   */
  async queueForResearch(model) {
    // This would be implemented to:
    // 1. Use LLM to search for model specifications
    // 2. Parse manufacturer spec sheets
    // 3. Update the profile with accurate data
    
    await this.storage.addToSyncQueue('model_research', model.fullModel, 'research', {
      manufacturer: model.manufacturer,
      model: model.model,
      fullModel: model.fullModel,
      priority: 'high'
    });
    
    console.log(`Queued ${model.fullModel} for specification research`);
  }

  /**
   * Get test configuration for a specific module
   */
  getTestConfig(moduleId) {
    if (!this.currentProfile) {
      throw new Error('No model profile loaded');
    }
    
    const testConfigs = this.currentProfile.test_config;
    
    switch(moduleId) {
      case 'keyboard-test':
        return {
          ...testConfigs.keyboard_test,
          totalKeys: this.currentProfile.specifications.keyboard.total_keys,
          hasBacklight: this.currentProfile.specifications.keyboard.backlight
        };
        
      case 'usb-test':
        return {
          ...testConfigs.usb_test,
          modelPortCount: this.currentProfile.specifications.usb_ports.total,
          portTypes: this.currentProfile.specifications.usb_ports,
          portLocations: this.currentProfile.specifications.usb_ports.locations
        };
        
      case 'display-test':
        return {
          ...testConfigs.display_test,
          displayType: this.currentProfile.specifications.display.type,
          resolution: this.currentProfile.specifications.display.resolution,
          hasTouchscreen: this.currentProfile.specifications.display.touchscreen
        };
        
      case 'audio-test':
        return {
          hasSpeakers: this.currentProfile.specifications.audio.speakers > 0,
          speakerCount: this.currentProfile.specifications.audio.speakers,
          hasMicrophone: this.currentProfile.specifications.audio.microphone,
          hasAudioJack: this.currentProfile.specifications.audio.audio_jack
        };
        
      case 'camera-test':
        return {
          cameraType: this.currentProfile.specifications.camera.type,
          expectedResolution: this.currentProfile.specifications.camera.resolution,
          hasIRCamera: this.currentProfile.specifications.camera.type === 'IR'
        };
        
      case 'power-test':
        return {
          hasDCJack: this.currentProfile.specifications.power.dc_jack,
          hasUSBCCharging: this.currentProfile.specifications.power.usbc_charging,
          expectedWattage: this.currentProfile.specifications.power.wattage
        };
        
      case 'trackpad-test':
        return {
          trackpadType: this.currentProfile.specifications.trackpad.type,
          buttonCount: this.currentProfile.specifications.trackpad.buttons,
          hasTrackpoint: this.currentProfile.specifications.trackpad.has_trackpoint
        };
        
      default:
        return {};
    }
  }

  /**
   * Update profile with researched data
   */
  async updateProfile(modelName, specifications) {
    const profile = await this.storage.getModelProfile(modelName);
    if (!profile) {
      throw new Error('Model profile not found');
    }
    
    // Merge new specifications
    profile.specifications = {
      ...profile.specifications,
      ...specifications
    };
    
    // Update test configurations based on new specs
    profile.test_config = this.generateTestConfig(profile.specifications);
    
    profile.research_status = 'researched';
    profile.last_updated = new Date().toISOString();
    
    await this.storage.saveModelProfile(profile);
    
    return profile;
  }

  /**
   * Generate test configuration from specifications
   */
  generateTestConfig(specs) {
    return {
      keyboard_test: {
        layout_map: specs.keyboard.layout.toLowerCase() + '_layout',
        special_keys: this.getSpecialKeys(specs.manufacturer),
        skip_numpad: !specs.keyboard.numpad,
        test_backlight: specs.keyboard.backlight
      },
      usb_test: {
        expected_ports: specs.usb_ports.total,
        test_sequence: this.generateUSBTestSequence(specs.usb_ports.locations),
        require_usb3_speed: specs.usb_ports.usb3 > 0,
        test_usbc: specs.usb_ports.usbc > 0
      },
      display_test: {
        test_patterns: ['white', 'black', 'red', 'green', 'blue'],
        check_brightness_levels: true,
        touchscreen_test: specs.display.touchscreen,
        expected_resolution: specs.display.resolution
      },
      audio_test: {
        test_speakers: specs.audio.speakers > 0,
        speaker_channels: specs.audio.speakers,
        test_microphone: specs.audio.microphone,
        test_audio_jack: specs.audio.audio_jack
      },
      camera_test: {
        expected_type: specs.camera.type,
        test_ir: specs.camera.type === 'IR',
        expected_resolution: specs.camera.resolution
      },
      power_test: {
        test_dc_jack: specs.power.dc_jack,
        test_usbc_charging: specs.power.usbc_charging,
        expected_wattage: specs.power.wattage
      },
      physical_inspection: {
        check_parts: ['A', 'B', 'C', 'D'],
        check_rubber_feet: true,
        check_port_covers: true,
        model_specific_checks: this.getModelSpecificChecks(specs)
      }
    };
  }

  /**
   * Get special keys based on manufacturer
   */
  getSpecialKeys(manufacturer) {
    const lower = manufacturer.toLowerCase();
    const baseKeys = ['Fn', 'Windows', 'Alt', 'Ctrl', 'Shift'];
    
    if (lower.includes('dell')) {
      return [...baseKeys, 'Dell', 'F1-F12'];
    } else if (lower.includes('hp')) {
      return [...baseKeys, 'HP', 'F1-F12'];
    } else if (lower.includes('lenovo')) {
      return [...baseKeys, 'ThinkVantage', 'F1-F12'];
    }
    
    return baseKeys;
  }

  /**
   * Generate USB test sequence based on port locations
   */
  generateUSBTestSequence(locations) {
    const sequence = [];
    
    if (locations.left > 0) {
      for (let i = 1; i <= locations.left; i++) {
        sequence.push(`left-${i}`);
      }
    }
    
    if (locations.right > 0) {
      for (let i = 1; i <= locations.right; i++) {
        sequence.push(`right-${i}`);
      }
    }
    
    if (locations.back > 0) {
      for (let i = 1; i <= locations.back; i++) {
        sequence.push(`back-${i}`);
      }
    }
    
    return sequence;
  }

  /**
   * Get model-specific physical checks
   */
  getModelSpecificChecks(specs) {
    const checks = [];
    
    if (specs.battery.removable) {
      checks.push('battery-latch');
    }
    
    if (specs.display.touchscreen) {
      checks.push('screen-bezel-integrity');
    }
    
    if (specs.network.ethernet) {
      checks.push('ethernet-port-flap');
    }
    
    return checks;
  }
}

module.exports = ModelProfile;