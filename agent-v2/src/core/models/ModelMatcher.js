/**
 * Model Fuzzy Matching Utility
 * Handles variations in laptop model names like "HP EliteBook 840 G5" vs "840G5"
 */
class ModelMatcher {
  constructor() {
    // Common manufacturer aliases
    this.manufacturerAliases = {
      'hp': ['hewlett-packard', 'hewlett packard', 'hp inc', 'hp inc.'],
      'dell': ['dell inc', 'dell inc.', 'dell computer'],
      'lenovo': ['lenovo inc', 'lenovo inc.', 'ibm'],
      'asus': ['asustek', 'asus computer'],
      'acer': ['acer inc', 'acer inc.'],
      'msi': ['micro-star', 'msi inc'],
      'toshiba': ['toshiba corp', 'toshiba corporation'],
      'samsung': ['samsung electronics'],
      'lg': ['lg electronics'],
      'microsoft': ['microsoft corp', 'microsoft corporation']
    };
    
    // Common model series patterns
    this.seriesPatterns = {
      'hp': {
        'elitebook': /elite\s*book/i,
        'probook': /pro\s*book/i,
        'zbook': /z\s*book/i,
        'pavilion': /pavilion/i,
        'envy': /envy/i,
        'spectre': /spectre/i
      },
      'dell': {
        'latitude': /latitude/i,
        'precision': /precision/i,
        'inspiron': /inspiron/i,
        'xps': /xps/i,
        'vostro': /vostro/i,
        'alienware': /alienware/i
      },
      'lenovo': {
        'thinkpad': /think\s*pad/i,
        'ideapad': /idea\s*pad/i,
        'yoga': /yoga/i,
        'legion': /legion/i
      }
    };
  }

  /**
   * Find best matching models from a list
   * @param {string} input - User input model name
   * @param {Array} modelList - List of known models
   * @param {number} threshold - Minimum similarity score (0-100)
   * @returns {Array} Sorted array of matches with scores
   */
  findBestMatches(input, modelList, threshold = 70) {
    const normalizedInput = this.normalizeModelName(input);
    const matches = [];
    
    for (const model of modelList) {
      const normalizedModel = this.normalizeModelName(model.fullModel || model);
      const score = this.calculateSimilarity(normalizedInput, normalizedModel);
      
      if (score >= threshold) {
        matches.push({
          model: model,
          score: score,
          matchType: this.getMatchType(normalizedInput, normalizedModel)
        });
      }
    }
    
    // Sort by score descending
    return matches.sort((a, b) => b.score - a.score);
  }

  /**
   * Normalize model name for comparison
   */
  normalizeModelName(name) {
    if (!name) return '';
    
    // Convert to lowercase
    let normalized = name.toLowerCase();
    
    // Remove common suffixes/prefixes
    normalized = normalized.replace(/\s*(laptop|notebook|computer|pc)\s*/g, ' ');
    
    // Normalize spaces
    normalized = normalized.replace(/\s+/g, ' ').trim();
    
    // Extract key components
    const components = this.extractModelComponents(normalized);
    
    return components.join(' ');
  }

  /**
   * Extract key components from model name
   */
  extractModelComponents(modelName) {
    const components = [];
    
    // Extract manufacturer
    const manufacturer = this.extractManufacturer(modelName);
    if (manufacturer) {
      components.push(manufacturer);
    }
    
    // Extract series
    const series = this.extractSeries(modelName, manufacturer);
    if (series) {
      components.push(series);
    }
    
    // Extract model number (e.g., 840, 7490, X1)
    const modelNumbers = modelName.match(/\b\d{3,4}[a-z]?\b/gi) || [];
    components.push(...modelNumbers.map(n => n.toLowerCase()));
    
    // Extract generation (e.g., G5, Gen 9, 9th Gen)
    const generation = this.extractGeneration(modelName);
    if (generation) {
      components.push(generation);
    }
    
    return components;
  }

  /**
   * Extract manufacturer from model name
   */
  extractManufacturer(modelName) {
    // Check direct matches
    for (const [canonical, aliases] of Object.entries(this.manufacturerAliases)) {
      if (modelName.includes(canonical)) {
        return canonical;
      }
      for (const alias of aliases) {
        if (modelName.includes(alias.toLowerCase())) {
          return canonical;
        }
      }
    }
    
    // Check for manufacturer at start of string
    const firstWord = modelName.split(' ')[0];
    for (const manufacturer of Object.keys(this.manufacturerAliases)) {
      if (firstWord === manufacturer) {
        return manufacturer;
      }
    }
    
    return null;
  }

  /**
   * Extract series from model name
   */
  extractSeries(modelName, manufacturer) {
    if (!manufacturer || !this.seriesPatterns[manufacturer]) {
      return null;
    }
    
    for (const [series, pattern] of Object.entries(this.seriesPatterns[manufacturer])) {
      if (pattern.test(modelName)) {
        return series;
      }
    }
    
    return null;
  }

  /**
   * Extract generation identifier
   */
  extractGeneration(modelName) {
    // Match patterns like G5, G6, Gen 9, 9th Gen
    const patterns = [
      /\bg(\d+)\b/i,           // G5, G6
      /\bgen\s*(\d+)\b/i,      // Gen 9
      /\b(\d+)(?:st|nd|rd|th)\s+gen\b/i  // 9th Gen
    ];
    
    for (const pattern of patterns) {
      const match = modelName.match(pattern);
      if (match) {
        return `g${match[1]}`;
      }
    }
    
    return null;
  }

  /**
   * Calculate similarity score between two normalized model names
   */
  calculateSimilarity(input, model) {
    // Split into components
    const inputParts = input.split(' ');
    const modelParts = model.split(' ');
    
    // Calculate different similarity metrics
    const exactMatches = this.countExactMatches(inputParts, modelParts);
    const partialMatches = this.countPartialMatches(inputParts, modelParts);
    const orderSimilarity = this.calculateOrderSimilarity(inputParts, modelParts);
    const lengthSimilarity = this.calculateLengthSimilarity(inputParts, modelParts);
    
    // Weighted combination
    const score = (
      exactMatches * 40 +
      partialMatches * 30 +
      orderSimilarity * 20 +
      lengthSimilarity * 10
    );
    
    return Math.min(100, Math.round(score));
  }

  /**
   * Count exact matching components
   */
  countExactMatches(input, model) {
    let matches = 0;
    const maxPossible = Math.max(input.length, model.length);
    
    for (const part of input) {
      if (model.includes(part)) {
        matches++;
      }
    }
    
    return matches / maxPossible;
  }

  /**
   * Count partial matches (substring matching)
   */
  countPartialMatches(input, model) {
    let matches = 0;
    const maxPossible = Math.max(input.length, model.length);
    
    for (const inputPart of input) {
      for (const modelPart of model) {
        if (inputPart.length >= 3 && modelPart.length >= 3) {
          if (inputPart.includes(modelPart) || modelPart.includes(inputPart)) {
            matches += 0.5;
            break;
          }
        }
      }
    }
    
    return Math.min(1, matches / maxPossible);
  }

  /**
   * Calculate order similarity (are components in same order)
   */
  calculateOrderSimilarity(input, model) {
    let orderScore = 0;
    let lastIndex = -1;
    
    for (const part of input) {
      const index = model.indexOf(part);
      if (index > lastIndex) {
        orderScore++;
        lastIndex = index;
      }
    }
    
    return orderScore / input.length;
  }

  /**
   * Calculate length similarity
   */
  calculateLengthSimilarity(input, model) {
    const diff = Math.abs(input.length - model.length);
    const max = Math.max(input.length, model.length);
    return 1 - (diff / max);
  }

  /**
   * Determine match type for user feedback
   */
  getMatchType(input, model) {
    const inputParts = input.split(' ');
    const modelParts = model.split(' ');
    
    // Exact match
    if (input === model) {
      return 'exact';
    }
    
    // All components present
    const allPresent = inputParts.every(part => 
      modelParts.some(mPart => mPart.includes(part) || part.includes(mPart))
    );
    
    if (allPresent) {
      return 'strong';
    }
    
    // Key components match (manufacturer + model number)
    const hasManufacturer = inputParts.some(part => 
      this.extractManufacturer(part) !== null
    );
    const hasModelNumber = inputParts.some(part => /\d{3,}/.test(part));
    
    if (hasManufacturer && hasModelNumber) {
      return 'moderate';
    }
    
    return 'weak';
  }

  /**
   * Create a manual selection UI for models
   */
  createManualSelectionUI(matches, manufacturer = null) {
    // Group by manufacturer if not specified
    const grouped = {};
    
    for (const match of matches) {
      const mfg = match.model.manufacturer || 'Unknown';
      if (!grouped[mfg]) {
        grouped[mfg] = [];
      }
      grouped[mfg].push(match);
    }
    
    // If manufacturer specified, only show those
    if (manufacturer) {
      return grouped[manufacturer] || [];
    }
    
    return grouped;
  }
}

module.exports = ModelMatcher;