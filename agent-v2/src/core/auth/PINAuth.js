const EventEmitter = require('events');
const crypto = require('crypto');

/**
 * PIN-based authentication for quick user login
 */
class PINAuth extends EventEmitter {
  constructor(storage) {
    super();
    this.storage = storage;
    this.currentSession = null;
    this.sessionTimeout = 30 * 60 * 1000; // 30 minutes
    this.sessionTimer = null;
  }

  /**
   * Authenticate user with PIN
   * @param {string} pin - 4-6 digit PIN
   * @param {object} options - Additional options
   */
  async authenticate(pin, options = {}) {
    try {
      // Validate PIN format
      if (!this.validatePIN(pin)) {
        throw new Error('Invalid PIN format. Must be 4-6 digits.');
      }

      // For demo/development, accept certain PINs
      // In production, this would validate against a user database
      const validUsers = {
        '1234': { name: 'Demo User', role: 'technician' },
        '4321': { name: 'Senior Tech', role: 'senior' },
        '9999': { name: 'Admin User', role: 'admin' },
        '1111': { name: 'Test User 1', role: 'technician' },
        '2222': { name: 'Test User 2', role: 'technician' }
      };

      const user = validUsers[pin];
      if (!user) {
        throw new Error('Invalid PIN');
      }

      // End any existing session
      if (this.currentSession) {
        await this.endSession();
      }

      // Create new session
      const sessionId = await this.storage.createUserSession(pin, user.name);
      
      this.currentSession = {
        id: sessionId,
        pin: pin,
        userName: user.name,
        role: user.role,
        loginTime: new Date(),
        lastActivity: new Date()
      };

      // Set session timeout
      this.resetSessionTimeout();

      this.emit('authenticated', {
        sessionId,
        userName: user.name,
        role: user.role
      });

      return {
        success: true,
        sessionId,
        user: {
          name: user.name,
          role: user.role
        }
      };
    } catch (error) {
      this.emit('authenticationFailed', { error: error.message });
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Validate PIN format
   */
  validatePIN(pin) {
    return /^\d{4,6}$/.test(pin);
  }

  /**
   * Get current session
   */
  getCurrentSession() {
    if (!this.currentSession) {
      return null;
    }

    // Check if session is still valid
    const now = new Date();
    const timeSinceActivity = now - this.currentSession.lastActivity;
    
    if (timeSinceActivity > this.sessionTimeout) {
      this.endSession();
      return null;
    }

    return {
      id: this.currentSession.id,
      userName: this.currentSession.userName,
      role: this.currentSession.role,
      loginTime: this.currentSession.loginTime,
      timeRemaining: Math.floor((this.sessionTimeout - timeSinceActivity) / 1000)
    };
  }

  /**
   * Update session activity
   */
  updateActivity() {
    if (this.currentSession) {
      this.currentSession.lastActivity = new Date();
      this.resetSessionTimeout();
    }
  }

  /**
   * Reset session timeout
   */
  resetSessionTimeout() {
    if (this.sessionTimer) {
      clearTimeout(this.sessionTimer);
    }

    this.sessionTimer = setTimeout(() => {
      this.endSession();
    }, this.sessionTimeout);
  }

  /**
   * End current session
   */
  async endSession() {
    if (!this.currentSession) {
      return;
    }

    try {
      await this.storage.endUserSession(this.currentSession.id);
      
      const sessionInfo = {
        sessionId: this.currentSession.id,
        userName: this.currentSession.userName,
        duration: new Date() - this.currentSession.loginTime
      };

      this.currentSession = null;

      if (this.sessionTimer) {
        clearTimeout(this.sessionTimer);
        this.sessionTimer = null;
      }

      this.emit('sessionEnded', sessionInfo);
    } catch (error) {
      console.error('Error ending session:', error);
    }
  }

  /**
   * Check if user has permission for an action
   */
  hasPermission(action) {
    if (!this.currentSession) {
      return false;
    }

    const permissions = {
      technician: [
        'run_tests',
        'view_results',
        'update_status',
        'print_labels'
      ],
      senior: [
        'run_tests',
        'view_results',
        'update_status',
        'print_labels',
        'manual_entry',
        'handle_exceptions',
        'modify_tests'
      ],
      admin: [
        'run_tests',
        'view_results',
        'update_status',
        'print_labels',
        'manual_entry',
        'handle_exceptions',
        'modify_tests',
        'manage_users',
        'view_analytics',
        'system_config'
      ]
    };

    const userPermissions = permissions[this.currentSession.role] || [];
    return userPermissions.includes(action);
  }

  /**
   * Require authentication for an action
   */
  requireAuth(action = null) {
    const session = this.getCurrentSession();
    
    if (!session) {
      throw new Error('Authentication required');
    }

    if (action && !this.hasPermission(action)) {
      throw new Error(`Permission denied for action: ${action}`);
    }

    return session;
  }

  /**
   * Get session info for display
   */
  getSessionDisplay() {
    const session = this.getCurrentSession();
    
    if (!session) {
      return {
        isAuthenticated: false,
        message: 'Not logged in'
      };
    }

    const minutes = Math.floor(session.timeRemaining / 60);
    const seconds = session.timeRemaining % 60;

    return {
      isAuthenticated: true,
      userName: session.userName,
      role: session.role,
      timeRemaining: `${minutes}:${seconds.toString().padStart(2, '0')}`
    };
  }

  /**
   * Quick re-authentication with same PIN
   */
  async quickReauth(pin) {
    if (!this.currentSession || this.currentSession.pin !== pin) {
      return { success: false, error: 'Invalid PIN for quick re-authentication' };
    }

    this.updateActivity();
    
    return {
      success: true,
      message: 'Session extended'
    };
  }

  /**
   * Initialize from stored session
   */
  async initializeFromStorage() {
    try {
      const activeSession = await this.storage.getActiveUserSession();
      
      if (activeSession) {
        const now = new Date();
        const loginTime = new Date(activeSession.login_time);
        const timeSinceLogin = now - loginTime;
        
        if (timeSinceLogin < this.sessionTimeout) {
          this.currentSession = {
            id: activeSession.id,
            pin: activeSession.user_pin,
            userName: activeSession.user_name,
            role: 'technician', // Default role, would come from user DB
            loginTime: loginTime,
            lastActivity: now
          };
          
          this.resetSessionTimeout();
          
          this.emit('sessionRestored', {
            sessionId: activeSession.id,
            userName: activeSession.user_name
          });
          
          return true;
        } else {
          // Session expired, end it
          await this.storage.endUserSession(activeSession.id);
        }
      }
    } catch (error) {
      console.error('Error initializing from storage:', error);
    }
    
    return false;
  }
}

module.exports = PINAuth;