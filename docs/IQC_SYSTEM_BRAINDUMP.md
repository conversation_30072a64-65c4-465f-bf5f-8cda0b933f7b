# IQC System Brain Dump - Comprehensive Documentation
**Date**: December 17, 2024
**Purpose**: Complete capture of all requirements, ideas, and implementation details discussed for the laptop refurbishment QC system expansion

## 1. Project Context & Current State

### Existing System
- **Battery Testing**: 80-90% complete and satisfactory
- **Agent**: v1.7 with embedded credentials, timer-based battery testing
- **Dashboard**: Professional card-based UI with real-time updates
- **Database**: Supabase with PostgreSQL + Realtime
- **Deployment**: Vercel (Frontend), FOG/PXE + USB (Agent)
- **Power Control**: 30-channel Ethernet Relay Module (Modbus built but not implemented)

### Business Context
- Refurbishing company processing 200-unit lots
- Current scale: 50/day → Target: 100/day (3000/month)
- 10-20 laptop models per month (HP EliteBook, ProBook, Dell Latitude, etc.)
- Workers: Non-technical, "labor kind of people" who need simple instructions

## 2. IQC (Input Quality Control) Requirements

### Purpose
- Quick assessment of incoming laptop batches
- Identify defects early (LCD broken, keyboard faulty, dead units)
- Generate overview statistics (e.g., 10% LCD broken, 30% need keyboard replacement)
- 6 people assigned per 200-unit lot

### Boot Options for IQC
1. **USB Quick Boot** - Lightweight Linux/Windows PE
2. **PXE Boot** - Via FOG server (being set up)
3. **Existing SSD** - If laptop has SSD installed (20-30% cases)
4. **Manual Entry** - For dead/non-booting units (10-20% expected)

### Hardware Scenarios
- 70% laptops arrive without RAM/SSD
- 20-30% have RAM or SSD (or both)
- Some units BIOS locked (corporate decommissioned)
- BIOS unlock creates duplicate serial/MAC issues

## 3. Detailed Test Requirements

### 3.1 System Information Capture
- **Automatic Capture** (locked after first boot):
  - Serial number
  - MAC address (may come from LAN IC or BIOS)
  - Processor model (i3/i5/i7/i9) - important for mixed lots
  - RAM serial number (distinguish original vs test RAM)
  - SSD serial number (if present)
- **UID Assignment**: Dynamic prefix + number (user-adjustable)

### 3.2 RAM & SSD Management
- **Test RAM Scenario**: Engineer has one test RAM for multiple units
  - System must detect if RAM serial matches "test RAM"
  - Flag original RAM vs test RAM in database
- **SSD Tracking**:
  - Record original SSD serial if found
  - May remove high-capacity SSDs for separate use
  - Track "first seen in" laptop association

### 3.3 LCD Testing
**Test Categories**:
1. **Perfect Condition** - No issues
2. **Lines** - Vertical or horizontal (replacement needed)
3. **Patches** - Dead pixels or internal patches (replacement needed)
4. **Keyboard Marks** - Surface marks only (can be refurbished)
5. **Broken/Cracked** - Physical damage

**Broken LCD Workflow**:
- Option to test via HDMI/VGA output
- Allows testing other components despite broken screen
- LCD refurbishment option (replace polarizer film)

### 3.4 Keyboard Testing
**Requirements**:
- Model-specific keyboard layouts (pre-researched)
- Visual representation on screen
- Track keys pressed/not pressed
- Backlight test (if applicable)
- Count missing keys
- Count broken locks (not repairable)
- Open-source library integration needed

### 3.5 Trackpad/Trackball Testing
**Configurations**:
- Standard trackpad (2 buttons)
- Dell Latitude style (4 buttons - 2 trackpad, 2 trackball)
- Test all zones and buttons
- Gesture recognition
- Trackball rotation (if present)

### 3.6 USB Port Testing
**Requirements**:
- Model-specific port count (from hardware database)
- Test each port for:
  - Power delivery
  - Data transfer
  - USB 2.0 vs 3.0 compatibility
  - Type-C functionality
- Physical USB tester device needed
- Common issue: USB works at 2.0 but not 3.0 speeds

### 3.7 Power Input Testing
**Two Types**:
1. **DC Barrel Jack**:
   - Check power LED indicator
   - Verify charging functionality
2. **Type-C Charging**:
   - If model supports Type-C power
   - Test power delivery negotiation

### 3.8 Camera Testing
- Detect standard vs IR camera
- Capture test image
- Store compressed proof (minimal database storage)
- Simple working/not working status

### 3.9 Audio Testing
**Microphone**:
- Record 5-second test clip
- Store low-bitrate proof (10KB max)

**Speakers**:
- Test left and right channels separately
- Check for crackling/damage
- Simple pass/fail

### 3.10 Physical Body Inspection
**Part Nomenclature**:
- **A-Part**: LCD back cover (where logo is)
- **B-Part**: LCD bezel
- **C-Part**: Keyboard/trackpad area
- **D-Part**: Bottom base

**Condition Options** (for A, C, D parts):
1. Perfect
2. Paint Required
3. Damaged
4. Needs Replacement

**B-Part** (Bezel - cannot be painted):
1. OK
2. Damaged
3. Needs Replacement

**Additional Checks**:
- D-Part rubber feet condition
- Stickers condition
- Trackpad as separate component (if applicable)

## 4. Multi-Stage Testing Framework

### Test Stages
1. **IQC** - Initial input quality control
2. **Mid-QC** - After repairs/painting
3. **Final QC** - Before shipping

### Reusable Tests
- Same tests run at different intensities
- IQC: Quick functional checks
- Final QC: Comprehensive testing
- Track which tests passed/failed at each stage

## 5. User Management & Authentication

### Requirements
- Quick login system (no typing passwords repeatedly)
- Track who performed each test
- Session persistence (30-minute timeout)

### Options Considered
1. **PIN-based** (4-6 digits) - Recommended
2. **Face detection** - Too complex, camera not always accessible
3. **RFID/NFC badges** - Future option

### User Roles
- **Technician**: Run tests, update status
- **Senior Tech**: Handle dead units, BIOS issues, manual entries
- **Admin**: View analytics, manage users

## 6. Workflow Processes

### Laptop Lifecycle
1. **Receive** → IQC
2. **Repair Decisions** → Parts ordering
3. **Disassembly** (if needed)
4. **Painting** (if needed)
5. **Part Replacement** (LCD, keyboard, etc.)
6. **Reassembly**
7. **Windows Installation** (via FOG)
8. **Battery Testing**
9. **Final QC**
10. **Labeling & Shipping**

### Dead Laptop Handling
- 10-20% expected dead on arrival
- Senior tech logs manually in dashboard
- System assigns UID for tracking
- Serial/MAC captured later when repaired

## 7. Label Printing Integration

### Requirements
- Zebra thermal printers (ZPL commands)
- Network-connected printers at each station
- Label size: 30mm height × 70mm width

### Label Contents
- UID (primary identifier)
- Serial number
- Test date
- Tester ID
- Lot number
- QC status

### Existing Experience
- User has implemented label printing in another project
- Template designer with dynamic field mapping
- Convert to ZPL for printing

## 8. Hardware Parts Tracking (Future Luxury Feature)

### Component Registration
- Scan/record serial numbers for:
  - LCD panels
  - Batteries
  - RAM modules
  - SSDs
  - Motherboards
- Track part movement between laptops
- "This LCD came from laptop X, now in laptop Y"

### Image Documentation
- Photos at key stages:
  - IQC condition
  - Before painting
  - After painting
  - Final condition
- Store in Supabase with laptop UID

## 9. Model-Specific Hardware Database

### Information to Research/Store
- USB port count and types
- Camera type (standard/IR)
- Display type (touch/non-touch)
- Power options (barrel/Type-C)
- Keyboard layout and key count
- Trackpad configuration
- LAN port availability
- Discrete graphics (for stress testing)
- Special features

### Implementation
- Use LLM to research model specifications
- Build database of 20-30 common models
- Auto-load test template based on detected model

## 10. Implementation Priorities

### Immediate (Week 1-2)
1. IQC test suite in existing agent
2. Manual entry for dead units
3. Basic model detection
4. RAM/SSD serial capture

### Short-term (Week 3-4)
1. Model-specific profiles (5-10 models)
2. Quick PIN authentication
3. Visual keyboard/trackpad testers
4. Physical inspection checklist

### Medium-term (Month 2)
1. Label printing integration
2. USB 3.0 speed testing
3. Camera/audio capture
4. Advanced analytics dashboard

### Long-term (Month 3+)
1. Full parts tracking system
2. Image documentation
3. AI-powered defect detection
4. Direct WebSocket (agent-dashboard)

## 11. Technical Architecture Decisions

### Agent-First Approach (Recommended)
**Reasons**:
- Workers use laptop being tested directly
- No need for separate PCs at workstations
- Step-by-step visual guidance
- Automatic data capture
- Immediate feedback

**Agent Handles** (80-90%):
- Display test instructions
- Capture hardware automatically
- Run automated tests
- Show PASS/FAIL clearly
- Submit to database
- Trigger label printing

**Dashboard Handles** (10-20%):
- Dead laptop manual entry
- Batch overview/analytics
- Senior tech interventions
- Test configuration
- Real-time monitoring

## 12. Database Schema Extensions

### New Tables Needed
```sql
-- Model specifications
CREATE TABLE laptop_models (
  id UUID PRIMARY KEY,
  manufacturer VARCHAR(50),
  model_number VARCHAR(100),
  usb_ports JSONB,
  camera_type VARCHAR(50),
  display_type VARCHAR(50),
  power_options JSONB,
  keyboard_layout VARCHAR(50),
  trackpad_config JSONB,
  has_lan_port BOOLEAN,
  has_discrete_gpu BOOLEAN,
  special_features JSONB
);

-- IQC test results
CREATE TABLE iqc_test_results (
  id UUID PRIMARY KEY,
  laptop_id UUID REFERENCES laptops(id),
  test_type VARCHAR(50),
  result VARCHAR(20),
  details JSONB,
  tested_by VARCHAR(100),
  tested_at TIMESTAMP,
  test_stage VARCHAR(20) -- 'iqc', 'mid_qc', 'final_qc'
);

-- Parts tracking
CREATE TABLE laptop_parts (
  id UUID PRIMARY KEY,
  part_type VARCHAR(50),
  serial_number VARCHAR(200),
  first_seen_laptop_id UUID,
  current_laptop_id UUID,
  condition VARCHAR(50),
  notes TEXT
);
```

### Laptop Table Additions
```sql
ALTER TABLE laptops ADD COLUMN original_ram BOOLEAN DEFAULT FALSE;
ALTER TABLE laptops ADD COLUMN original_ssd BOOLEAN DEFAULT FALSE;
ALTER TABLE laptops ADD COLUMN ram_serial VARCHAR(100);
ALTER TABLE laptops ADD COLUMN ssd_serial VARCHAR(100);
ALTER TABLE laptops ADD COLUMN processor_model VARCHAR(50);
ALTER TABLE laptops ADD COLUMN part_a_condition VARCHAR(20);
ALTER TABLE laptops ADD COLUMN part_b_condition VARCHAR(20);
ALTER TABLE laptops ADD COLUMN part_c_condition VARCHAR(20);
ALTER TABLE laptops ADD COLUMN part_d_condition VARCHAR(20);
ALTER TABLE laptops ADD COLUMN keyboard_issues JSONB;
ALTER TABLE laptops ADD COLUMN lot_number VARCHAR(50);
ALTER TABLE laptops ADD COLUMN iqc_completed_at TIMESTAMP;
ALTER TABLE laptops ADD COLUMN iqc_completed_by VARCHAR(100);
```

## 13. UI/UX Considerations

### Agent Interface Principles
- Large, clear buttons
- Visual progress indicators
- Step-by-step guidance
- Color coding (Green=Pass, Red=Fail, Yellow=Warning)
- Minimal text, maximum visuals
- No complex navigation

### Dashboard Design
- Lot-level overview
- Real-time progress tracking
- Issue categorization
- Export capabilities
- Drill-down to individual laptops

## 14. Modbus Integration (Next After IQC)

### Current State
- ModbusRelayController class built
- 30-channel relay control ready
- Not yet integrated with main system

### Integration Plan
- Connect to battery testing workflow
- Automatic power cycling during tests
- Emergency stop functionality
- Bay assignment automation

## 15. Key Challenges & Solutions

### Challenge: Non-technical Workers
**Solution**: Visual-first interface with minimal text

### Challenge: Test RAM Management
**Solution**: Serial number comparison system

### Challenge: Dead Laptops
**Solution**: Dashboard manual entry by senior tech

### Challenge: BIOS Locked Units
**Solution**: Track duplicate serials, use UID as primary

### Challenge: Scale (100 laptops/day)
**Solution**: Parallel testing, efficient workflows

## 16. Future Enhancements

### AI Integration
- Defect detection from images
- Predictive failure analysis
- Optimal repair path suggestions

### Advanced Analytics
- Lot quality trends
- Technician performance metrics
- Cost analysis per repair type

### Automation
- Robotic testing stations
- Automated photography
- Direct API integration with suppliers

## 17. Summary of User's Vision

The user wants a comprehensive QC system that:
1. Handles laptops from receipt to shipping
2. Tracks every component and test
3. Works with non-technical staff
4. Scales to 3000 laptops/month
5. Maintains detailed history
6. Integrates with existing battery test system
7. Supports multiple test stages (IQC, Mid-QC, Final QC)
8. Enables quick lot assessment
9. Minimizes manual data entry
10. Provides real-time visibility

The system should be simple but comprehensive, focusing on guided workflows for workers while providing detailed analytics for management.

---
**End of Brain Dump**
**Next Steps**: Use this document to create refined implementation plan and task list