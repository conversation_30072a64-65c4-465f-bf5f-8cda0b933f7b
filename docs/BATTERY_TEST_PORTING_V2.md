# Battery Test Module Porting Guide: v1.8 to v2.0

**Created**: June 19, 2025  
**Purpose**: Comprehensive guide for implementing battery testing in Agent v2.0  
**Status**: Ready for implementation

## 📋 Executive Summary

Battery testing is a critical component that supports both manual and automatic testing modes. Unlike other hardware tests, battery testing involves multiple sessions per laptop (due to battery repairs/rewrites) and requires coordination between the agent, dashboard, and power control systems.

## 🏗️ Architecture Overview

### Current v1.8 Implementation
```
Dashboard → Database → Agent v1.8 → Manual Power Control
    ↓          ↓          ↓
WebSocket   Test      Battery
Updates    Session    Monitor
```

### Target v2.0 Architecture
```
Dashboard → Database → Agent v2.0 → Manual/Auto Power Control
    ↓          ↓          ↓              ↓
WebSocket   Offline    TestModule    Modbus/FOG
Updates     Storage    Framework     Integration
```

## 🔋 Battery Testing Modes

### 1. Manual Mode (Primary Implementation)
- **Current Status**: 90% working in v1.8
- **Use Case**: Existing Windows installation, manual power cable management
- **Flow**:
  1. Dashboard creates test session with configuration
  2. Agent detects new session and starts test
  3. Visual prompts guide user to connect/disconnect power
  4. Real-time graph shows charge/discharge curves
  5. Results stored locally, synced when online

### 2. Automatic Mode (Future Enhancement)
- **Status**: Infrastructure exists, not implemented
- **Use Case**: FOG deployment with Modbus relay control
- **Flow**:
  1. FOG installs Windows and deploys Agent v2.0
  2. Agent auto-starts battery test sequence
  3. Modbus relay controls power automatically
  4. No manual intervention required
  5. Results uploaded after completion

## 📊 Key Features to Port

### Core Test Logic
1. **Two-phase testing**:
   - Configurable discharge duration (1-120 minutes)
   - Configurable charge duration (1-120 minutes)
   - Option to start with charge or discharge

2. **Battery monitoring**:
   - 5-second reading intervals during active testing
   - 3-second intervals when waiting for power change
   - Capture percentage, voltage, temperature, health

3. **Session management**:
   - Support multiple test sessions per laptop
   - Resume existing sessions if agent restarts
   - Auto-complete stale sessions

4. **Health analysis**:
   - Calculate charge/discharge rates
   - Compare against expected rates
   - Generate pass/fail based on health score

### Visual Interface Requirements (NEW for v2.0)

1. **Real-time Battery Graph**:
   ```html
   <div class="battery-graph">
     <canvas id="battery-chart"></canvas>
     <div class="graph-legend">
       <span class="discharge-line">Discharge</span>
       <span class="charge-line">Charge</span>
     </div>
   </div>
   ```

2. **Phase Progress Indicator**:
   ```html
   <div class="phase-indicator">
     <div class="phase-icon">🔋</div>
     <div class="phase-name">Discharging</div>
     <div class="phase-progress">
       <div class="progress-bar" style="width: 45%"></div>
     </div>
     <div class="time-remaining">11:23 remaining</div>
   </div>
   ```

3. **Power State Visual**:
   ```html
   <div class="power-state">
     <div class="power-icon" data-state="battery">🔌</div>
     <div class="instruction">Please disconnect power cable</div>
   </div>
   ```

4. **Battery Health Summary**:
   ```html
   <div class="health-summary">
     <div class="health-score">85%</div>
     <div class="metrics">
       <div>Design: 4200 mWh</div>
       <div>Current: 3570 mWh</div>
       <div>Cycles: 245</div>
     </div>
   </div>
   ```

## 🔧 Implementation Plan

### Phase 1: Core Module Structure
Create `/agent-v2/src/modules/battery/BatteryTest.js`:

```javascript
const TestModule = require('../../core/framework/TestModule');
const si = require('systeminformation');
const Chart = require('chart.js'); // For visual graphs

class BatteryTest extends TestModule {
  constructor() {
    super({
      id: 'battery-test',
      name: 'Battery Test',
      description: 'Comprehensive battery charge/discharge testing',
      category: 'battery',
      estimatedTime: 40, // Default 20+20 minutes
      requiresUserInteraction: true,
      supportsSessions: true // NEW: Indicates multiple sessions support
    });
    
    this.currentPhase = null;
    this.phaseStartTime = null;
    this.readings = [];
    this.chart = null;
  }
  
  async onRunTest(config) {
    // Check for existing session from dashboard
    const existingSession = await this.checkExistingSession(config);
    if (existingSession) {
      return this.resumeSession(existingSession);
    }
    
    // Start new test with visual UI
    await this.initializeVisualUI();
    return this.startNewTest(config);
  }
  
  async initializeVisualUI() {
    this.emit('displayUI', {
      type: 'battery-test',
      content: this.generateBatteryTestUI()
    });
  }
}
```

### Phase 2: Dashboard Integration
1. **Session Detection**:
   - Monitor `test_sessions` table for new battery tests
   - Use existing session ID if available
   - Create local session if offline

2. **Real-time Updates**:
   - Store readings in offline storage first
   - Sync to Supabase when online
   - Update dashboard via existing WebSocket channels

3. **Power Control Integration**:
   - Detect if bridge service is available
   - Show appropriate UI (manual vs auto)
   - Future: Integrate Modbus for automatic control

### Phase 3: Visual Components
1. **Chart.js Integration**:
   - Real-time line graph for battery percentage
   - Dual-axis for charge/discharge phases
   - Smooth animations for data updates

2. **Interactive Controls**:
   - Pause/Resume test capability
   - Skip to next phase
   - Manual power state override

3. **Progress Visualization**:
   - Circular progress for overall test
   - Linear progress for current phase
   - ETA calculations

### Phase 4: Offline Storage
```javascript
// Store test data locally first
await this.storage.saveBatteryReading({
  session_id: this.sessionId,
  percentage: batteryInfo.percentage,
  voltage: batteryInfo.voltage,
  is_charging: batteryInfo.isCharging,
  timestamp: new Date().toISOString()
});

// Sync engine will handle upload
```

## 🔌 Integration Points

### 1. Dashboard Communication
- **Existing**: Via database polling and WebSocket subscriptions
- **Enhanced**: Add IPC events for immediate UI updates
- **Future**: Direct WebSocket connection to dashboard

### 2. Bridge Service
- **Endpoint**: `http://localhost:5200/api/power/control`
- **Current**: Manual implementation exists
- **TODO**: Integrate with battery test flow

### 3. Modbus Relay
- **Status**: Code written, hardware testing pending
- **Location**: `/bridge/modbus-relay.js`
- **Integration**: Future automatic mode support

### 4. FOG Server
- **Status**: Not implemented
- **Purpose**: Automatic deployment and testing
- **Integration**: Post-deployment auto-start battery test

## 📝 Key Differences from v1.8

1. **Offline-First**: All data stored locally, synced when online
2. **Visual Interface**: Rich HTML UI instead of console output
3. **Module Framework**: Extends TestModule base class
4. **Event System**: Uses standard module events
5. **Session Support**: Better handling of multiple sessions

## 🧪 Testing Strategy

### Manual Testing
1. Start test from dashboard with various configurations
2. Verify visual prompts appear correctly
3. Test power cable connect/disconnect detection
4. Verify graph updates in real-time
5. Check offline storage and sync

### Automatic Testing (Future)
1. Mock Modbus relay responses
2. Simulate FOG deployment scenario
3. Test unattended operation
4. Verify automatic power control

## 📋 Implementation Checklist

- [ ] Create BatteryTest.js module structure
- [ ] Port battery monitoring logic from v1.8
- [ ] Implement visual UI components
- [ ] Add Chart.js for real-time graphs
- [ ] Integrate with offline storage
- [ ] Handle existing session detection
- [ ] Add phase management (charge/discharge)
- [ ] Implement health calculations
- [ ] Create result analysis logic
- [ ] Add manual power prompts
- [ ] Test dashboard integration
- [ ] Document API changes
- [ ] Update CLAUDE.md with battery test info

## 🚀 Next Steps

1. **Session 1**: Create basic module structure and visual UI
2. **Session 2**: Port core test logic and monitoring
3. **Session 3**: Implement graphs and progress tracking
4. **Session 4**: Dashboard integration and testing
5. **Session 5**: Bridge service and Modbus integration (when hardware available)

## 📌 Important Notes

- Battery testing is unique: supports multiple sessions per laptop
- Manual mode is priority (90% use case)
- Visual feedback is critical for operators
- Must handle power state changes gracefully
- Test can be hours long - needs resume capability

## 🔗 Related Files

- `/agent/src/batteryTest.js` - Current v1.8 implementation
- `/agent/src/battery.js` - Battery monitoring utilities
- `/dashboard/src/components/BatteryTestModal.jsx` - Test configuration UI
- `/dashboard/src/components/LaptopCard/BatteryTestPanel.jsx` - Test monitoring UI
- `/bridge/modbus-relay.js` - Power control implementation
- `/docs/IQC_SYSTEM_BRAINDUMP.md` - Business requirements

---

**Remember**: Battery testing is session-based and can run multiple times per laptop. The implementation must support both manual operation (primary) and future automatic operation via FOG/Modbus integration.