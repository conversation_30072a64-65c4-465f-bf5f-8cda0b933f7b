# System Architecture Guide - QC System v2.0

## 🏗️ Overview

This document explains the modular architecture of our comprehensive QC system for refurbished laptops, designed to handle the complete lifecycle from initial quality control through final testing.

## 📦 Modular Architecture

### Agent v2.0 Structure

```
🏢 Agent v2.0 (Electron)
├── 📦 Core Framework
│   ├── Authentication Module (PIN-based login)
│   ├── Offline Storage (local SQLite)
│   ├── Sync Engine (background sync to Supabase)
│   ├── Error Handler (graceful failures)
│   └── Update Manager (auto-updates)
│
├── 📦 Test Modules (Reusable across stages)
│   ├── Battery Test Module (from v1.8)
│   ├── Hardware Test Module
│   │   ├── Keyboard Tester
│   │   ├── Display Tester
│   │   ├── Audio/Video Tester
│   │   ├── USB Port Tester
│   │   └── Physical Inspection
│   ├── System Info Module
│   └── Reporting Module
│
└── 📦 UI Components
    ├── Test Instructions (visual guides)
    ├── Progress Indicators
    ├── Result Display
    └── User Feedback
```

### Dashboard Enhancement

```
🏢 Dashboard (React)
├── 📋 Overview Page (Analytics & Lot Management)
├── 🔋 Battery Testing Page (Current + Windows Progress)
├── 🔧 Hardware Testing Page (Comprehensive Tests)
├── 📊 QC Management Page (Multi-stage Workflow)
├── 📈 Reports Page (Export & Analysis)
└── ⚙️ Settings Page (Model Profiles & Config)
```

## 🔄 Data Flow Architecture

### Hardware Test Flow (Agent-First Approach)
```
1. Laptop Boot → Agent v2.0 Launches
          ↓
2. PIN Login → User Authentication
          ↓
3. Hardware Detection → Auto-capture System Info
          ↓
4. Test Selection → Based on QC Stage (IQC/Mid/Final)
          ↓
5. Visual Test Guides → Step-by-step Instructions
          ↓
6. Local Storage → Save Results Offline
          ↓
7. Background Sync → Upload to Supabase
          ↓
8. Dashboard Update → Real-time Monitoring
```

### Multi-Stage QC Workflow
```
IQC (Input QC)          Mid-QC              Final QC
    ↓                      ↓                    ↓
Quick Tests          Post-Repair Tests    Comprehensive
- Basic Function     - Verify Repairs      - All Tests
- Physical Check     - New Parts Test      - Stress Tests
- Issue Detection    - Paint Quality       - Certification
```

### Offline-First Architecture
```
Agent Local Storage (SQLite)
    ├── Test Results
    ├── System Info
    ├── User Sessions
    └── Sync Queue
          ↓
    Network Available?
    Yes → Sync to Supabase
    No  → Queue for Later
```

## 💾 Database Schema Extensions

### New Tables for Hardware Testing

```sql
-- Hardware test results (reusable across stages)
CREATE TABLE hardware_test_results (
  id UUID PRIMARY KEY,
  laptop_id UUID REFERENCES laptops(id),
  test_stage VARCHAR(20), -- 'iqc', 'mid_qc', 'final_qc'
  test_module VARCHAR(50), -- 'keyboard', 'display', 'usb', etc.
  result VARCHAR(20), -- 'pass', 'fail', 'partial'
  details JSONB, -- Module-specific data
  tested_by VARCHAR(100), -- User PIN/ID
  tested_at TIMESTAMP,
  synced_at TIMESTAMP -- For offline tracking
);

-- Physical inspection results
CREATE TABLE physical_inspections (
  id UUID PRIMARY KEY,
  laptop_id UUID REFERENCES laptops(id),
  part_a_condition VARCHAR(20), -- 'perfect', 'paint_required', 'damaged', 'needs_replacement'
  part_b_condition VARCHAR(20),
  part_c_condition VARCHAR(20),
  part_d_condition VARCHAR(20),
  keyboard_issues JSONB,
  notes TEXT,
  inspected_by VARCHAR(100),
  inspected_at TIMESTAMP
);

-- Hardware components tracking
CREATE TABLE laptop_components (
  id UUID PRIMARY KEY,
  laptop_id UUID REFERENCES laptops(id),
  component_type VARCHAR(50), -- 'ram', 'ssd', 'battery', 'lcd'
  original_serial VARCHAR(200),
  current_serial VARCHAR(200),
  is_original BOOLEAN,
  replaced_at TIMESTAMP,
  notes TEXT
);

-- Model specifications
CREATE TABLE laptop_models (
  id UUID PRIMARY KEY,
  manufacturer VARCHAR(50),
  model_number VARCHAR(100),
  specifications JSONB, -- All hardware specs
  test_profile JSONB, -- Which tests to run
  created_at TIMESTAMP
);
```

## 🧩 Module Specifications

### Hardware Test Module

```javascript
// Base Test Module Interface
class TestModule {
  constructor(config) {
    this.name = config.name;
    this.version = config.version;
    this.requiredHardware = config.hardware;
  }
  
  async initialize() { /* Load test resources */ }
  async runTest() { /* Execute test logic */ }
  async getResults() { /* Return test results */ }
  async cleanup() { /* Clean up resources */ }
}

// Example: Keyboard Test Module
class KeyboardTestModule extends TestModule {
  constructor() {
    super({
      name: 'Keyboard Test',
      version: '1.0',
      hardware: ['keyboard']
    });
  }
  
  async runTest() {
    // Load model-specific layout
    const layout = await this.loadKeyboardLayout(laptop.model);
    
    // Show visual keyboard
    this.displayVisualKeyboard(layout);
    
    // Track key presses
    const results = await this.trackKeyPresses();
    
    // Calculate pass/fail
    return this.evaluateResults(results);
  }
}
```

### Sync Engine Architecture

```javascript
class SyncEngine {
  constructor(localDB, remoteDB) {
    this.local = localDB;
    this.remote = remoteDB;
    this.syncInterval = 30000; // 30 seconds
  }
  
  async startSync() {
    // Check network connectivity
    if (!this.isOnline()) {
      return this.scheduleRetry();
    }
    
    // Get unsynced records
    const pendingSync = await this.local.getUnsyncedRecords();
    
    // Batch upload
    for (const batch of this.createBatches(pendingSync)) {
      try {
        await this.remote.uploadBatch(batch);
        await this.local.markSynced(batch);
      } catch (error) {
        await this.handleSyncError(error);
      }
    }
  }
}
```

## 🎯 Key Design Principles

### 1. **Modular & Reusable**
- Test modules work across all QC stages
- Configurable test intensity per stage
- Easy to add new test types

### 2. **Offline-First**
- Local storage for all test data
- Background sync when online
- No data loss during network issues

### 3. **User-Centric**
- Visual guides for non-technical users
- PIN-based quick login
- Clear pass/fail indicators

### 4. **Smart Real-time**
- Real-time only for active monitoring
- Batch updates for efficiency
- Reduced server load vs v1.x

### 5. **Error Resilience**
- Graceful degradation
- User prompts for conflicts
- Automatic recovery

## 🔌 Integration Points

### 1. **FOG Server Integration**
```
FOG Completes Install → Webhook → Dashboard
                     ↓
                 Agent Auto-start
                     ↓
                 Begin Testing
```

### 2. **Modbus Relay (Battery + Power)**
```
Battery Test Request → Bridge Service → Modbus Commands
                    ↓                ↓
            Power Cycling      Status Updates
```

### 3. **Label Printing (Future)**
```
Test Complete → Generate Label → Print Queue
              ↓              ↓
         ZPL Commands   Network Printer
```

## 📊 Performance Optimizations

### Agent v2.0 Improvements
- **Event-driven**: No polling loops
- **Worker threads**: Heavy operations off main thread
- **Lazy loading**: Load modules on demand
- **Efficient updates**: Only sync changed data

### Dashboard Enhancements
- **Virtualized lists**: Handle 100+ laptops
- **Debounced updates**: Prevent UI thrashing
- **Optimistic updates**: Instant feedback
- **Progressive loading**: Load data as needed

## 🔐 Security & Authentication

### User Management
```
PIN Login (4-6 digits)
    ↓
Session Created (30 min timeout)
    ↓
Role-based Access
├── Technician: Run tests
├── Senior: Handle exceptions
└── Admin: Full access
```

### Data Security
- Offline data encrypted locally
- API keys embedded in compiled EXE
- Network validation for agents
- Audit trail for all actions

## 🚀 Deployment Architecture

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│   Agent v2.0    │     │    Dashboard    │     │ Bridge Service  │
│  (Windows EXE)  │────▶│  (Vercel/React) │────▶│  (Local Node)   │
└─────────────────┘     └─────────────────┘     └─────────────────┘
        │                        │                        │
        └────────────────────────┴────────────────────────┘
                                 │
                          ┌──────────────┐
                          │   Supabase   │
                          │  (Database)  │
                          └──────────────┘
```

## 📈 Scalability Considerations

- **Current**: 50 laptops/day across 30 bays
- **Target**: 100+ laptops/day (3000/month)
- **Architecture**: Supports 500+ concurrent tests
- **Database**: Partitioned tables for performance
- **Sync**: Intelligent batching for efficiency

---

**Remember**: Modular design enables rapid feature development while maintaining system stability.