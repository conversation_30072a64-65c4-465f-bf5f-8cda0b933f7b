# Hardware Test Modules Specification

## 📋 Overview

This document specifies the hardware test modules that are reusable across all QC stages (Input QC, Mid-Stage QC, Final QC). Each module is designed to work independently and can be configured for different test intensities.

**Update (Session 4)**: Modules now support flexible execution order. Operators can run tests in any sequence via the new Test Control Panel UI, while maintaining all smart model-aware behaviors.

## 🧩 Module Architecture

### Base Module Interface

```javascript
class TestModule {
  constructor(config) {
    this.name = config.name;
    this.version = config.version;
    this.requiredHardware = config.hardware;
    this.testStage = config.stage; // 'iqc', 'mid_qc', 'final_qc'
    this.intensity = config.intensity; // 'quick', 'standard', 'comprehensive'
  }
  
  async initialize() { /* Load resources */ }
  async runTest(laptop) { /* Execute test */ }
  async getResults() { /* Return results */ }
  async saveOffline() { /* Save to SQLite */ }
  async cleanup() { /* Clean up */ }
}
```

## 📦 Test Modules

### 1. System Information Module

**Purpose**: Capture hardware details and detect components

**Tests**:
- CPU model detection (i3/i5/i7/i9)
- RAM detection (capacity, speed, serial)
- SSD detection (capacity, model, serial)
- Model number identification
- BIOS version check

**Special Features**:
- Test RAM detection (compare against known test RAM serials)
- Original component flagging
- Duplicate serial/MAC detection with user prompt

**Intensity Levels**:
- **Quick (IQC)**: Basic detection only
- **Standard (Mid)**: Verify replacements
- **Comprehensive (Final)**: Full system profile

### 2. Keyboard Test Module

**Purpose**: Verify all keys functionality

**Visual Interface**:
```
┌─────────────────────────────────────────┐
│        Keyboard Test - HP 840 G5         │
├─────────────────────────────────────────┤
│  [ESC] [F1] [F2] ... [F12] [DEL]       │
│  [~] [1] [2] [3] ... [0] [-] [=] [BS]  │
│  [TAB] [Q] [W] [E] ... [P] [{] [}] [\] │
│  [CAPS] [A] [S] [D] ... [L] [;] ['] [↵]│
│  [SHIFT] [Z] [X] ... [M] [,] [.] [/]   │
│  [CTRL] [WIN] [ALT] [SPACE] ...         │
├─────────────────────────────────────────┤
│ Progress: 45/87 keys tested             │
│ Failed Keys: F5, NumLock                │
│ Backlight: ✓ Working                    │
└─────────────────────────────────────────┘
```

**Features**:
- Model-specific keyboard layouts
- Visual feedback (green = tested, red = failed)
- Backlight test (if applicable)
- Missing key detection
- Sticky key detection

**Test Requirements**:
- All keys must be pressed
- Special keys tested (Fn combinations)
- Multimedia keys verified

### 3. Display Test Module

**Purpose**: Identify display defects

**Test Screens**:
1. **Solid Colors**: White, Black, Red, Green, Blue
2. **Gradient Test**: Smooth color transitions
3. **Grid Pattern**: Line detection
4. **Dead Pixel Test**: Rapid color changes

**Visual Interface**:
```
┌─────────────────────────────────────────┐
│         Display Test Results            │
├─────────────────────────────────────────┤
│ □ Perfect condition                     │
│ □ Vertical lines detected              │
│ □ Horizontal lines detected            │
│ □ Dead pixels/patches found            │
│ □ Keyboard marks only                  │
│ □ Brightness issues                    │
│ □ Backlight bleeding                   │
├─────────────────────────────────────────┤
│ [Test External Display via HDMI]        │
└─────────────────────────────────────────┘
```

**Decision Tree**:
- Lines/Patches → LCD replacement
- Keyboard marks → Refurbishment possible
- Broken → Test via external display

### 4. Audio/Video Test Module

**Purpose**: Test camera, microphone, and speakers

**Camera Test**:
- Capture test image
- IR camera detection (if present)
- Resolution verification
- Save compressed thumbnail (50KB max)

**Microphone Test**:
- 5-second recording
- Playback for verification
- Noise level detection
- Save proof (10KB max)

**Speaker Test**:
- Left channel tone (1kHz)
- Right channel tone (1kHz)
- Both channels together
- Volume level test

**Visual Interface**:
```
┌─────────────────────────────────────────┐
│        Audio/Video Test Suite           │
├─────────────────────────────────────────┤
│ Camera:                                 │
│ [📷 Capture] Preview: ✓ Working         │
│                                         │
│ Microphone:                             │
│ [🎤 Record 5s] [▶ Play] Level: ████░░  │
│                                         │
│ Speakers:                               │
│ [🔊 Test Left] [🔊 Test Right] [🔊 Both]│
│ Left: ✓ Pass  Right: ✓ Pass            │
└─────────────────────────────────────────┘
```

### 5. USB Port Test Module

**Purpose**: Verify USB functionality and speed

**Requirements**:
- USB test device (provided to technician)
- Tests power delivery and data transfer

**Visual Interface**:
```
┌─────────────────────────────────────────┐
│      USB Port Test - Dell 7420          │
├─────────────────────────────────────────┤
│ Port Map:                               │
│ ┌─────────────────────────┐            │
│ │ [1] [2]        [C1] [3] │            │
│ └─────────────────────────┘            │
│                                         │
│ Test Results:                           │
│ Port 1: ✓ USB 3.0 (5 Gbps)            │
│ Port 2: ⚠ USB 2.0 only                 │
│ Port 3: ✓ USB 3.0 (5 Gbps)            │
│ Type-C: ✓ Data + Power Delivery        │
│                                         │
│ Insert test device in each port...      │
└─────────────────────────────────────────┘
```

**Tests**:
- Port detection
- USB 2.0 vs 3.0 speed
- Power delivery capability
- Type-C functionality

### 6. Power Input Test Module

**Purpose**: Verify charging capabilities

**Tests**:
- DC jack functionality
- Power LED indicator
- Type-C power delivery (if supported)
- Charging detection

**Visual Interface**:
```
┌─────────────────────────────────────────┐
│         Power Input Test                │
├─────────────────────────────────────────┤
│ DC Jack:                                │
│ □ Power connected                       │
│ □ LED indicator working                 │
│ □ Charging detected                     │
│                                         │
│ Type-C Power (if supported):            │
│ □ Power delivery negotiated             │
│ □ Charging active                       │
│                                         │
│ Current Status: On Battery              │
└─────────────────────────────────────────┘
```

### 7. Physical Inspection Module

**Purpose**: Document physical condition

**Parts Classification**:
- **A-Part**: LCD back cover
- **B-Part**: LCD bezel
- **C-Part**: Keyboard/trackpad area
- **D-Part**: Bottom base

**Condition Options**:
1. Perfect
2. Paint Required
3. Damaged
4. Needs Replacement

**Visual Interface**:
```
┌─────────────────────────────────────────┐
│      Physical Inspection Checklist      │
├─────────────────────────────────────────┤
│ A-Part (LCD Back):                      │
│ ○ Perfect ○ Paint ○ Damaged ○ Replace  │
│                                         │
│ B-Part (Bezel):                         │
│ ○ OK ○ Damaged ○ Replace               │
│                                         │
│ C-Part (Keyboard Area):                 │
│ ○ Perfect ○ Paint ○ Damaged ○ Replace  │
│ □ Missing keys: [  ]                    │
│ □ Broken locks: [  ]                    │
│                                         │
│ D-Part (Bottom):                        │
│ ○ Perfect ○ Paint ○ Damaged ○ Replace  │
│ □ Rubber feet: OK / Missing / Damaged  │
│                                         │
│ Notes: [                              ] │
└─────────────────────────────────────────┘
```

### 8. Battery Test Module (From v1.8)

**Purpose**: Test battery health and capacity

**Features**:
- Charge/discharge cycles
- Health score calculation
- WMI data collection
- Manual or automated (with Modbus)

**Already Implemented**: Port from existing v1.8

## 🔧 Test Intensity Configuration

### Quick (IQC - 5 minutes)
- Basic functionality check
- Major defect identification
- Skip time-consuming tests

### Standard (Mid-QC - 10 minutes)
- Verify repairs completed
- Test replaced components
- Quality assurance

### Comprehensive (Final QC - 15 minutes)
- All tests mandatory
- Stress testing included
- Certification ready

## 📊 Results Storage

### Offline Storage (SQLite)
```sql
CREATE TABLE test_results (
  id INTEGER PRIMARY KEY,
  laptop_id TEXT,
  module_name TEXT,
  test_stage TEXT,
  result TEXT, -- 'pass', 'fail', 'partial'
  details TEXT, -- JSON data
  tested_by TEXT,
  tested_at TIMESTAMP,
  synced INTEGER DEFAULT 0
);
```

### Result Format
```json
{
  "module": "keyboard",
  "stage": "iqc",
  "result": "fail",
  "details": {
    "total_keys": 87,
    "tested_keys": 87,
    "failed_keys": ["F5", "NumLock"],
    "backlight": true,
    "missing_keys": 0,
    "test_duration": 45
  }
}
```

## 🔄 Stage-Specific Behavior

### Input QC
- Focus on detection
- Quick pass/fail
- Identify repair needs

### Mid-Stage QC
- Verify repairs
- Test new components
- Quality check

### Final QC
- Comprehensive testing
- Stress tests
- Final certification

## 🚀 Implementation Priority

1. **System Information** - Foundation for all tests
2. **Keyboard Test** - Most common issue
3. **Display Test** - High-value component
4. **Physical Inspection** - Determines repair path
5. **USB/Power Tests** - Functionality verification
6. **Audio/Video** - Complete the suite

---

**Remember**: Each module must work offline-first with visual guidance for non-technical users.