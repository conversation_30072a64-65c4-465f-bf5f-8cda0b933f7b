# FOG Implementation Plan for Laptop QC System

**Date**: December 18, 2024  
**Purpose**: Complete implementation guide for FOG-based laptop deployment system

## Executive Summary

This document outlines the implementation of a FOG-based deployment system capable of installing Windows on 30-50 laptops simultaneously. The system uses PXE network booting with a simple operator workflow: connect laptop, select Windows version, and the installation runs automatically.

## System Overview

### What We're Building
- **Capacity**: 30 deployment bays + 10 IQC testing stations
- **Daily throughput**: 50-500 laptops (scalable)
- **Deployment method**: PXE boot with FOG Project
- **Images**: 5-10 Windows versions (Win10/Win11 variants)
- **Operator workflow**: 3 clicks total - no server access needed

### Key Benefits
- **No registration required** - Deploy any laptop immediately
- **Continuous operation** - Hot-swap laptops as they complete
- **Simple for staff** - Select image from menu and walk away
- **Efficient resource use** - Multicast for mass deployments

## Infrastructure Requirements

### Server Hardware (Dell R730)
- **CPU**: Dual E5-2680 v4 (28 cores total)
- **RAM**: 256GB DDR4 ECC
  - 32GB for OS/FOG
  - 128GB for RAM disk
  - Rest for caching/overhead
- **Storage**: 1× NVMe SSD for OS/logs only
- **Network**: 2× 10G ports (bond to 20Gbps)

### Network Equipment
- **Switch**: TP-Link TL-SG3452X JetStream
  - 48× 1Gbps ports for laptops
  - 4× 10G SFP+ ports
  - LACP support for link aggregation
  - IGMP snooping for multicast
- **Router**: UniFi Dream Machine SE (existing)
- **Cabling**: Cat6 for all laptop bays

### Network Architecture
```
[Internet]
    |
[UniFi Dream Machine SE]
    |
    | 10G uplink
    |
[TP-Link SG3452X Switch]
    |
    | 2×10G LACP (20Gbps)
    |
[Dell R730 FOG Server]
    |
    +-- 48× 1Gbps --> [30 Deployment Bays]
    +-- 48× 1Gbps --> [10 IQC Test Stations]
```

## FOG Server Installation

### Step 1: Create Proxmox VM
```bash
# VM Configuration
Name: FOG-Server
Type: VM (not container - better compatibility)
OS: Ubuntu 22.04 LTS
CPU: 8 cores
RAM: 32GB
Disk: 100GB (local storage)
Network: virtio, bridge to 10G interface
```

### Step 2: Install Ubuntu and FOG
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install prerequisites
sudo apt install git wget curl -y

# Download FOG
cd /opt
sudo git clone https://github.com/FOGProject/fogproject.git
cd fogproject/bin

# Run installer
sudo ./installfog.sh

# Installation options:
# - Installation mode: Normal
# - DHCP: No (use existing)
# - Additional languages: No
```

### Step 3: Configure RAM Disk
```bash
# Create mount point
sudo mkdir /images

# Add to /etc/fstab for persistence
echo "tmpfs /images tmpfs size=128G,mode=0755 0 0" | sudo tee -a /etc/fstab

# Mount immediately
sudo mount -t tmpfs -o size=128G tmpfs /images

# Set permissions
sudo chown -R fog:fog /images
sudo chmod -R 755 /images

# Update FOG configuration
sudo sed -i 's|/images|/images|g' /opt/fog/.fogsettings
```

### Step 4: FOG Web Configuration
1. Access: `http://[server-ip]/fog`
2. Default login: fog/password (change immediately)
3. **Storage Management**:
   - DefaultMember → Max Clients: 30
   - Storage Path: /images
4. **FOG Configuration** → **FOG Settings**:
   - TFTP Server: [server-ip]
   - Max Clients: 30
   - Enable multicast: Yes

### Step 5: Network Configuration
```bash
# Enable IP forwarding
sudo sysctl -w net.ipv4.ip_forward=1
echo "net.ipv4.ip_forward=1" | sudo tee -a /etc/sysctl.conf

# Configure firewall
sudo ufw allow 67/udp  # DHCP
sudo ufw allow 69/udp  # TFTP
sudo ufw allow 80/tcp  # HTTP
sudo ufw allow 443/tcp # HTTPS
sudo ufw allow 9098/tcp # FOG multicast
```

## Switch Configuration (TP-Link SG3452X)

### Link Aggregation Setup
1. Access switch web interface
2. **L2 Features** → **Link Aggregation** → **LAG**
3. Create LAG1:
   - Member Ports: Port 49, 50 (SFP+ 1&2)
   - Type: Static or LACP
4. Connect both ports to R730 server

### VLAN Configuration (Optional)
```
VLAN 1: Default/Management
VLAN 100: Deployment Network (optional isolation)
VLAN 200: IQC Testing Network (optional isolation)
```

### Multicast Configuration
1. **L2 Features** → **Multicast** → **IGMP Snooping**
2. Enable IGMP Snooping globally
3. Enable on deployment VLAN

## Image Preparation

### Creating Master Images
1. Install Windows 10/11 on reference laptop
2. Install drivers and required software
3. Run sysprep: `C:\Windows\System32\Sysprep\sysprep.exe`
   - Enter OOBE
   - Generalize
   - Shutdown

### Capturing Images in FOG
1. Register reference laptop in FOG
2. Create image definition:
   - Name: "Windows10-Standard"
   - Type: Single Disk - Resizable
   - Compression: zstd (recommended)
3. Schedule capture task
4. PXE boot laptop to capture

### Image Storage
- Images stored in RAM disk at `/images`
- Typical sizes: 5-10GB compressed
- 128GB RAM disk holds 12-20 images

## Operational Workflow

### For Operators (Daily Use)
1. **Connect laptop** to any available bay
2. **Power on** and press F12/F9 (boot menu)
3. **Select** network/PXE boot option
4. FOG menu appears:
   ```
   FOG Computer Cloning Solution
   
   1. Deploy Image
   2. System Information
   3. Debug
   4. Exit to BIOS
   ```
5. **Select** "Deploy Image"
6. **Choose** from image list:
   - Windows 10 Standard
   - Windows 11 Standard
   - Windows 10 Office
   - etc.
7. **Deployment starts** automatically
8. **Walk away** - takes 45-60 minutes
9. **Replace** with next laptop when complete

### No Server Access Required
- Operators never log into FOG server
- All interaction through PXE boot menu
- Continuous operation throughout day
- No registration or configuration needed

## Performance Analysis

### Network Bandwidth
- **Unicast mode**: Each laptop uses 1Gbps
- **Multicast mode**: All laptops share 1Gbps
- **Server capacity**: 20Gbps (LACP bond)
- **Practical limit**: 20 unicast streams simultaneously

### Bottleneck Analysis
1. **Client NIC (70%)**: 1Gbps laptop limit
2. **Switch backplane (15%)**: Adequate for load
3. **Server I/O (10%)**: RAM disk eliminates this
4. **CPU (5%)**: Compression/decompression

### Expected Performance
- **Unicast deployment**: 15-20 minutes per laptop
- **Multicast deployment**: 20-25 minutes (all complete together)
- **Daily capacity**: 200-300 laptops easily
- **Concurrent operations**: 20-25 deployments + 10 live boots

## Testing and Validation

### Initial Testing
1. Deploy single laptop via unicast
2. Test multicast with 5 laptops
3. Verify image resizing works
4. Test hot-swap workflow

### Load Testing
1. Start 20 unicast deployments
2. Monitor network utilization
3. Add 5 live boot sessions
4. Verify no performance degradation

### Operator Training
1. Show PXE boot key (F12/F9/Del)
2. Demonstrate menu navigation
3. Practice image selection
4. Explain completion indicators

## Troubleshooting

### Common Issues
1. **PXE boot fails**: Check BIOS boot order
2. **No menu appears**: Verify network cable
3. **Slow deployment**: Check for multicast issues
4. **Image fails**: Verify sufficient RAM (4GB+)

### Monitoring
- FOG Dashboard: Active tasks and queue
- Network utilization: Switch interface stats
- Server resources: `htop` on FOG server

## Maintenance

### Daily
- Monitor active deployments
- Clear completed tasks
- Verify image availability

### Weekly
- Check server resources
- Review deployment logs
- Update image library as needed

### Monthly
- Capture updated images
- Clean old images
- Review performance metrics

## Cost-Benefit Analysis

### Hardware Investment
- TP-Link Switch: ₹45,000
- Network cables: ₹10,000
- Total: ₹55,000 (one-time)

### Operational Savings
- Manual installation: 2 hours × ₹200/hour = ₹400/laptop
- FOG deployment: 5 minutes oversight = ₹17/laptop
- **Savings: ₹383 per laptop**
- **ROI: After 144 laptops (2-3 days)**

## Conclusion

This FOG implementation provides a robust, scalable solution for mass laptop deployment. The system requires minimal technical knowledge to operate, supports continuous operation, and pays for itself within the first week of use. The combination of RAM disk storage and multicast deployment ensures optimal performance even at peak load.