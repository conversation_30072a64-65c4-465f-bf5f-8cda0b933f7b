# Supabase Setup Guide

## 1. Create Supabase Project

1. Go to [https://supabase.com](https://supabase.com)
2. Sign up or log in
3. Click "New Project"
4. Fill in:
   - Project name: `battery-testing`
   - Database password: (save this securely)
   - Region: Choose closest to your location
5. Click "Create new project"

## 2. Get API Keys

After project creation:

1. Go to Settings → API
2. Copy these values:
   - **Project URL**: `https://xxxxx.supabase.co`
   - **Anon/Public Key**: For dashboard
   - **Service Role Key**: For agent (keep secret!)

## 3. Set Up Database Schema

1. Go to SQL Editor in Supabase dashboard
2. Click "New query"
3. Copy contents of `supabase_schema.sql`
4. Click "Run"

## 4. Configure Environment Variables

### Agent (.env)
```
SUPABASE_URL=https://xxxxx.supabase.co
SUPABASE_SERVICE_KEY=your_service_role_key_here
NETWORK_VALIDATION=192.168.50
```

### Dashboard (.env)
```
VITE_SUPABASE_URL=https://xxxxx.supabase.co
VITE_SUPABASE_ANON_KEY=your_anon_key_here
```

## 5. Enable Realtime (Already done in schema)

The schema automatically enables realtime for the laptops table.

## 6. Test Connection

Use Supabase dashboard to:
1. Go to Table Editor
2. View `laptops` table
3. Try inserting a test row

## Security Notes

- **Never expose Service Role Key** in client code
- Service Role Key should only be in agent EXE
- Use Anon Key for dashboard (public-facing)