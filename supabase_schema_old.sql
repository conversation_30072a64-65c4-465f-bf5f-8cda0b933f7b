-- Comprehensive QC System - Supabase Schema
-- Single schema file for all tables (as per user preference)
-- Last Updated: December 14, 2024

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- PHASE 1 TABLES: Core Infrastructure
-- =====================================================

-- Laptops table with bay assignment
CREATE TABLE laptops (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  short_id VARCHAR(10) UNIQUE NOT NULL,        -- A001, A002, etc.
  serial_number VARCHAR(100),
  mac_address VARCHAR(17),
  model VARCHAR(100),                           -- Dell Latitude 7420, HP ProBook 450, etc.
  bay_number INTEGER CHECK (bay_number >= 1 AND bay_number <= 30), -- Physical bay 1-30
  bay_assignment_pending INTEGER,               -- Pending bay for automatic assignment
  fog_id VARCHAR(50),                           -- FOG server tracking ID
  status VARCHAR(20) DEFAULT 'idle',            -- idle, connected, testing, completed
  last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Test sessions for all test types
CREATE TABLE test_sessions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  laptop_id UUID REFERENCES laptops(id) ON DELETE CASCADE,
  test_type VARCHAR(50) NOT NULL,              -- battery, hardware, full_qc
  start_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  end_time TIMESTAMP WITH TIME ZONE,
  result VARCHAR(20),                           -- pass, fail, incomplete
  notes TEXT,
  operator_id VARCHAR(100),                     -- Who ran the test
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Battery readings for battery tests
CREATE TABLE battery_readings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  session_id UUID REFERENCES test_sessions(id) ON DELETE CASCADE,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  percentage INTEGER CHECK (percentage >= 0 AND percentage <= 100),
  voltage DECIMAL(5,2),
  temperature DECIMAL(5,2),
  current_ma INTEGER,                           -- Current in milliamps
  is_charging BOOLEAN,
  power_source VARCHAR(20),                     -- battery, ac
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Power control logs for relay operations
CREATE TABLE power_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  bay_number INTEGER NOT NULL CHECK (bay_number >= 1 AND bay_number <= 30),
  action VARCHAR(10) NOT NULL,                 -- on, off
  triggered_by VARCHAR(50),                     -- dashboard, bridge, manual, schedule
  laptop_id UUID REFERENCES laptops(id),       -- Which laptop was affected
  success BOOLEAN DEFAULT true,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- PHASE 2 TABLES: Hardware Testing
-- =====================================================

-- Hardware test results
CREATE TABLE hardware_tests (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  session_id UUID REFERENCES test_sessions(id) ON DELETE CASCADE,
  component VARCHAR(50) NOT NULL,              -- keyboard, trackpad, webcam, usb, audio, display
  status VARCHAR(20) NOT NULL,                 -- pass, fail, not_tested, partial
  notes TEXT,
  test_data JSONB,                             -- Component-specific test data
  tested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Model-specific test profiles
CREATE TABLE test_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  model_name VARCHAR(100) UNIQUE NOT NULL,      -- Dell Latitude 7420
  battery_test_config JSONB,                    -- Charge/discharge durations, thresholds
  hardware_test_config JSONB,                   -- Which components to test
  active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- PHASE 3 TABLES: Printing & Reporting
-- =====================================================

-- Print jobs for labels and reports
CREATE TABLE print_jobs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  laptop_id UUID REFERENCES laptops(id) ON DELETE CASCADE,
  job_type VARCHAR(20) NOT NULL,               -- label, report, certificate
  printer_name VARCHAR(50),
  template_name VARCHAR(50),                    -- Which label/report template
  data JSONB,                                   -- Data to print
  zpl_content TEXT,                             -- Generated ZPL for Zebra printers
  status VARCHAR(20) DEFAULT 'queued',          -- queued, printing, completed, failed
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE
);

-- =====================================================
-- SYSTEM TABLES: Monitoring & Configuration
-- =====================================================

-- Installation tracking (FOG integration)
CREATE TABLE installations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  mac_address VARCHAR(17) NOT NULL,
  fog_task_id VARCHAR(50),
  status VARCHAR(50),                           -- registered, imaging, complete, failed
  progress INTEGER,                             -- 0-100 percentage
  image_name VARCHAR(100),
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- System configuration
CREATE TABLE system_config (
  key VARCHAR(100) PRIMARY KEY,
  value JSONB NOT NULL,
  description TEXT,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- INDEXES for Performance
-- =====================================================

CREATE INDEX idx_laptops_short_id ON laptops(short_id);
CREATE INDEX idx_laptops_status ON laptops(status);
CREATE INDEX idx_laptops_bay_number ON laptops(bay_number);
CREATE INDEX idx_test_sessions_laptop_id ON test_sessions(laptop_id);
CREATE INDEX idx_test_sessions_test_type ON test_sessions(test_type);
CREATE INDEX idx_battery_readings_session_id ON battery_readings(session_id);
CREATE INDEX idx_power_logs_bay_number ON power_logs(bay_number);
CREATE INDEX idx_hardware_tests_session_id ON hardware_tests(session_id);
CREATE INDEX idx_print_jobs_status ON print_jobs(status);
CREATE INDEX idx_installations_mac_address ON installations(mac_address);

-- =====================================================
-- FUNCTIONS & TRIGGERS
-- =====================================================

-- Function to auto-update updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at trigger to relevant tables
CREATE TRIGGER update_laptops_updated_at BEFORE UPDATE
  ON laptops FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_test_profiles_updated_at BEFORE UPDATE
  ON test_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_config_updated_at BEFORE UPDATE
  ON system_config FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to get laptop by bay number
CREATE OR REPLACE FUNCTION get_laptop_by_bay(bay_num INTEGER)
RETURNS TABLE(laptop_id UUID, short_id VARCHAR, model VARCHAR, status VARCHAR) AS $$
BEGIN
  RETURN QUERY
  SELECT id, short_id, model, status
  FROM laptops
  WHERE bay_number = bay_num
  ORDER BY last_seen DESC
  LIMIT 1;
END;
$$ language 'plpgsql';

-- =====================================================
-- ROW LEVEL SECURITY (RLS)
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE laptops ENABLE ROW LEVEL SECURITY;
ALTER TABLE test_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE battery_readings ENABLE ROW LEVEL SECURITY;
ALTER TABLE power_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE hardware_tests ENABLE ROW LEVEL SECURITY;
ALTER TABLE test_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE print_jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE installations ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_config ENABLE ROW LEVEL SECURITY;

-- Service role can do everything (for agents and bridge service)
CREATE POLICY "Service role full access" ON laptops FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Service role full access" ON test_sessions FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Service role full access" ON battery_readings FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Service role full access" ON power_logs FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Service role full access" ON hardware_tests FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Service role full access" ON test_profiles FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Service role full access" ON print_jobs FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Service role full access" ON installations FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Service role full access" ON system_config FOR ALL USING (auth.role() = 'service_role');

-- Authenticated users can read most data (for dashboard)
CREATE POLICY "Authenticated read access" ON laptops FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Authenticated read access" ON test_sessions FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Authenticated read access" ON battery_readings FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Authenticated read access" ON power_logs FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Authenticated read access" ON hardware_tests FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Authenticated read access" ON test_profiles FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Authenticated read access" ON print_jobs FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Authenticated read access" ON installations FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Authenticated read access" ON system_config FOR SELECT USING (auth.role() = 'authenticated');

-- =====================================================
-- REALTIME SUBSCRIPTIONS
-- =====================================================

-- Enable realtime for live updates
ALTER PUBLICATION supabase_realtime ADD TABLE laptops;
ALTER PUBLICATION supabase_realtime ADD TABLE test_sessions;
ALTER PUBLICATION supabase_realtime ADD TABLE battery_readings;
ALTER PUBLICATION supabase_realtime ADD TABLE power_logs;
ALTER PUBLICATION supabase_realtime ADD TABLE installations;

-- =====================================================
-- DEFAULT CONFIGURATION DATA
-- =====================================================

-- Insert default system configuration
INSERT INTO system_config (key, value, description) VALUES
  ('relay_ip', '"*************0"'::jsonb, 'IP address of the 30-channel relay module'),
  ('relay_port', '502'::jsonb, 'Modbus TCP port for relay module'),
  ('network_range', '"************/24"'::jsonb, 'Allowed network range for agents'),
  ('battery_test_defaults', '{"charge_minutes": 30, "discharge_minutes": 30, "pass_threshold": 80}'::jsonb, 'Default battery test parameters'),
  ('fog_server_url', '"http://*************"'::jsonb, 'FOG server URL for PXE deployment')
ON CONFLICT (key) DO NOTHING;

-- Insert sample test profiles for common models
INSERT INTO test_profiles (model_name, battery_test_config, hardware_test_config) VALUES
  ('Dell Latitude 7420', 
   '{"charge_minutes": 30, "discharge_minutes": 30, "pass_threshold": 85}'::jsonb,
   '{"components": ["keyboard", "trackpad", "webcam", "usb", "audio"]}'::jsonb),
  ('HP ProBook 450 G8',
   '{"charge_minutes": 45, "discharge_minutes": 30, "pass_threshold": 80}'::jsonb,
   '{"components": ["keyboard", "trackpad", "webcam", "usb", "audio", "fingerprint"]}'::jsonb),
  ('HP EliteBook 840 G7',
   '{"charge_minutes": 30, "discharge_minutes": 45, "pass_threshold": 85}'::jsonb,
   '{"components": ["keyboard", "trackpad", "webcam", "usb", "audio", "fingerprint", "smartcard"]}'::jsonb)
ON CONFLICT (model_name) DO NOTHING;