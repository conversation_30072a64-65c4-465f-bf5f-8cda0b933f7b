const express = require('express');
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Mock relay control since actual hardware not available
class MockRelayController {
  constructor() {
    this.states = {};
    // Initialize all 30 bays to ON
    for (let i = 1; i <= 30; i++) {
      this.states[i] = true;
    }
  }

  async connect() {
    console.log('[MOCK] Relay controller connected');
    return true;
  }

  async setPower(bayNumber, state) {
    console.log(`[MOCK] Setting bay ${bayNumber} to ${state ? 'ON' : 'OFF'}`);
    this.states[bayNumber] = state;
    return true;
  }

  getState(bayNumber) {
    return this.states[bayNumber];
  }

  getAllStates() {
    return { ...this.states };
  }
}

// Initialize
const app = express();
const relay = new MockRelayController();
const supabase = createClient(
  process.env.SUPABASE_URL || 'https://primrdkulctsjvugdiyu.supabase.co',
  process.env.SUPABASE_SERVICE_KEY || 'YOUR_SERVICE_KEY'
);

app.use(express.json());

// Connect to relay on startup
relay.connect();

// Subscribe to power control commands from dashboard
const subscribeToPowerCommands = async () => {
  const subscription = supabase
    .channel('power-commands')
    .on('postgres_changes', {
      event: 'INSERT',
      schema: 'public',
      table: 'power_logs'
    }, async (payload) => {
      console.log('Power command received:', payload);
      
      const { bay_number, action } = payload.new;
      const powerState = action === 'on';
      
      try {
        // Execute relay control
        await relay.setPower(bay_number, powerState);
        
        // Log success (in real implementation, this would update the power_log record)
        console.log(`Successfully set bay ${bay_number} to ${action}`);
      } catch (error) {
        console.error(`Failed to control bay ${bay_number}:`, error);
      }
    })
    .subscribe();

  console.log('Subscribed to power commands');
};

// Web UI endpoints
app.get('/', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>Bridge Service - Relay Control</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .bay-grid { display: grid; grid-template-columns: repeat(6, 1fr); gap: 10px; margin: 20px 0; }
        .bay { border: 2px solid #ccc; padding: 10px; text-align: center; border-radius: 5px; }
        .bay.on { background: #90EE90; border-color: #228B22; }
        .bay.off { background: #FFB6C1; border-color: #DC143C; }
        button { padding: 5px 10px; margin-top: 5px; cursor: pointer; }
        .status { margin: 20px 0; padding: 10px; background: #f0f0f0; border-radius: 5px; }
      </style>
    </head>
    <body>
      <h1>Bridge Service - Mock Relay Control</h1>
      <div class="status">
        <strong>Status:</strong> Running (Mock Mode)<br>
        <strong>Supabase:</strong> ${process.env.SUPABASE_URL ? 'Connected' : 'Not configured'}<br>
        <strong>Relay:</strong> Mock controller active
      </div>
      
      <h2>Bay Power Control</h2>
      <div id="bayGrid" class="bay-grid"></div>
      
      <script>
        const updateGrid = async () => {
          const response = await fetch('/api/relay/states');
          const states = await response.json();
          
          const grid = document.getElementById('bayGrid');
          grid.innerHTML = '';
          
          for (let i = 1; i <= 30; i++) {
            const isOn = states[i];
            const bayDiv = document.createElement('div');
            bayDiv.className = 'bay ' + (isOn ? 'on' : 'off');
            bayDiv.innerHTML = \`
              <div>Bay \${i}</div>
              <div>\${isOn ? 'ON' : 'OFF'}</div>
              <button onclick="togglePower(\${i}, \${!isOn})">\${isOn ? 'Turn OFF' : 'Turn ON'}</button>
            \`;
            grid.appendChild(bayDiv);
          }
        };
        
        const togglePower = async (bay, state) => {
          await fetch(\`/api/relay/\${bay}\`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ state })
          });
          updateGrid();
        };
        
        // Update grid every 2 seconds
        updateGrid();
        setInterval(updateGrid, 2000);
      </script>
    </body>
    </html>
  `);
});

// API endpoints
app.get('/api/relay/states', (req, res) => {
  res.json(relay.getAllStates());
});

app.post('/api/relay/:bay', async (req, res) => {
  const bayNumber = parseInt(req.params.bay);
  const { state } = req.body;
  
  if (bayNumber < 1 || bayNumber > 30) {
    return res.status(400).json({ error: 'Invalid bay number' });
  }
  
  try {
    await relay.setPower(bayNumber, state);
    
    // Log to Supabase
    await supabase.from('power_logs').insert({
      bay_number: bayNumber,
      action: state ? 'on' : 'off',
      triggered_by: 'bridge-ui'
    });
    
    res.json({ success: true, bay: bayNumber, state });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Start server
const PORT = process.env.PORT || 5200;
app.listen(PORT, () => {
  console.log(`Bridge service running on http://localhost:${PORT}`);
  subscribeToPowerCommands();
});