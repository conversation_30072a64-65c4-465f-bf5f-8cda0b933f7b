const ModbusRTU = require('modbus-serial');

class ModbusRelayController {
  constructor(ip = '**************', port = 502) {
    this.client = new ModbusRTU();
    this.ip = ip;
    this.port = port;
    this.connected = false;
    this.states = {};
    
    // Initialize all 30 bays state cache
    for (let i = 1; i <= 30; i++) {
      this.states[i] = false;
    }
  }

  async connect() {
    try {
      await this.client.connectTCP(this.ip, { port: this.port });
      this.client.setID(1); // Default Modbus device ID
      this.connected = true;
      console.log(`Connected to Modbus relay at ${this.ip}:${this.port}`);
      
      // Read initial states
      await this.readAllStates();
      return true;
    } catch (error) {
      console.error('Failed to connect to Modbus relay:', error);
      this.connected = false;
      throw error;
    }
  }

  async disconnect() {
    if (this.connected) {
      this.client.close();
      this.connected = false;
    }
  }

  async setPower(bayNumber, state) {
    if (!this.connected) {
      throw new Error('Not connected to relay');
    }

    try {
      // Most 30-channel relays use coil addresses 0-29
      const coilAddress = bayNumber - 1;
      
      // Write single coil (Function Code 5)
      await this.client.writeCoil(coilAddress, state);
      
      // Update cache
      this.states[bayNumber] = state;
      
      console.log(`Set bay ${bayNumber} (coil ${coilAddress}) to ${state ? 'ON' : 'OFF'}`);
      return true;
    } catch (error) {
      console.error(`Failed to control bay ${bayNumber}:`, error);
      throw error;
    }
  }

  async readAllStates() {
    if (!this.connected) {
      throw new Error('Not connected to relay');
    }

    try {
      // Read all 30 coils at once (Function Code 1)
      const result = await this.client.readCoils(0, 30);
      
      // Update states cache
      for (let i = 0; i < 30; i++) {
        this.states[i + 1] = result.data[i];
      }
      
      return this.states;
    } catch (error) {
      console.error('Failed to read relay states:', error);
      throw error;
    }
  }

  getState(bayNumber) {
    return this.states[bayNumber];
  }

  getAllStates() {
    return { ...this.states };
  }

  // Power cycle pattern for bay assignment
  async powerCyclePattern(bayNumber, pattern = ['off', 'on', 'off', 'on']) {
    const delays = [3000, 3000, 3000, 5000]; // milliseconds
    
    for (let i = 0; i < pattern.length; i++) {
      const state = pattern[i] === 'on';
      await this.setPower(bayNumber, state);
      
      if (i < pattern.length - 1) {
        await new Promise(resolve => setTimeout(resolve, delays[i]));
      }
    }
  }

  // Batch operations for efficiency
  async setMultipleBays(bayStates) {
    // bayStates: { 1: true, 2: false, ... }
    const operations = [];
    
    for (const [bay, state] of Object.entries(bayStates)) {
      operations.push(this.setPower(parseInt(bay), state));
    }
    
    await Promise.all(operations);
  }

  // Emergency all-off
  async emergencyStop() {
    try {
      // Write multiple coils at once - all OFF
      const allOff = new Array(30).fill(false);
      await this.client.writeCoils(0, allOff);
      
      // Update cache
      for (let i = 1; i <= 30; i++) {
        this.states[i] = false;
      }
      
      console.log('EMERGENCY STOP: All relays turned OFF');
      return true;
    } catch (error) {
      console.error('Emergency stop failed:', error);
      throw error;
    }
  }
}

module.exports = ModbusRelayController;