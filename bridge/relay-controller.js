const MockRelayController = require('./mock-relay');
const ModbusRelayController = require('./modbus-relay');

// Factory to create appropriate relay controller
function createRelayController(config = {}) {
  const {
    mode = process.env.RELAY_MODE || 'mock',
    ip = process.env.RELAY_IP || '**************',
    port = process.env.RELAY_PORT || 502
  } = config;

  if (mode === 'modbus') {
    console.log(`Creating Modbus relay controller for ${ip}:${port}`);
    return new ModbusRelayController(ip, parseInt(port));
  } else {
    console.log('Creating Mock relay controller');
    return new MockRelayController();
  }
}

module.exports = { createRelayController };