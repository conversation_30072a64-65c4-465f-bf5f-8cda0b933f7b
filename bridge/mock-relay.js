// Mock relay controller for testing without hardware
class MockRelayController {
  constructor() {
    this.states = {};
    this.connected = false;
    
    // Initialize all 30 bays to ON
    for (let i = 1; i <= 30; i++) {
      this.states[i] = true;
    }
  }

  async connect() {
    console.log('[MOCK] Relay controller connected');
    this.connected = true;
    return true;
  }

  async disconnect() {
    console.log('[MOCK] Relay controller disconnected');
    this.connected = false;
  }

  async setPower(bayNumber, state) {
    console.log(`[MOCK] Setting bay ${bayNumber} to ${state ? 'ON' : 'OFF'}`);
    this.states[bayNumber] = state;
    return true;
  }

  async readAllStates() {
    console.log('[MOCK] Reading all relay states');
    return this.states;
  }

  getState(bayNumber) {
    return this.states[bayNumber];
  }

  getAllStates() {
    return { ...this.states };
  }

  async powerCyclePattern(bayNumber, pattern = ['off', 'on', 'off', 'on']) {
    const delays = [3000, 3000, 3000, 5000];
    
    console.log(`[MOCK] Starting power cycle pattern for bay ${bayNumber}`);
    for (let i = 0; i < pattern.length; i++) {
      const state = pattern[i] === 'on';
      await this.setPower(bayNumber, state);
      
      if (i < pattern.length - 1) {
        await new Promise(resolve => setTimeout(resolve, delays[i]));
      }
    }
    console.log(`[MOCK] Power cycle pattern completed for bay ${bayNumber}`);
  }

  async setMultipleBays(bayStates) {
    console.log('[MOCK] Setting multiple bays:', bayStates);
    for (const [bay, state] of Object.entries(bayStates)) {
      this.states[parseInt(bay)] = state;
    }
  }

  async emergencyStop() {
    console.log('[MOCK] EMERGENCY STOP: All relays turned OFF');
    for (let i = 1; i <= 30; i++) {
      this.states[i] = false;
    }
    return true;
  }
}

module.exports = MockRelayController;